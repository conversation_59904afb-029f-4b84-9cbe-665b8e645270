#####################################
# Flutter/PlatformView 基础混淆保留
#####################################
-keep class io.flutter.** { *; }
-dontwarn io.flutter.**

-keepclassmembers class * {
    public <init>(...);
}
-keepclassmembers class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
}

#####################################
# ByteDance / DJX / NOV / GroMore SDK
#####################################
# 保留 nov sdk 主接口和模型类
-keep class com.bytedance.sdk.nov.** { *; }
-keep interface com.bytedance.sdk.nov.api.iface.** { *; }



# 保留R下面的资源
-keep class **.R$* {*;}
-keepattributes InnerClasses
-keep class **.R
-keep class **.R$* {
    <fields>;
}

# 保留 DJX、GroMore、聚合逻辑、短剧容器等
-keep class com.bytedance.** { *; }
-keep class com.ss.** { *; }
-keep class bykvm*.** { *; }

# 保留所有可能被反射的混淆类（如 kcgk 是内部生成的包）
-keep class kcgk.** { *; }
-keep class **.kcgk.** { *; }

# 防止构建失败
-dontwarn com.bytedance.**
-dontwarn kcgk.**

#####################################
# 广告 SDK 支持（按需启用）
#####################################

# GroMore Adapter
-keep class com.bytedance.msdk.adapter.** { public *; }
-keep class com.bytedance.msdk.api.** { public *; }
-keep class com.bytedance.msdk.base.TTBaseAd { *; }
-keep class com.bytedance.msdk.adapter.TTAbsAdLoaderAdapter {
    public *;
    protected <fields>;
}

# 百度广告 SDK（可选）
-ignorewarnings
-dontwarn com.baidu.mobads.sdk.api.**
-keep class com.baidu.mobads.** { *; }
-keep class com.style.widget.** { *; }
-keep class com.component.** { *; }
-keep class com.baidu.ad.magic.flute.** { *; }
-keep class com.baidu.mobstat.forbes.** { *; }

# 快手广告 SDK（可选）
-dontwarn com.kwai.**
-dontwarn com.kwad.**
-dontwarn com.ksad.**
-keep class org.chromium.** { *; }
-keep class aegon.chrome.** { *; }
-keep class com.kwai.** { *; }

# AdMob（可选）
-keep class com.google.android.gms.ads.MobileAds { public *; }

# Sigmob（可选）
-dontwarn com.sigmob.**
-keep class com.sigmob.**.** { *; }

# OAID 支持（可选）
-dontwarn com.bun.**
-keep class com.bun.** { *; }
-keep class a.** { *; }
-keep class XI.** { *; }
-keep class com.asus.msa.SupplementaryDID.** { *; }
-keep class com.zui.opendeviceidlibrary.** { *; }
-keep class com.huawei.hms.ads.identifier.** { *; }
-keep class com.samsung.android.deviceidservice.** { *; }
-keep class org.json.** { *; }

# 游可赢 Klevin（可选）
-keep class com.tencent.tgpa.** { *; }
-keep class com.tencent.klevin.** { *; }

# Mintegral SDK（可选）
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.mbridge.** { *; }
-keep interface com.mbridge.** { *; }
-dontwarn com.mbridge.**
-keep class **.R$* { public static final int mbridge*; }

#####################################
# 反射、JS接口（WebView）等
#####################################
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}
