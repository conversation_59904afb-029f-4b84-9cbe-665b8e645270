plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.meteorsnow"
    compileSdk = 35
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.meteorsnow.video"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24
        targetSdk = 33
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        ndk {
            abiFilters 'arm64-v8a'
        }
    }

    signingConfigs {
        release {
            v1SigningEnabled true
            v2SigningEnabled true
            keyAlias 'ms-video'
            keyPassword 'meteorsnow'
            storePassword 'meteorsnow'
            storeFile file('../sign_key/ms-video.jks')
        }
    }

    buildTypes {
        release {
            minifyEnabled false
//            zipAlignEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            signingConfig signingConfigs.release
        }
    }

    /// 自定义APK安装包名
    android.applicationVariants.all { variant ->
        variant.outputs.all {
            def createTime = new Date().format("YYYYMMddHHmm", TimeZone.getTimeZone("GMT+08:00"))
            if (variant.buildType.name == 'release') {
                outputFileName = "ms-video_${versionName}_build_${versionCode}_${createTime}_${buildType.name}.apk"
            }
        }
    }
}

flutter {
    source = "../.."
}
