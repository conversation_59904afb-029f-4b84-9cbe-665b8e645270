package com.meteorsnow;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;

import com.meteorsnow.ForegroundService;

public class MainActivity extends FlutterFragmentActivity {

    private static final String CHANNEL = "com.meteorsnow/method";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 如果需要在 onCreate 做其它初始化，可以加这里
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        GeneratedPluginRegistrant.registerWith(flutterEngine);

        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    switch (call.method) {
                        case "setEarpiece":
                            result.success(1);
                            break;
                        case "setSpeaker":
                            result.success(1);
                            break;
                        case "startService":
                            String token = call.argument("token"); // 从 Dart 传入参数
                            toStartService(token);
                            result.success(1);
                            break;
                        default:
                            result.notImplemented();
                            break;
                    }
                });
    }

    /**
     * 启动前台服务
     */
    private void toStartService(@Nullable String token) {
        if (!ForegroundService.serviceIsLive) {
            Intent intent = new Intent(this, ForegroundService.class);
            intent.putExtra("token", token);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent);
            } else {
                startService(intent);
            }
        }
    }
}
