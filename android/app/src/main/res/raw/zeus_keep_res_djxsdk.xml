<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools"
    tools:keep="
@attr/layout_constraintEnd_toStartOf,
@attr/layout_constraintTop_toBottomOf,
@attr/layout_constraintEnd_toEndOf,
@anim/djx_anim_comment_out,
@attr/layout_constraintLeft_toLeftOf,
@style/djxAuthor2Activity,
@attr/layout_constraintBottom_toBottomOf,
@style/djx_news_dislike_dialog,
@attr/layout_constraintStart_toStartOf,
@style/djx_report_link_guide_dialog_style,
@anim/djx_anim_comment_in,
@color/white,
@color/black,
@attr/layout_constraintTop_toTopOf,
@style/TextAppearance.AppCompat.Medium,
@attr/layout_constraintStart_toEndOf,
@attr/layout_goneMarginLeft,
@anim/djx_anim_no_anim,
@style/djxAppNoTitle,
@style/djx_privacy_dialog_style,
@anim/djx_anim_right_in,
@style/djx_animation_share_style,
@style/djxNewsDetailActivity,
@anim/djx_anim_right_out,
@style/djx_fav_invalid_dialog_style,
@style/djx_dialog_no_title,
@style/djx_dislike_dialog_style,
@style/djx_draw_share_dialog_style,
@color/djx_draw_author_activity_bg,
@style/djxAppFull,
@attr/layout_constraintRight_toRightOf,
@attr/layout_constraintBottom_toTopOf,
@raw/zeus_keep_res_djxsdk,
@id/text_tip
" />