{"configVersion": 2, "packages": [{"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/develop/FlutterSdk/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-1.0.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/lints-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/develop/FlutterSdk/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "flutter_gromore_ads", "rootUri": "../", "packageUri": "lib/", "languageVersion": "2.12"}], "generated": "2025-08-25T16:19:06.050816Z", "generator": "pub", "generatorVersion": "3.5.4", "flutterRoot": "file:///Users/<USER>/develop/FlutterSdk/flutter", "flutterVersion": "3.24.5", "pubCache": "file:///Users/<USER>/.pub-cache"}