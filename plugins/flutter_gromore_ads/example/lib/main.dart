import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

import 'ads_config.dart';
import 'pages/home_page.dart';

void main() {
  // 绑定引擎
  WidgetsFlutterBinding.ensureInitialized();
  setAdEvent();
  init().then((value) {
    if (value) {
      FlutterGromoreAds.showSplashAd(
        AdsConfig.splashId,
        logo: AdsConfig.logo,
        timeout: 3.5,
      );
    }
  });

  // 启动
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Banner(
        message: 'Pro',
        location: BannerLocation.topEnd,
        child: HomePage(),
      ),
    );
  }
}

/// 初始化广告 SDK
Future<bool> init() async {
  bool result = await FlutterGromoreAds.initAd(
    AdsConfig.appId,
    themeStatus: 0,
  );
  debugPrint("广告SDK 初始化${result ? '成功' : '失败'}");
  return result;
}

/// 设置广告监听
Future<void> setAdEvent() async {
  FlutterGromoreAds.onEventListener((event) {
    debugPrint('onEventListener adId:${event.adId} action:${event.action}');
    if (event is AdErrorEvent) {
      // 错误事件
      debugPrint(
          'onEventListener errCode:${event.errCode} errMsg:${event.errMsg}');
    } else if (event is AdRewardEvent) {
      // 激励事件
      debugPrint(
          'onEventListener rewardType:${event.rewardType} rewardVerify:${event.rewardVerify} rewardAmount:${event.rewardAmount} rewardName:${event.rewardName} errCode:${event.errCode} errMsg:${event.errMsg} customData:${event.customData} userId:${event.userId}');
    } else if (event is AdEcpmEvent) {
      // 广告价格信息事件
      debugPrint(
          'onEventListener ecpm:${event.ecpm} channel:${event.channel} subChannel:${event.subChannel} sdkName:${event.sdkName} scenarioId:${event.scenarioId} errMsg:${event.errMsg}');
      // 添加记录
      AdsConfig.addEcpmEvent(event);
    } else if (event.action == AdEventAction.onAdLoaded) {
      // 广告加载成功
      // 判断是开屏代码位 id，如果是预加载则展示, preload: true 一定要传
      if (event.adId == AdsConfig.splashId) {
        // FlutterGromoreAds.showSplashAd(
        //   AdsConfig.splashId,
        //   logo: AdsConfig.logo,
        //   preload: true,
        // );
        // 已缓存广告
        AdsConfig.splashAdPreload = true;
      }
    } else if (event.action == AdEventAction.onAdClosed) {
      /// 这里写关闭的逻辑
      if (event.adId == AdsConfig.splashId) {
        // 未缓存广告
        AdsConfig.splashAdPreload = false;
      }
    } else {
      if (event.action == AdEventAction.onAdClicked) {
        debugPrint('onEventListener onAdClicked 广告点击了');
        AdsConfig.isShowHotStartAd = false;
        // 更新广告点击时间
        AdsConfig.updateAdClickTime(event);
      }
    }
  });
}
