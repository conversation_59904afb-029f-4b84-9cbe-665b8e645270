import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:flutter_gromore_ads_example/pages/ecmp_page.dart';
import '../ads_config.dart';
import '../router/router.dart';
import '../theme/style.dart';
import 'banner_page.dart';
import 'feed2_page.dart';
import 'feed3_page.dart';
import 'feed_page.dart';

import 'fullscreen_video_page.dart';
import 'reward_video_page.dart';
import 'splash_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FlutterAds GroMore Pro 版'),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Center(
            child: Column(
              children: [
                InkWell(
                  child: Image.asset(
                    'images/gromore_pro.png',
                    width: double.maxFinite,
                  ),
                  onTap: () => pushProPage(context),
                ),
                kDivider,
                ListTile(
                  dense: true,
                  title: const Text('🚀 GroMore Pro 版'),
                  onTap: () => pushProPage(context),
                ),
                kDivider,
                ListTile(
                  dense: true,
                  title: const Text('📢 请求应用跟踪透明授权(iOS)'),
                  onTap: () => requestIDFA(),
                ),
                kDivider,
                ListTile(
                  dense: true,
                  title: const Text('📱 请求相关权限（Android）'),
                  onTap: () => requestPermissionIfNecessary(),
                ),
                kDivider,
                ListTile(
                  dense: true,
                  title: const Text('💰 收益面板'),
                  subtitle: const Text('仅 Pro 版支持'),
                  onTap: () => pushPage(context, const EcpmPage()),
                ),
                kDivider,
                ListTile(
                  dense: true,
                  title: const Text('🐛 可视化测试工具'),
                  subtitle: const Text('仅 Pro 版支持'),
                  onTap: () => FlutterGromoreAds.launchTestTools(),
                ),
                kDivider,
                ListTile(
                  title: const Text('⬇️ 预缓存'),
                  subtitle: const Text('仅 Pro 版支持（插屏、激励视频）'),
                  onTap: () => preload(
                    rewardPosids: [
                      AdsConfig.rewardVideoId,
                      AdsConfig.rewardVideoId2
                    ],
                    insertPosids: [
                      AdsConfig.interstitialIdHalf,
                      AdsConfig.interstitialId,
                      AdsConfig.interstitialIdHorizontal
                    ],
                  ),
                ),
                kDivider,
                ListTile(
                  title: const Text('开屏广告'),
                  onTap: () => pushPage(context, const SplashPage()),
                ),
                kDivider,
                ListTile(
                  title: const Text('新插屏广告'),
                  onTap: () => pushPage(context, const FullScreenVideoPage()),
                ),
                kDivider,
                ListTile(
                  title: const Text('Banner 广告'),
                  onTap: () => pushPage(context, const BannerPage()),
                ),
                kDivider,
                ListTile(
                  title: const Text('激励视频广告'),
                  subtitle: const Text('仅 Pro 版支持'),
                  onTap: () => pushPage(context, const RewardVideoPage()),
                ),
                kDivider,
                ListTile(
                  title: const Text('信息流（单个）'),
                  subtitle: const Text('仅 Pro 版支持'),
                  onTap: () => pushPage(context, const Feed2Page()),
                ),
                kDivider,
                ListTile(
                  title: const Text('信息流（列表）'),
                  subtitle: const Text('仅 Pro 版支持'),
                  onTap: () => pushPage(context, const FeedPage()),
                ),
                kDivider,
                ListTile(
                  title: const Text('信息流（轮播）'),
                  subtitle: const Text('仅 Pro 版支持'),
                  onTap: () => pushPage(context, const Feed3Page()),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 请求应用跟踪透明度授权
  Future<void> requestIDFA() async {
    bool result = await FlutterGromoreAds.requestIDFA;
    print('请求广告标识符:$result');
  }

  /// 请求应用跟踪透明度授权
  Future<void> requestPermissionIfNecessary() async {
    bool result = await FlutterGromoreAds.requestPermissionIfNecessary;
    print('请求相关权限:$result');
  }

  /// 预加载广告
  /// [rewardPosids] 激励视频广告位 id 列表
  /// [insertPosids] 插屏广告位 id 列表
  Future<void> preload(
      {List<String> rewardPosids = const [],
      List<String> insertPosids = const []}) async {
    bool result = await FlutterGromoreAds.preload(
      rewardPosids: rewardPosids,
      insertPosids: insertPosids,
      customData: 'customData',
      userId: 'userId',
    );
    print("预加载：${result ? '成功' : '失败'}");
  }

  // 定义时间间隔
  int interval = 30 * 1000; // 10秒
  int currentTime = 0;
  bool isShow = false;

  /// 请求开屏广告
  Future<void> requestSplashAd() async {
    bool result = await FlutterGromoreAds.showSplashAd(
      AdsConfig.splashId,
      preload: true,
    );
    print("HomePage 请求开屏广告：${result ? '成功' : '失败'}");
  }

  /// 监听 App 生命周期
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print("HomePage didChangeAppLifecycleState:${state.toString()}");
    if (state == AppLifecycleState.paused) {
      print("HomePageApp 进入后台");
      if (!AdsConfig.splashAdPreload) {
        // 加载广告
        requestSplashAd();
      }
    } else if (state == AppLifecycleState.resumed) {
      print("HomePage App 从后台返回");
      int diffTime = DateTime.now().millisecondsSinceEpoch - currentTime;
      print("HomePage diffTime:${diffTime}");
      if (diffTime > interval && AdsConfig.isShowHotStartAd) {
        currentTime = DateTime.now().millisecondsSinceEpoch;
        // 展示广告
        if (AdsConfig.splashAdPreload) {
          requestSplashAd();
        } else {
          print("HomePage 未缓存广告");
        }
      } else {
        print("HomePage 未到时间");
        AdsConfig.isShowHotStartAd = true;
      }
    }
  }
}
