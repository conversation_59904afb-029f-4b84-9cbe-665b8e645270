import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/event/ad_ecpm_event.dart';
import 'package:flutter_gromore_ads_example/ads_config.dart';
import 'package:flutter_gromore_ads_example/router/router.dart';
import '../widgets/widgets.dart';

// 收益页面
class EcpmPage extends StatefulWidget {
  const EcpmPage({Key? key}) : super(key: key);

  @override
  _EcpmPageState createState() => _EcpmPageState();
}

class _EcpmPageState extends State<EcpmPage> {
  // 总收益
  double total = 0000;

  @override
  void initState() {
    // 计算总收益
    for (var element in AdsConfig.ecpmEvents) {
      total += double.parse(element.ecpm);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: buildAppBar(context, '收益面板'),
        body: Column(
          children: [
            Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('🎉 总收益：'),
                  Text(
                    total.toStringAsFixed(2),
                    style: const TextStyle(
                      fontSize: 28,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              height: 66,
              alignment: Alignment.center,
            ),
            const Divider(),
            Flexible(
              child: ListView.separated(
                itemBuilder: (context, index) {
                  AdEcpmEvent event =
                      AdsConfig.ecpmEvents.reversed.toList()[index];
                  return ListTile(
                    title: Text(
                      '收益：${event.ecpm}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                        '厂商：${event.sdkName} 广告位：${event.adId} \n展示时间：${event.showTime} \n点击时间：${event.clickTime ?? '未点击'}'),
                    onTap: () {},
                  );
                },
                itemCount: AdsConfig.ecpmEvents.length,
                separatorBuilder: (BuildContext context, int index) {
                  return const Divider(height: 0.5, color: Colors.black12);
                },
              ),
            ),
          ],
        ));
  }
}

class EcpmFloatBtn extends StatelessWidget {
  const EcpmFloatBtn({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        pushPage(context, const EcpmPage());
      },
      child: const Icon(
        Icons.attach_money,
        color: Colors.red,
      ),
    );
  }
}
