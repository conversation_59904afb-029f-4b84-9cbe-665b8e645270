import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

import '../ads_config.dart';
import '../widgets/widgets.dart';
import 'ecmp_page.dart';

/// 信息流页面，我想使用 ViewPager 来实现页面轮播并且顶部有一个广告 View
class Feed3Page extends StatefulWidget {
  const Feed3Page({Key? key}) : super(key: key);

  @override
  _Feed3PageState createState() => _Feed3PageState();
}

class _Feed3PageState extends State<Feed3Page> {
  List<int> adIds = []; // 存储多个广告ID
  int currentPage = 0; // 跟踪当前页面
  int currentAdIndex = 0; // 新增：记录当前广告索引
  int lastPage = 0; // 新增：记录上一次的页面位置
  bool isLoadingMoreAds = false; // 新增：标记是否正在加载更多广告
  final PageController _pageController = PageController();

  double adWidth = 375;
  double adHeight = 156;

  @override
  void initState() {
    super.initState();
    // Using MediaQuery to get screen size
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final size = MediaQuery.of(context).size;
      double width = size.width;
      double height = width / 3.0;
      debugPrint("Feed3Page width:${width} height:${height}");
      adWidth = width;
      adHeight = height;
      getFeedAdList();
    });
    _pageController.addListener(_onPageChange);
  }

  @override
  void dispose() {
    _pageController.removeListener(_onPageChange);
    clearFeedAds();
    super.dispose();
  }

  void _onPageChange() {
    final page = _pageController.page?.round() ?? 0;
    if (page != currentPage) {
      final isForward = page > lastPage;
      if (isForward) {
        lastPage = currentPage;
        currentPage = page;
        currentAdIndex = currentPage ~/ 3 % adIds.length;
        setState(() {});
        // 当展示到最后一条广告时，加载更多广告
        if (currentAdIndex == adIds.length - 1 && !isLoadingMoreAds) {
          _loadMoreAds();
        }
      } else {
        currentPage = page;
        setState(() {});
      }
    }
  }

  // 新增：加载更多广告的方法
  Future<void> _loadMoreAds() async {
    if (isLoadingMoreAds) return;

    try {
      isLoadingMoreAds = true;
      List<int> newAdIds = await FlutterGromoreAds.loadFeedAd(
        AdsConfig.feedId,
        width: adWidth.toInt(),
        height: adHeight.toInt(),
        count: 3,
      );

      setState(() {
        adIds.addAll(newAdIds);
      });
    } catch (e) {
      print('Load more ads failed: ${e.toString()}');
    } finally {
      isLoadingMoreAds = false;
    }
  }

  // 修改广告获取逻辑
  int? _getAdIdForPage(int page) {
    if (adIds.isEmpty) return null;
    return adIds[currentAdIndex];
  }

  Widget _buildAdWidget() {
    int? currentAdId = _getAdIdForPage(currentPage);

    if (adIds.isEmpty) {
      return TextButton(
        child: const Text('广告加载中...'),
        onPressed: () {
          getFeedAdList();
        },
      );
    }
    if (currentAdId == null) {
      return const Text('广告加载中...');
    }
    return ClipRect(
      child: Align(
        alignment: Alignment.center,
        heightFactor: 0.76,
        child: AdFeedWidget(
          key: ValueKey('feed_ad_$currentAdId'),
          posId: '$currentAdId',
          width: adWidth,
          height: adHeight,
          show: true,
        ),
      ),
    );
  }

  // 加载信息流广告
  Future<void> getFeedAdList() async {
    try {
      List<int> adResultList = await FlutterGromoreAds.loadFeedAd(
        AdsConfig.feedId,
        width: adWidth.toInt(),
        height: adHeight.toInt(),
        count: 3,
      );
      setState(() {
        adIds = adResultList;
      });
    } catch (e) {
      print(e.toString());
      setState(() {
        adIds = [];
      });
    }
  }

  // 清除信息流广告
  Future<void> clearFeedAds() async {
    if (adIds.isNotEmpty) {
      bool result = await FlutterGromoreAds.clearFeedAd(adIds);
      print('clearFeedAd:$result');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(context, '信息流广告'),
      body: Column(
        children: [
          Container(
            child: Center(
              child: _buildAdWidget(),
            ),
          ),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemBuilder: (context, index) {
                return Container(
                  color: Colors.blue,
                  alignment: Alignment.center,
                  child: Text('Page ${index + 1}'),
                );
              },
              itemCount: 50,
            ),
          ),
        ],
      ),
      floatingActionButton: const EcpmFloatBtn(),
    );
  }
}
