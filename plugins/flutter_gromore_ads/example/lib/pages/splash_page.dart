import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

import '../ads_config.dart';
import '../theme/style.dart';
import '../widgets/widgets.dart';
import 'ecmp_page.dart';

// 开屏
class SplashPage extends StatefulWidget {
  const SplashPage({Key? key}) : super(key: key);

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(context, '开屏广告'),
      body: Column(
        children: [
          ListTile(
            title: const Text('开屏（全屏）'),
            onTap: () => showSplashAd(),
          ),
          kDivider,
          ListTile(
            title: const Text('开屏-Logo1'),
            onTap: () => showSplashAd(AdsConfig.logo),
          ),
          kDivider,
          ListTile(
            title: const Text('开屏-Logo2'),
            onTap: () => showSplashAd(AdsConfig.logo2),
          ),
          kDivider,
          ListTile(
            title: const Text('开屏-预加载(Pro)'),
            subtitle: const Text('仅 Pro 版支持'),
            onTap: () => showSplashAd(AdsConfig.logo2, true),
          ),
          kDivider,
          ListTile(
            title: const Text('开屏-展示预加载(Pro)'),
            subtitle: const Text('仅 Pro 版支持'),
            onTap: () => showSplashAd(AdsConfig.logo2, true),
          ),
          kDivider,
        ],
      ),
      floatingActionButton: const EcpmFloatBtn(),
    );
  }

  /// 展示开屏广告
  /// [logo] 展示如果传递则展示logo，不传递不展示
  /// [preload] 是否仅预加载不展示，true：仅预加载 false：加载并展示
  Future<void> showSplashAd([String? logo, bool preload = false]) async {
    bool result = await FlutterGromoreAds.showSplashAd(
      AdsConfig.splashId,
      logo: logo,
      timeout: 3.5,
      preload: preload,
    );
    print("展示开屏广告${result ? '成功' : '失败'}");
  }
}
