import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:flutter_gromore_ads_example/theme/style.dart';

import '../ads_config.dart';
import '../widgets/widgets.dart';
import 'ecmp_page.dart';

class BannerPage extends StatefulWidget {
  const BannerPage({Key? key}) : super(key: key);

  @override
  _BannerPageState createState() => _BannerPageState();
}

class _BannerPageState extends State<BannerPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(context, 'Banner广告'),
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            kSizedBox10,
            AdBannerWidget(
              posId: AdsConfig.bannerId,
              width: 300,
              height: 125,
            ),
            kSizedBox10,
            AdBannerWidget(
              posId: AdsConfig.bannerId,
              width: 300,
              height: 125,
            ),
            kSizedBox10,
            AdBannerWidget(
              posId: AdsConfig.bannerId,
              width: 300,
              height: 125,
            ),
          ],
        ),
      ),
      floatingActionButton: const EcpmFloatBtn(),
    );
  }
}
