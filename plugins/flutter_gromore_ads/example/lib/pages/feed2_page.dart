import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

import '../ads_config.dart';
import '../widgets/widgets.dart';
import 'ecmp_page.dart';

/// 信息流页面
class Feed2Page extends StatefulWidget {
  const Feed2Page({Key? key}) : super(key: key);

  @override
  _Feed2PageState createState() => _Feed2PageState();
}

class _Feed2PageState extends State<Feed2Page> {
  int adId = 0;

  @override
  void initState() {
    getFeedAdList();
    super.initState();
  }

  @override
  void dispose() {
    clearFeedAd();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(context, '信息流广告'),
      body: Center(
        child: Column(
          children: [
            const SizedBox(height: 50),
            if (adId == -1)
              TextButton(
                child: const Text('广告加载失败，请重试'),
                onPressed: () {
                  getFeedAdList();
                },
              ),
            if (adId == 0) const Text('广告加载中'),
            if (adId != 0)
              AdFeedWidget(
                key: ValueKey('feed_ad_$adId'),
                posId: '$adId',
                width: 300,
                height: 125,
                show: true,
              ),
          ],
        ),
      ),
      floatingActionButton: const EcpmFloatBtn(),
    );
  }

  // 加载信息流广告
  Future<void> getFeedAdList() async {
    try {
      List<int> adResultList = await FlutterGromoreAds.loadFeedAd(
        AdsConfig.feedId,
        width: 300,
        height: 125,
        count: 3,
      );
      if (adResultList.isNotEmpty) {
        adId = adResultList.first;
      } else {
        adId = -1;
      }
    } catch (e) {
      print(e.toString());
      adId = -1;
    }

    setState(() {});
  }

  // 清除信息流广告
  Future<void> clearFeedAd() async {
    bool result = await FlutterGromoreAds.clearFeedAd([adId]);
    print('clearFeedAd:$result');
  }
}
