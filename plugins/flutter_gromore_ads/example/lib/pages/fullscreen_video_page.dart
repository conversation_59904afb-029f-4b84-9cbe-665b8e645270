import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

import '../ads_config.dart';
import '../theme/style.dart';
import '../widgets/widgets.dart';
import 'ecmp_page.dart';

// 全屏视频
class FullScreenVideoPage extends StatefulWidget {
  const FullScreenVideoPage({Key? key}) : super(key: key);

  @override
  _FullScreenVideoPageState createState() => _FullScreenVideoPageState();
}

class _FullScreenVideoPageState extends State<FullScreenVideoPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(context, '插屏广告'),
      body: Column(
        children: [
          ListTile(
            title: const Text('新插屏-半屏'),
            onTap: () => showFullScreenVideoAd(AdsConfig.interstitialIdHalf),
          ),
          kDivider,
          ListTile(
            title: const Text('新插屏-全屏'),
            onTap: () =>
                showFullScreenVideoAd(AdsConfig.interstitialIdHorizontal),
          ),
          kDivider,
          ListTile(
            title: const Text('新插屏-横屏'),
            onTap: () => showFullScreenVideoAd(AdsConfig.interstitialId),
          ),
          kDivider,
        ],
      ),
      floatingActionButton: const EcpmFloatBtn(),
    );
  }

  /// 展示全屏视频、新插屏广告
  /// [posId] 广告位id
  Future<void> showFullScreenVideoAd(String posId) async {
    bool result = await FlutterGromoreAds.showInterstitialAd(posId);
    print("展示插屏广告${result ? '成功' : '失败'}");
  }
}
