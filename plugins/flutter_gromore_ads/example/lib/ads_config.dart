import 'dart:io';

import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

/// 广告配置信息
class AdsConfig {
  // 开屏广告是否缓存
  static bool splashAdPreload = false;
  // 是否显示热启动广告
  static bool isShowHotStartAd = true;

  /// 获取 Logo 资源名称
  static String get logo {
    if (Platform.isAndroid) {
      return 'flutterads_logo';
    } else {
      return 'LaunchImage';
    }
  }

  /// 获取 Logo 资源名称 2
  static String get logo2 {
    if (Platform.isAndroid) {
      return 'flutterads_logo2';
    } else {
      return 'LaunchImage2';
    }
  }

  /// 获取 App id
  static String get appId {
    return Platform.isAndroid ? '5550178' : '5463850';
  }

  /// 获取 App Config
  static String get config {
    if (Platform.isIOS) {
      return 'ios_config_5209496';
    }
    return 'android_config_5216573.json';
  }

  /// 获取开屏广告位id
  static String get splashId {
    return Platform.isAndroid ? '102917532' : '102945032';
  }

  /// 获取插屏广告位id 竖屏
  static String get interstitialId =>
      Platform.isAndroid ? '102916794' : '102945606';

  /// 获取插屏广告位id 横屏
  static String get interstitialIdHorizontal =>
      Platform.isAndroid ? '102916794' : '102945606';

  /// 获取插屏广告位id 半屏
  static String get interstitialIdHalf =>
      Platform.isAndroid ? '102917431' : '102945607';

  /// 获取激励视频广告位id
  static String get rewardVideoId =>
      Platform.isAndroid ? '102917712' : '102620155';

  /// 获取激励视频广告位id-进阶
  static String get rewardVideoId2 =>
      Platform.isAndroid ? '102917712' : '102620155';

  /// 获取 Banner 广告位id
  static String get bannerId => Platform.isAndroid ? '102918478' : '102943984';

  /// 获取 Feed 信息流广告位 id
  static String get feedId => Platform.isAndroid ? '102917714' : '102943985';

  /// 收益面板记录
  static List<AdEcpmEvent> ecpmEvents = [];

  /// 添加收益记录
  static void addEcpmEvent(AdEcpmEvent event) {
    ecpmEvents.add(event);
  }

  /// 更新广告点击时间
  static void updateAdClickTime(AdEvent event) {
    // 从后往前遍历，取最新的广告设置点击时间
    for (var i = ecpmEvents.length - 1; i >= 0; i--) {
      if (ecpmEvents[i].adId == event.adId) {
        ecpmEvents[i].clickTime = DateTime.now();
        break;
      }
    }
  }
}
