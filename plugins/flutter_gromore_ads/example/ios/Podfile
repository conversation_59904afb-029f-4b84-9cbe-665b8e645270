# Uncomment this line to define a global platform for your project
platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
# ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  # use_frameworks!
  # use_modular_headers!
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  #1.GroMoreSDK核心库
  # 广点通/优量汇 https://github.com/CocoaPods/Specs/tree/master/Specs/a/9/a/GMGdtAdapter/
  pod 'GMGdtAdapter', '*********'
  pod 'GDTMobSDK', '4.15.10'
  # 快手 https://github.com/CocoaPods/Specs/tree/master/Specs/f/a/b/GMKsAdapter/
  pod 'GMKsAdapter', '********'
  pod 'KSAdSDK', '3.3.69' # 这里一定要使用 '********' 及以上版本，不然 iOS 隐私清单无法通过
  #=====> 这里是与当前版本对应的适配器版本
  # pod 'GMAdmobAdapter-Beta', '10.0.0.0'
  # pod 'GMBaiduAdapter-Beta', '5.370.0'
  # pod 'GMGdtAdapter-Beta', '4.15.00.0'
  # pod 'GMKlevinAdapter-Beta', '2.11.0.211.1'
  # pod 'GMKsAdapter-Beta', '3.3.67.0'
  # pod 'GMMintegralAdapter-Beta', '7.7.1.0'
  # pod 'GMSigmobAdapter-Beta', '4.15.3.0'
  # pod 'GMUnityAdapter-Beta', '4.3.0.0'
  #=====>

  # 百度SDK
  # pod 'CSJMBaiduAdapter', '5.300.0'
  # pod 'BaiduMobAdSDK', '5.300'
  # UnityAds
#  pod 'CSJMUnityAdapter', '4.3.0.0'
#  pod 'UnityAds', '4.3.0'
  # Admob/GoogleAd
#  pod 'CSJMAdmobAdapter', '10.0.0.0'
#  pod 'Google-Mobile-Ads-SDK', '10.0.0'
  # SigmobAd
#  pod 'CSJMSigmobAdapter', '4.8.0.0'
#  pod 'SigmobAd-iOS', '4.8.0'
  # MintegralAdSDK
#  pod 'CSJMMintegralAdapter', '7.3.6.0.0'
  # MintegralAdSDK 使用时请务必使用cocoapod源
  # pod 'MintegralAdSDK', '7.3.6.0', :subspecs => [
  # 'SplashAd',
  # 'InterstitialAd',
  # 'NewInterstitialAd',
  # 'InterstitialVideoAd',
  # 'RewardVideoAd',
  # 'NativeAd',
  # 'NativeAdvancedAd',
  # 'BannerAd',
  # 'BidNativeAd',
  # 'BidNewInterstitialAd',
  # 'BidInterstitialVideoAd',
  # 'BidRewardVideoAd'
  # ]
  #  pod 'CSJMKlevinAdapter', '2.11.0.211.1'
  #  pod 'KlevinAdSDK', '2.11.0.211'

end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
  end
end
