allprojects {
    repositories {
        google()
        mavenCentral()
        //本地⽂件仓库依赖
        flatDir {
            dirs 'libs'
        }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
// 配置 kotlin 版本
buildscript {
    ext.kotlin_version = '1.7.10'
}