plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.zero.flutter_gromore_ads_example"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.huahuacaocao.flowercare"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled = true
    }

    //签名信息
    signingConfigs {
        release {
            // 下面的 storePassword keyPassword 可以写死成自己的密码，也可以从环境变量中读取
            storeFile new File("${project.projectDir}/keystore/money.jks")
            storePassword System.getenv("HHCC_PWD")
            keyAlias 'plantmonitor'
            keyPassword System.getenv("HHCC_PWD")
        }
    }

    buildTypes {
        debug{
            ndk {
                abiFilters 'armeabi','armeabi-v7a','arm64-v8a'
            }
            // 跑示例 Debug 模式下，使用 debug 签名
            // signingConfig signingConfigs.debug
            signingConfig signingConfigs.release
        }
        release {
            ndk {
                abiFilters 'armeabi','armeabi-v7a','arm64-v8a'
            }
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            // 开启混淆
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'
    // 测试工具(这里仅 debug 模式下引用，正式项目用这种模式)
    // debugImplementation files('libs/tools-release.aar')
    implementation files('libs/tools-release.aar')
    //GroMore_sdk adapter
    implementation "com.pangle.cn:mediation-gdt-adapter:4.591.1461.2" //gdt adapter
    implementation files('libs/GDTSDK.unionNormal.4.591.1461.aar')// 广点通广告 SDK
    implementation "com.pangle.cn:mediation-ks-adapter:3.3.67.1.0"//ks adapter
    implementation files('libs/kssdk-ad-3.3.67.1.aar')// 快手广告 SDK
    // 其他厂商参考 https://www.csjplatform.com/union/media/union/download/detail?id=142&docId=27562&osType=android
    ///  =====> 这里是与当前版本对应的适配器版本
    // implementation "com.pangle.cn:mediation-ks-adapter:3.3.67.1.0"//ks adapter
    // implementation "com.pangle.cn:mediation-gdt-adapter:4.591.1461.1" //gdt adapter
    // implementation "com.pangle.cn:mediation-admob-adapter:17.2.0.62"//admob adapter
    // implementation "com.pangle.cn:mediation-baidu-adapter:9.37.0"//baidu adapter
    // implementation "com.pangle.cn:mediation-klevin-adapter:2.11.0.3.23"//游可赢 adapter
    // implementation "com.pangle.cn:mediation-mintegral-adapter:16.5.57.5"//mintegral adapter
    // implementation "com.pangle.cn:mediation-sigmob-adapter:4.19.4.0"//sigmob adapter
    // // wind-sdk 和 common版本必须匹配使用，不然可能存在兼容性问题
    // implementation "com.pangle.cn:mediation-unity-adapter:4.3.0.29"//unity adapter
    // =====>
}
