<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.zero.flutter_gromore_ads">

    <!-- 必要权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <!--必要权限，解决安全风险漏洞，发送和注册广播事件需要调用带有传递权限的接口-->
    <permission
        android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN"
        android:protectionLevel="signature" />
    <uses-permission android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN" />

    <application
        android:networkSecurityConfig="@xml/network_config"
        android:requestLegacyExternalStorage="true">
        <!-- 开屏广告页面 -->
        <activity
            android:name=".page.AdSplashActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme" />

        <!-- Pangle start================== -->
        <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="${applicationId}.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/pangle_file_paths" />
        </provider>
        <provider
            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
            android:authorities="${applicationId}.TTMultiProvider"
            android:exported="false" />
        <!-- Pangle end================== -->

    </application>
</manifest>
