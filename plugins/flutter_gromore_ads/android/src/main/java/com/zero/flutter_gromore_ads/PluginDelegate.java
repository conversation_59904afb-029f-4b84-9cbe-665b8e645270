package com.zero.flutter_gromore_ads;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.bytedance.mtesttools.api.TTMediationTestTool;
import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdConfig;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.bytedance.sdk.openadsdk.TTCustomController;
import com.bytedance.sdk.openadsdk.mediation.IMediationManager;
import com.bytedance.sdk.openadsdk.mediation.IMediationPreloadRequestInfo;
import com.bytedance.sdk.openadsdk.mediation.MediationConstant;
import com.bytedance.sdk.openadsdk.mediation.MediationPreloadRequestInfo;
import com.bytedance.sdk.openadsdk.mediation.ad.MediationAdSlot;
import com.bytedance.sdk.openadsdk.mediation.init.MediationConfig;
import com.bytedance.sdk.openadsdk.mediation.init.MediationPrivacyConfig;
import com.zero.flutter_gromore_ads.load.FeedAdLoad;
import com.zero.flutter_gromore_ads.load.FeedAdManager;
import com.zero.flutter_gromore_ads.load.SplashAdLoad;
import com.zero.flutter_gromore_ads.page.InterstitialPage;
import com.zero.flutter_gromore_ads.page.NativeViewFactory;
import com.zero.flutter_gromore_ads.page.RewardVideoPage;
import com.zero.flutter_gromore_ads.utils.FileUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import io.flutter.BuildConfig;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

/// 插件代理
public class PluginDelegate implements MethodChannel.MethodCallHandler, EventChannel.StreamHandler {
    private final String TAG = PluginDelegate.class.getSimpleName();
    // Flutter 插件绑定对象
    public FlutterPlugin.FlutterPluginBinding bind;
    // 当前 Activity
    public Activity activity;
    // 返回通道
    private MethodChannel.Result result;
    // 事件通道
    private EventChannel.EventSink eventSink;
    // 插件代理对象
    private static PluginDelegate _instance;

    public static PluginDelegate getInstance() {
        return _instance;
    }

    // Banner View
    public static final String KEY_BANNER_VIEW = "flutter_gromore_ads_banner";
    // Feed View
    public static final String KEY_FEED_VIEW = "flutter_gromore_ads_feed";
    // 广告参数
    public static final String KEY_POSID = "posId";
    // logo 参数
    public static final String KEY_LOGO = "logo";
    // timeout 参数
    public static final String KEY_TIMEOUT = "timeout";
    // 预加载
    public static final String PRELOAD = "preload";
    // 是否初始化
    public static boolean adInit = false;
    // 开屏广告加载对象
    public SplashAdLoad splashAdLoad;

    /**
     * 插件代理构造函数构造函数
     *
     * @param activity      Activity
     * @param pluginBinding FlutterPluginBinding
     */
    public PluginDelegate(Activity activity, FlutterPlugin.FlutterPluginBinding pluginBinding) {
        this.activity = activity;
        this.bind = pluginBinding;
        _instance = this;
    }

    /**
     * 方法通道调用
     *
     * @param call   方法调用对象
     * @param result 回调结果对象
     */
    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        String method = call.method;
        Log.d(TAG, "MethodChannel onMethodCall method:" + method + " arguments:" + call.arguments);
        if ("requestPermissionIfNecessary".equals(method)) {
            requestPermissionIfNecessary(call, result);
        } else if ("initAd".equals(method)) {
            initAd(call, result);
        } else if ("preload".equals(method)) {
            preload(call, result);
        } else if ("showSplashAd".equals(method)) {
            showSplashAd(call, result);
        } else if ("showInterstitialAd".equals(method)) {
            showInterstitialAd(call, result);
        } else if ("showRewardVideoAd".equals(method)) {
            showRewardVideoAd(call, result);
        } else if ("loadFeedAd".equals(method)) {
            loadFeedAd(call, result);
        } else if ("clearFeedAd".equals(method)) {
            clearFeedAd(call, result);
        } else if ("launchTestTools".equals(method)) {
            launchTestTools(call, result);
        } else {
            result.notImplemented();
        }
    }

    /**
     * 建立事件通道监听
     *
     * @param arguments 参数
     * @param events    事件回调对象
     */
    @Override
    public void onListen(Object arguments, EventChannel.EventSink events) {
        Log.d(TAG, "EventChannel onListen arguments:" + arguments);
        eventSink = events;
    }

    /**
     * 取消事件通道监听
     *
     * @param arguments 参数
     */
    @Override
    public void onCancel(Object arguments) {
        Log.d(TAG, "EventChannel onCancel");
        eventSink = null;
    }

    /**
     * 添加事件
     *
     * @param event 事件
     */
    public void addEvent(Object event) {
        if (eventSink != null) {
            Log.d(TAG, "EventChannel addEvent event:" + event.toString());
            eventSink.success(event);
        }
    }

    /**
     * 展示 Banner 广告
     */
    public void registerBannerView() {
        bind.getPlatformViewRegistry()
                .registerViewFactory(KEY_BANNER_VIEW, new NativeViewFactory(KEY_BANNER_VIEW, this));
    }

    /**
     * 展示 Feed 信息流广告
     */
    public void registerFeedView() {
        bind.getPlatformViewRegistry()
                .registerViewFactory(KEY_FEED_VIEW, new NativeViewFactory(KEY_FEED_VIEW, this));
    }

    /**
     * 请求权限
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestPermissionIfNecessary(MethodCall call, MethodChannel.Result result) {
        TTAdSdk.getMediationManager().requestPermissionIfNecessary(activity);
        result.success(true);
    }

    /**
     * 初始化广告
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void initAd(MethodCall call, final MethodChannel.Result result) {
        String appId = call.argument("appId");
        String config = call.argument("config");
        Boolean useMediation = call.argument("useMediation");
        int limitPersonalAds = call.argument("limitPersonalAds");
        int themeStatus = call.argument("themeStatus");

        // 构建基础配置
        TTAdConfig.Builder configBuilder = new TTAdConfig.Builder()
                .appId(appId)
                .useMediation(useMediation)
                .debug(BuildConfig.RELEASE)
                .supportMultiProcess(false)
                .themeStatus(themeStatus);
        // 离线配置
        JSONObject localConfigJson = null;
        if (!TextUtils.isEmpty(config)) {
            String localConfigStr = FileUtils.getJson(config, activity);
            try {
                localConfigJson = new JSONObject(localConfigStr);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        // 构建配置
        TTAdConfig gmPangleOption;
        if (localConfigJson != null) {
            gmPangleOption = configBuilder
                    .customController(getTTCustomController(limitPersonalAds == 0))
                    .setMediationConfig(new MediationConfig.Builder()
                            .setCustomLocalConfig(localConfigJson)
                            .build()).build();
        } else {
            gmPangleOption = configBuilder
                    .customController(getTTCustomController(limitPersonalAds == 0))
                    .build();
        }
        // 初始化 SDK
        TTAdSdk.init(activity.getApplicationContext(), gmPangleOption);
        TTAdSdk.start(new TTAdSdk.Callback() {
            @Override
            public void success() {
                result.success(TTAdSdk.isSdkReady());
                adInit=true;
            }

            @Override
            public void fail(int i, String s) {
                result.success(false);
                Log.e(TAG, "TTAdSdk init start Error code:" + i + " msg:" + s);
                adInit=false;
            }
        });
    }

    /**
     * 预加载
     *
     * @param call
     * @param result
     */
    public void preload(MethodCall call, final MethodChannel.Result result) {
        // 判断是否为空
        if (activity == null) {
            Log.e(TAG,"Activity is null");
            result.success(false);
            return;
        }
        // 判断是否初始化成功
        if (!adInit) {
            Log.e(TAG,"SDK is not initialized successfully");
            result.success(false);
            return;
        }

        List<String> rewardPosids = call.argument("rewardPosids");
        List<String> insertPosids = call.argument("insertPosids");
        int concurrent = call.argument("concurrent");
        int interval = call.argument("interval");
        List<IMediationPreloadRequestInfo> requestInfoList = new ArrayList<>();
        if (rewardPosids != null && !rewardPosids.isEmpty()) {
            String customData = call.argument("customData");
            String userId = call.argument("userId");
            int orientation = call.argument("orientation");
            // 激励视频
            AdSlot adSlotReward = new AdSlot.Builder()
                    .setUserID(userId)//tag_id
                    .setOrientation(orientation)
                    .setMediationAdSlot(new MediationAdSlot
                            .Builder()
                            .setExtraObject(MediationConstant.CUSTOM_DATA_KEY_GROMORE_EXTRA, customData)
                            .build())
                    .build();
            IMediationPreloadRequestInfo preloadReward = new MediationPreloadRequestInfo(AdSlot.TYPE_REWARD_VIDEO, adSlotReward, rewardPosids);
            requestInfoList.add(preloadReward);
        }
        if (insertPosids != null && !insertPosids.isEmpty()) {
            // 插屏
            AdSlot adSlotInsert = new AdSlot.Builder().build();
            IMediationPreloadRequestInfo preloadInsert = new MediationPreloadRequestInfo(AdSlot.TYPE_FULL_SCREEN_VIDEO, adSlotInsert, insertPosids);
            requestInfoList.add(preloadInsert);
        }
        // 判断是否为空
        if (requestInfoList.isEmpty()) {
            result.success(false);
        } else {
            IMediationManager mediationManager = TTAdSdk.getMediationManager();
            if (mediationManager != null) {
                mediationManager.preload(activity, requestInfoList, concurrent, interval);
                result.success(true);
            } else {
                Log.e(TAG,"MediationManager is null");
                result.success(false);
            }
        }

    }


    private TTCustomController getTTCustomController(boolean limitPersonalAds) {
        return new TTCustomController() {

            @Override
            public boolean isCanUseWifiState() {
                return super.isCanUseWifiState();
            }

            @Override
            public String getMacAddress() {
                return super.getMacAddress();
            }

            @Override
            public boolean isCanUseWriteExternal() {
                return super.isCanUseWriteExternal();
            }

            @Override
            public String getDevOaid() {
                return super.getDevOaid();
            }

            @Override
            public boolean isCanUseAndroidId() {
                return super.isCanUseAndroidId();
            }

            @Override
            public String getAndroidId() {
                return super.getAndroidId();
            }

            @Override
            public MediationPrivacyConfig getMediationPrivacyConfig() {
                return new MediationPrivacyConfig() {

                    @Override
                    public boolean isLimitPersonalAds() {
                        return !limitPersonalAds;
                    }

                    @Override
                    public boolean isProgrammaticRecommend() {
                        return limitPersonalAds;
                    }
                };
            }

            @Override
            public boolean isCanUsePermissionRecordAudio() {
                return super.isCanUsePermissionRecordAudio();
            }
        };
    }

    /**
     * 显示开屏广告
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void showSplashAd(MethodCall call, MethodChannel.Result result) {
        if (splashAdLoad != null && splashAdLoad.isDisplay) {
            result.success(false);
            Log.e(TAG, "showSplashAd 广告展示中，无法再次展示");
            return;
        }
        boolean preload = call.argument(PRELOAD);
        // 如果是预加载的情况下，已经加载完毕则进行展示
        if (splashAdLoad != null && splashAdLoad.isLoaded && preload) {
            splashAdLoad.goActivity();
        } else {
            // 加载广告并展示，如果非预加载的情况下
            splashAdLoad = new SplashAdLoad();
            splashAdLoad.showAd(activity, call);
        }
        result.success(true);
    }

    /**
     * 显示插屏广告
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void showInterstitialAd(MethodCall call, MethodChannel.Result result) {
        InterstitialPage adPage = new InterstitialPage();
        adPage.showAd(activity, call);
        result.success(true);
    }

    /**
     * 显示激励视频广告
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void showRewardVideoAd(MethodCall call, MethodChannel.Result result) {
        RewardVideoPage adPage = new RewardVideoPage();
        adPage.showAd(activity, call);
        result.success(true);
    }

    /**
     * 加载信息流广告列表
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void loadFeedAd(MethodCall call, MethodChannel.Result result) {
        FeedAdLoad feedAd = new FeedAdLoad();
        feedAd.loadFeedAdList(activity, call, result);
    }

    /**
     * 删除信息流广告列表
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void clearFeedAd(MethodCall call, MethodChannel.Result result) {
        List<Integer> adList = call.argument("list");
        if (adList != null) {
            for (int ad : adList) {
                FeedAdManager.getInstance().removeAd(ad);
            }
        }
        result.success(true);
    }

    /**
     * 启动测试工具
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void launchTestTools(MethodCall call, MethodChannel.Result result) {
        // if (BuildConfig.DEBUG) {
        TTMediationTestTool.launchTestTools(activity, new TTMediationTestTool.ImageCallBack() {
            @Override
            public void loadImage(ImageView imageView, String s) {

            }
        });
        // }
        result.success(true);
    }

}
