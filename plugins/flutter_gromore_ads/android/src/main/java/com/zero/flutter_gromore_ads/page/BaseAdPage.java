package com.zero.flutter_gromore_ads.page;

import static com.zero.flutter_gromore_ads.PluginDelegate.KEY_POSID;

import android.app.Activity;
import android.util.Log;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.bytedance.sdk.openadsdk.mediation.manager.MediationAdEcpmInfo;
import com.bytedance.sdk.openadsdk.mediation.manager.MediationBaseManager;
import com.zero.flutter_gromore_ads.event.AdEcpmEvent;
import com.zero.flutter_gromore_ads.event.AdErrorEvent;
import com.zero.flutter_gromore_ads.event.AdEvent;
import com.zero.flutter_gromore_ads.event.AdEventAction;
import com.zero.flutter_gromore_ads.event.AdEventHandler;

import io.flutter.plugin.common.MethodCall;

/**
 * 基础广告页面
 */
public abstract class BaseAdPage {
    private static final String TAG = RewardVideoPage.class.getSimpleName();
    // 上下文
    protected Activity activity;
    // 广告位 id
    protected String posId;
    // 广告配置
    protected AdSlot adSlot;
    // 广告加载器
    protected TTAdNative adLoader;

    /**
     * 显示广告
     *
     * @param activity 上下文
     * @param call     方法调用
     */
    public void showAd(Activity activity, MethodCall call) {
        this.activity = activity;
        this.posId = call.argument(KEY_POSID);
        this.adLoader= TTAdSdk.getAdManager().createAdNative(activity);
        loadAd(call);
    }

    /**
     * 加载广告
     *
     * @param call 方法调用
     */
    public abstract void loadAd(MethodCall call);

    /**
     * 发送广告事件
     *
     * @param event 广告事件
     */
    protected void sendEvent(AdEvent event) {
        AdEventHandler.getInstance().sendEvent(event);
    }

    /**
     * 发送广告事件
     *
     * @param action 操作
     */
    protected void sendEvent(String action) {
        sendEvent(new AdEvent(posId, action));
    }

    /**
     * 发送错误事件
     *
     * @param errCode 错误码
     * @param errMsg  错误事件
     */
    protected void sendErrorEvent(int errCode, String errMsg) {
        sendEvent(new AdErrorEvent(posId, errCode, errMsg));
    }

    /**
     * 发送 ecmp 事件
     * @param adManager 广告媒体管理
     */
    protected void sendEcmpEvent(MediationBaseManager adManager){
        if (adManager != null) {
            Log.i(TAG, "sendEcmpEvent");
            // 获取广告 ecmp
            MediationAdEcpmInfo adEcpmInfo=adManager.getShowEcpm();
            AdEcpmEvent ecpmEvent=new AdEcpmEvent(this.posId, AdEventAction.onAdEcpm,adEcpmInfo);
            sendEvent(ecpmEvent);
            Log.i(TAG, "sendEcmpEvent adEcpmInfo:"+adEcpmInfo.getEcpm());
        }
    }
}
