package com.zero.flutter_gromore_ads.event;

import com.bytedance.sdk.openadsdk.mediation.manager.MediationAdEcpmInfo;

import java.util.HashMap;

/**
 * 广告Ecmp事件
 */
public class AdEcpmEvent extends AdEvent {
    protected MediationAdEcpmInfo ecpmInfo;
    public AdEcpmEvent(String adId, String action,MediationAdEcpmInfo ecpmInfo) {
        super(adId, action);
        this.ecpmInfo = ecpmInfo;
    }
    public HashMap<String, Object> toMap() {
        HashMap<String, Object> newMap = super.toMap();
        newMap.put("ecpm", ecpmInfo.getEcpm());
        newMap.put("channel", ecpmInfo.getChannel());
        newMap.put("subChannel", ecpmInfo.getSubChannel());
        newMap.put("sdkName", ecpmInfo.getSdkName());
        newMap.put("scenarioId", ecpmInfo.getScenarioId());
        newMap.put("errMsg", ecpmInfo.getErrorMsg());
        newMap.put("customData", ecpmInfo.getCustomData());
        return newMap;
    }
}
