package com.zero.flutter_gromore_ads.load;

import android.app.Activity;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.bytedance.sdk.openadsdk.TTFeedAd;
import com.zero.flutter_gromore_ads.event.AdEventAction;
import com.zero.flutter_gromore_ads.page.BaseAdPage;
import com.zero.flutter_gromore_ads.utils.UIUtils;

import java.util.ArrayList;
import java.util.List;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

/**
 * 信息流加载对象
 */
public class FeedAdLoad extends BaseAdPage implements TTAdNative.FeedAdListener {
    private final String TAG = FeedAdLoad.class.getSimpleName();
    TTAdNative ad;
    private MethodChannel.Result result;

    /**
     * 加载信息流广告列表
     *
     * @param call
     * @param result
     */
    public void loadFeedAdList(Activity activity, @NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        this.result = result;
        showAd(activity, call);
    }

    @Override
    public void loadAd(@NonNull MethodCall call) {
        // 获取请求模板广告素材的尺寸
        int expressViewWidth = call.argument("width");
        int expressViewHeight = call.argument("height");
        int count = call.argument("count");
        // 原生广告
        ad = TTAdSdk.getAdManager().createAdNative(activity);
        // 广告配置
        AdSlot adSlot = new AdSlot.Builder()
                .setCodeId(this.posId)
                .setImageAcceptedSize(UIUtils.dp2px(activity, expressViewWidth), UIUtils.dp2px(activity, expressViewHeight))// 单位dp
//                .setExpressViewAcceptedSize(expressViewWidth, expressViewHeight) // 单位dp
                .setAdCount(count) // 请求广告数量为1到3条 （优先采用平台配置的数量）
                .build();
        ad.loadFeedAd(adSlot, this);
    }

    @Override
    public void onFeedAdLoad(List<TTFeedAd> list) {
        Log.i(TAG, "FeedAdLoad onAdLoaded");
        List<Integer> adResultList = new ArrayList<>();

        if (list.isEmpty()) {
            this.result.success(adResultList);
            return;
        }
        Log.i(TAG, "FeedAdLoad onAdLoaded ：" + list.size());
        for (TTFeedAd adItem : list) {
            int key = adItem.hashCode();
            adResultList.add(key);
            FeedAdManager.getInstance().putAd(key, adItem);
        }
        // 添加广告事件
        sendEvent(AdEventAction.onAdLoaded);
        this.result.success(adResultList);
    }

    @Override
    public void onError(int i, String s) {
        Log.e(TAG, "FeedAdLoad onError code:" + i + " msg:" + s);
        sendErrorEvent(i, s);
        this.result.error("" + i, s, s);
    }
}
