group 'com.zero.flutter_gromore_ads'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
        //GroMore SDK依赖	
        maven {	
            url "https://artifact.bytedance.com/repository/pangle"	
        }
        //mintegral sdk依赖
        maven {
            url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_support/"
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        //GroMore SDK依赖	
        maven {
            url "https://artifact.bytedance.com/repository/pangle"	
        }
        //mintegral sdk依赖
        maven {
            url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_support/"
        }
    }
}

apply plugin: 'com.android.library'

android {
    namespace = 'com.zero.flutter_gromore_ads'
    compileSdk 31
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    defaultConfig {
        minSdkVersion 21
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    // 依赖库：https://artifact.bytedance.com/repository/pangle/com/pangle/cn/
    // 测试工具（这里仅编译引用）
    compileOnly files('libs/tools-release.aar')
    //GroMore_sdk
    implementation "com.pangle.cn:mediation-sdk:6.4.1.5"  //融合SDK
}
