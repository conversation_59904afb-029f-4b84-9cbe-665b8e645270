<p align="center">
<a href="https://github.com/FlutterAds"><img src="https://raw.githubusercontent.com/FlutterAds/site/master/logo/flutterads_logo.png" alt="logo"/></a>
</p>
<h3 align="center">一款可以帮你提升收益的 Flutter 广告插件（Pro 版）</h3>

<p align="center">
<a href="https://pub.dev/packages/flutter_gromore_ads"><img src=https://img.shields.io/badge/version-v3.7.4-success></a>
<a href="https://github.com/FlutterAds/flutter_gromore_ads"><img src=https://img.shields.io/badge/platform-iOS%20%7C%20Android-brightgreen></a>
<a href="https://github.com/FlutterAds/flutter_gromore_ads/actions/workflows/flutter.yml"><img src="https://github.com/FlutterAds/flutter_gromore_ads/actions/workflows/flutter.yml/badge.svg?branch=develop"></a>
<p align="center">
<a href="https://flutterads.github.io/site/"><img src="https://raw.githubusercontent.com/FlutterAds/.github/main/gromore_pro_site-2.png" alt="gromore"/></a>
</p>

## 支持功能

- ✅ 开屏广告
- ✅ 插屏广告
- ✅ 横幅广告
- 🏆 信息流（Pro 版本）
- 🏆 激励视频（Pro 版本）
- 🏆 激励视频-二次激励（Pro 版本）

## 入门使用

### 引入依赖

> 在你的项目根目录下新建 plugins 文件夹，将flutter_gromore_ads 插件到 plugins 文件夹下解压后，在自己项目 pubspec.yaml 中引入插件即可。

``` Dart
dependencies:
  flutter_gromore_ads: 
    path: plugins/flutter_gromore_ads # 本地引入
```

> 下面 `导入 SDK` 是必须的配置，千万别省略了，仔细看文档来配置。

### 初始化广告

``` Dart
// 导包
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
/// [appId] 应用ID
/// [config] 配置文件名称
/// [limitPersonalAds] 是否限制个性化广告，0：不限制 1：限制
FlutterGromoreAds.initAd(appId);
```

### 开屏广告

- 全屏

``` Dart
/// [posId] 广告位 id
/// [logo] 如果传值则展示底部logo，不传不展示，则全屏展示
/// [timeout] 加载超时时间
FlutterGromoreAds.showSplashAd(
  posId,
  timeout: 3.5,
);
```


- 半屏

``` Dart
/// [posId] 广告位 id
/// [logo] 如果传值则展示底部logo，不传不展示，则全屏展示
FlutterGromoreAds.showSplashAd(
  posId,
  logo: 'flutterads_logo',
);
```

> [Logo 设置的最佳实践](https://github.com/FlutterAds/flutter_qq_ads/blob/develop/doc/SETTING_LOGO.md)

### 插屏广告

> 全屏插屏、半插屏、横插屏 都是由后台配置的，所以这里只传入广告位 id 即可。

``` Dart
/// [posId] 广告位 id
FlutterGromoreAds.showInterstitialAd(
    posId,
);
```

### 横幅广告

> 宽高更具你的广告配置来设置，这里只是示例

``` Dart
/// [posId] 广告位 id
/// [width] 宽度
/// [height] 高度
AdBannerWidget(
    posId: posId,
    width: 300,
    height: 75,
)
```

### 🏆 信息流广告

> `width` 和 `height` 需要根据你新建广告位的模板来设置

- 获取信息流广告列表

``` Dart
/// [posId] 广告位 id
/// [width] 宽度
/// [height] 高度
/// [count] 获取广告数量，建议 1~3 个
List<int> feedAdList = await FlutterGromoreAds.loadFeedAd(
    AdsConfig.feedId,
    width: 375,
    height: 128,
    count: 3,
  );
```
- 清除信息流广告列表

> 当你的广告不再需要时，请一定执行清除操作

``` Dart
/// [list] 信息流广告 id 列表
bool result = await FlutterGromoreAds.clearFeedAd(feedAdList);
```

- 页面中展示信息流广告
``` Dart
/// Feed 信息流广告组件
/// [posId]返回的广告 id，这里不是广告位id
/// [width]组件的宽度
/// [height]组件的高度
/// [show]是否显示
AdFeedWidget(
    posId: '${feedAdList[0]}',
    width: 375,
    height: 128,
    show: true,
  )
```

### 🏆 激励视频广告

> 二次激励视频广告，需要在 Gromore 后台配置
``` Dart
/// [posId] 广告位 id
/// [customData] 设置服务端验证的自定义信息
/// [userId] 设置服务端验证的用户信息
bool result = await FlutterGromoreAds.showRewardVideoAd(
    posId,
    customData: 'customData',
    userId: 'userId',
);
_result = "展示激励视频广告${result ? '成功' : '失败'}";

```

> 激励回调事件请查看下面的 `设置广告事件监听`，具体字段说明参考 AdRewardEvent

``` Dart
// 奖励类型，0:基础奖励 >0:进阶奖励 。4400版本新增
  final int rewardType;
  // 奖励是否有效
  final bool rewardVerify;
  // 奖励数量
  final int rewardAmount;
  // 奖励名称
  final String rewardName;
  // 错误码
  final int? errCode;
  // 错误信息
  final String? errMsg;
  // 服务端验证的自定义信息
  final String? customData;
  // 服务端验证的用户信息
  final String? userId;
```


### 设置广告事件监听

``` Dart
FlutterGromoreAds.onEventListener((event) {
    _adEvent = 'adId:${event.adId} action:${event.action}';
    if (event is AdErrorEvent) {
        // 错误事件
        _adEvent += ' errCode:${event.errCode} errMsg:${event.errMsg}';
    }else if (event is AdRewardEvent) {
        // 激励事件
        _adEvent +=
            ' rewardVerify:${event.rewardVerify} rewardAmount:${event.rewardAmount} rewardName:${event.rewardName} errCode:${event.errCode} errMsg:${event.errMsg} customData:${event.customData} userId:${event.userId}';
      }
    debugPrint('onEventListener:$_adEvent');
});
```
### 事件列表
|事件|说明|
|-|-|
|onAdLoaded|广告加载成功|
|onAdPresent|广告填充|
|onAdExposure|广告曝光|
|onAdClosed|广告关闭（开屏计时结束或者用户点击关闭）|
|onAdClicked|广告点击|
|onAdSkip|广告跳过|
|onAdComplete|广告播放或计时完毕|
|onAdError|广告错误|
|onAdReward|获得广告激励|

### 导入 SDK 

#### Android

- 引入依赖

|参考示例|官方文档|
|--|--|
|[build.gradle](https://github.com/FlutterAds/flutter_gromore_ads/blob/develop/example/android/app/build.gradle)|[点击这里](https://www.csjplatform.com/union/media/union/download/detail?id=142&docId=27562&osType=android)|

打开 `android/app/build.gradle` 添加依赖，需要哪个添加哪个，`Adapter` 和 `SDK` 要成对添加

``` gradle
dependencies {
    //GroMore_sdk adapter
    implementation "com.pangle.cn:mediation-gdt-adapter:4.540.1410.1" //gdt adapter
    implementation 'com.qq.e.union:union:4.540.1410'// 广点通广告 SDK
}
```

> 这里通过远程仓库导入你也可以按照官方示例中的 `aar` 导入依赖

- 添加配置文件

> 本插件内已经将可以内置的配置都内置了，只需要添加特有的配置接口，官方文档仅作为参考

|参考示例|官方文档|
|--|--|
|[AndroidManifest.xml](https://github.com/FlutterAds/flutter_gromore_ads/blob/develop/example/android/app/src/main/AndroidManifest.xml)|[点击这里](https://www.csjplatform.com/union/media/union/download/detail?id=75&docId=604de8b510af03004cbcbf69&osType=android#_1-2-2-androidmanifest-xml-第三方adn相关配置)|

打开 `android/app/src/main/AndroidManifest.xml` 添加对应的配置文件，与上面的 SDK 对应

``` xml
<!-- GDT start================== -->
<!-- targetSDKVersion >= 24时才需要添加这个provider。provider的authorities属性的值为${applicationId}.fileprovider，请开发者根据自己的${applicationId}来设置这个值，例如本例中applicationId为"com.qq.e.union.demo"。 -->
<provider
    android:name="com.qq.e.comm.GDTFileProvider"
    android:authorities="${applicationId}.gdt.fileprovider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/gdt_file_path" />
</provider>

<activity
    android:name="com.qq.e.ads.PortraitADActivity"
    android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
    android:screenOrientation="portrait" />
<activity
    android:name="com.qq.e.ads.LandscapeADActivity"
    android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
    android:screenOrientation="landscape"
    tools:replace="android:screenOrientation" />

<!-- 声明SDK所需要的组件 -->
<service
    android:name="com.qq.e.comm.DownloadService"
    android:exported="false" />
<!-- 请开发者注意字母的大小写，ADActdivity，而不是AdActivity -->

<activity
    android:name="com.qq.e.ads.ADActivity"
    android:configChanges="keyboard|keyboardHidden|orientation|screenSize" />
<!-- GDT end================== -->


<!-- Pangle start================== -->

<provider
    android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
    android:authorities="${applicationId}.TTFileProvider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/pangle_file_paths" />
</provider>

<provider
    android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
    android:authorities="${applicationId}.TTMultiProvider"
    android:exported="false" />

<!-- Pangle end================== -->

<!-- 其他广告配置参考官方文档添加即可================== -->	
```

- 动态请求权限（仅 Android）

> 必要权限已添加，其他权限`参考示例`和`官方文档`酌情添加即可。

``` Dart
bool result = await FlutterGromoreAds.requestPermissionIfNecessary;
```

#### iOS

- 引入依赖

|参考示例|官方文档|
|--|--|
|[example](https://github.com/FlutterAds/flutter_gromore_ads/blob/master/example/ios)|[点击这里](https://www.csjplatform.com/union/media/union/download/detail?id=143&docId=27487&osType=ios)|

1、在修改 `ios/Podfile` 引入 `SDK`，参考 [Podfile](https://github.com/FlutterAds/flutter_gromore_ads/blob/master/example/ios/Podfile)

``` ruby
#1.GroMoreSDK核心库
# 广点通/优量汇
pod 'CSJMGdtAdapter', '*********'
pod 'GDTMobSDK','4.14.30'
```


- 添加配置文件

打开 `ios/Runner/Info.plist` 添加如下配置，参考 [Info.plist](https://github.com/FlutterAds/flutter_gromore_ads/blob/56f8ac69747b7c123024511755d79d667cbc5ede/example/ios/Runner/Info.plist#L25-L31)

``` xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
<key>NSUserTrackingUsageDescription</key>
<string>为了向您提供更优质、安全的个性化服务及内容，需要您允许使用相关权限</string>
```

- 请求应用跟踪透明度授权（仅 iOS）
此步骤必须要做，不然上架审核时候会被拒绝
``` Dart
bool result = await FlutterGromoreAds.requestIDFA;
```


## 遇到问题
感谢你的支持，如遇到问题请随时微信联系我，我会第一时间解决你的问题。

## FlutterAds 广告插件系列
|插件|描述|
|-|-|
|[flutter_qq_ads](https://github.com/FlutterAds/flutter_qq_ads)|腾讯广告、广点通、优量汇 Flutter 广告插件|
|[flutter_pangle_ads](https://github.com/FlutterAds/flutter_pangle_ads)|字节跳动、穿山甲 Flutter 广告插件|
|[flutter_gromore_ads](https://github.com/FlutterAds/flutter_gromore_ads)|字节跳动、穿山甲、GroMore 聚合 Flutter 广告插件|
|[flutter_gromore_pro](https://flutterads.github.io/site/)|🏆🏆🏆 帮你大幅提升广告收益，发挥出最大的用户价值|

