import os

def check_utf8_bom(folder_path):
    """检查文件夹中所有文件是否是 utf-8 with bom 编码"""
    for file_name in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file_name)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                if f.read(3) == b'\xef\xbb\xbf':
                    print(f"{file_path}{file_name} 是 utf-8 with bom 编码")
        else:
            check_utf8_bom(file_path)

if __name__ == '__main__':
    check_utf8_bom('/Users/<USER>/proj/FlutterAds/flutter_gromore_ads/example')
