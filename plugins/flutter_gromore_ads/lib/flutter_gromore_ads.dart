import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';

import 'event/ad_event_handler.dart';

export 'event/ad_event_handler.dart';
export 'view/ad_banner_widget.dart';
export 'view/ad_feed_widget.dart';

/// 方向常量
// 竖屏
const int vertical = 1;
// 横屏
const int horizontal = 2;

/// GroMore 广告插件
class FlutterGromoreAds {
  // 方法通道
  static const MethodChannel _methodChannel =
      MethodChannel('flutter_gromore_ads');
  // 事件通道
  static const EventChannel _eventChannel =
      EventChannel('flutter_gromore_ads_event');

  ///事件回调
  ///@params onData 事件回调
  static Future<void> onEventListener(
      OnAdEventListener onAdEventListener) async {
    _eventChannel.receiveBroadcastStream().listen((data) {
      hanleAdEvent(data, onAdEventListener);
    });
  }

  /// 请求应用跟踪透明度授权(仅 iOS)
  static Future<bool> get requestIDFA async {
    if (Platform.isIOS) {
      final bool result = await _methodChannel.invokeMethod('requestIDFA');
      return result;
    }
    return true;
  }

  /// 动态请求相关权限（仅 Android）
  static Future<bool> get requestPermissionIfNecessary async {
    if (Platform.isAndroid) {
      final bool result =
          await _methodChannel.invokeMethod('requestPermissionIfNecessary');
      return result;
    }
    return true;
  }

  /// 初始化广告
  /// [appId] 应用ID
  /// [config] 配置文件名称
  /// [useMediation] 是否使用穿山甲聚合,true：使用 GroMore 聚合 false：不使用聚合，仅使用穿山甲
  /// [limitPersonalAds] 是否限制个性化广告，0：不限制 1：限制
  /// [themeStatus] 主题状态，0：普通模式 1：暗黑模式
  static Future<bool> initAd(String appId,
      {String? config,
      bool useMediation = true,
      int limitPersonalAds = 0,
      int themeStatus = 0}) async {
    final bool result = await _methodChannel.invokeMethod(
      'initAd',
      {
        'appId': appId,
        'config': config,
        'useMediation': useMediation,
        'limitPersonalAds': limitPersonalAds,
        'themeStatus': themeStatus,
      },
    );
    return result;
  }

  /// 预加载
  /// [rewardPosids] 激励视频广告位 id 列表
  /// [insertPosids] 插屏广告位 id 列表
  /// [concurrent] 并行加载的广告位数，默认值为2，合法值为[1,20]
  /// [interval] 时间间隔，默认值为2s，合法值为[1,10]
  /// [orientation] 视频方向，vertical：竖屏 horizontal：
  /// [customData] 设置服务端验证的自定义信息
  /// [userId] 设置服务端验证的用户信息
  static Future<bool> preload({
    List<String> rewardPosids = const [],
    List<String> insertPosids = const [],
    int concurrent = 2,
    int interval = 2,
    int orientation = vertical,
    String customData = "FlutterAds",
    String userId = "userId",
  }) async {
    bool result = await _methodChannel.invokeMethod(
      'preload',
      {
        'rewardPosids': rewardPosids,
        'insertPosids': insertPosids,
        'concurrent': concurrent,
        'interval': interval,
        'orientation': orientation,
        'customData': customData,
        'userId': userId,
      },
    );
    return result;
  }

  /// 加载并展示开屏广告（常用的，原生页面先跳转再加载）
  /// [posId] 广告位 id
  /// [logo] 如果传值则展示底部logo，不传不展示，则全屏展示
  /// [timeout] 加载超时时间
  /// [preload] 是否预加载，不展示 true：预加载 false：加载并展示（如果已预加载过，则会展示）
  static Future<bool> showSplashAd(String posId,
      {String? logo, double timeout = 3.5, bool preload = false}) async {
    final bool result = await _methodChannel.invokeMethod(
      'showSplashAd',
      {
        'posId': posId,
        'logo': logo,
        'timeout': timeout,
        'preload': preload,
      },
    );
    return result;
  }

  /// 展示插屏广告
  /// [posId] 广告位 id
  static Future<bool> showInterstitialAd(String posId) async {
    final bool result = await _methodChannel.invokeMethod(
      'showInterstitialAd',
      {'posId': posId},
    );
    return result;
  }

  /// 展示激励视频广告
  /// [posId] 广告位 id
  /// [customData] 设置服务端验证的自定义信息
  /// [userId] 设置服务端验证的用户信息
  static Future<bool> showRewardVideoAd(
    String posId, {
    int orientation = vertical,
    String customData = "FlutterAds",
    String userId = "userId",
  }) async {
    final bool result = await _methodChannel.invokeMethod(
      'showRewardVideoAd',
      {
        'posId': posId,
        'orientation': orientation,
        'customData': customData,
        'userId': userId,
      },
    );
    return result;
  }

  /// 加载信息流广告列表
  /// [posId] 广告位 id
  /// [width] 宽度
  /// [height] 高度
  /// [count] 获取广告数量，建议 1~3 个
  static Future<List<int>> loadFeedAd(String posId,
      {int width = 300, int height = 125, int count = 1}) async {
    final List<dynamic> result = await _methodChannel.invokeMethod(
      'loadFeedAd',
      {
        'posId': posId,
        'width': width,
        'height': height,
        'count': count,
      },
    );
    return List<int>.from(result);
  }

  /// 清除信息流广告列表
  /// [list] 信息流广告 id 列表
  static Future<bool> clearFeedAd(List<int> list) async {
    final bool result = await _methodChannel.invokeMethod(
      'clearFeedAd',
      {
        'list': list,
      },
    );
    return result;
  }

  /// 启动测试工具
  static Future<bool> launchTestTools() async {
    final result = await _methodChannel.invokeMethod('launchTestTools');
    return result;
  }
}
