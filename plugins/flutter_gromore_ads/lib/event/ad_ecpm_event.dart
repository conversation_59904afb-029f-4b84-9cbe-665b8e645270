import 'ad_event.dart';

/// 广告价格信息事件
class AdEcpmEvent extends AdEvent {
  AdEcpmEvent(
      {required this.ecpm,
      this.channel,
      this.subChannel,
      this.sdkName,
      this.scenarioId,
      this.errMsg,
      required String adId,
      required String action})
      : super(adId: adId, action: action);

  // Ecmp 价格
  final String ecpm;
  // 渠道
  final String? channel;
  // 子渠道
  final String? subChannel;
  // ADN 平台名称
  final String? sdkName;
  // 场景 ID
  final String? scenarioId;
  // 错误信息
  final String? errMsg;
  // 展示时间
  DateTime showTime = DateTime.now();
  // 点击时间
  DateTime? clickTime;
  // 解析 json 为激励事件对象
  factory AdEcpmEvent.fromJson(Map<dynamic, dynamic> json) {
    return AdEcpmEvent(
      adId: json['adId'],
      action: json['action'],
      ecpm: json['ecpm'],
      channel: json['channel'],
      subChannel: json['subChannel'],
      sdkName: json['sdkName'],
      scenarioId: json['scenarioId'],
      errMsg: json['errMsg'],
    );
  }
}
