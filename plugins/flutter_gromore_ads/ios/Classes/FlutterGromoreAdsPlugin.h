#import <Flutter/Flutter.h>
#import "FGMSplashPage.h"
#import "FGMInterstitialPage.h"
#import "FGMRewardVideoPage.h"
#import "FGMFeedAdLoad.h"
#import "FGMFeedAdManager.h"
#import "FGMFeedAdLoad.h"
#import "FGMFeedAdManager.h"
#import "FGMGroMore.h"


@interface FlutterGromoreAdsPlugin : NSObject<FlutterPlugin,FlutterStreamHandler>
+ (instancetype)shared; // 共享实例
@property (strong,nonatomic) FlutterEventSink eventSink;// 事件
@property (strong,nonatomic) FGMSplashPage *sad;// 开屏广告
@property (strong,nonatomic) FGMInterstitialPage *iad;// 插屏广告
@property (strong,nonatomic) FGMRewardVideoPage *rvad;// 激励视频广告
@property (strong,nonatomic) FGMFeedAdLoad *fad;// 信息流加载

extern NSString *const kGMAdBannerViewId;
extern NSString *const kGMAdFeedViewId;

- (void)addEvent:(NSObject *) event;
- (void)sendEvent:(FGMAdEvent *)event;
@end
