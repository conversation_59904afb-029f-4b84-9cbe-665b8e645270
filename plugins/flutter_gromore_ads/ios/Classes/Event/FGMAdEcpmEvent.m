//
//  FGMAdEcpmEvent.m
//  flutter_gromore_ads
//
//  Created by zero on 2023/10/24.
//

#import "FGMAdEcpmEvent.h"

@implementation FGMAdEcpmEvent

- (id)initWithAdId:(NSString *)adId ecmpInfo:(BUMRitInfo *)ecmpInfo{
    self.action=onAdEcpm;
    self.adId=adId;
    self.ecmpInfo=ecmpInfo;
    return self;
}

- (NSDictionary *)toMap{
    NSDictionary *data=[super toMap];
    NSMutableDictionary *errData=[[NSMutableDictionary alloc]initWithDictionary:data];
    [errData setObject:self.ecmpInfo.ecpm?: @"" forKey:@"ecpm"];
    [errData setObject:self.ecmpInfo.channel?: @"" forKey:@"channel"];
    [errData setObject:self.ecmpInfo.sub_channel?: @"" forKey:@"subChannel"];
    [errData setObject:self.ecmpInfo.adnName?: @"" forKey:@"sdkName"];
    [errData setObject:self.ecmpInfo.scenarioId?: @"" forKey:@"scenarioId"];
    [errData setObject:self.ecmpInfo.errorMsg?: @"" forKey:@"errMsg"];
    return errData;
}
@end
