//
//  FGMAdFeedView.m
//  flutter_gromore_ads
//
//  Created by Zero on 2023/10/7.
//

#import "FGMAdFeedView.h"
#import "FGMFeedAdManager.h"

@interface FGMAdFeedView()<FlutterPlatformView,BUNativeExpressAdViewDelegate>
@property (strong,nonatomic) BUNativeAdsManager *adManager;
@property (strong,nonatomic) UIView *feedView;
@property (strong,nonatomic) BUNativeAd *adView;
@property (strong,nonatomic) FlutterMethodChannel *methodChannel;

@end

@implementation FGMAdFeedView

- (instancetype)initWithFrame:(CGRect)frame viewIdentifier:(int64_t)viewId arguments:(id)args binaryMessenger:(NSObject<FlutterBinaryMessenger> *)messenger plugin:(FlutterGromoreAdsPlugin *)plugin{
    if(self==[super init]){
        self.viewId=viewId;
        self.feedView =[[UIView alloc] init];
        self.methodChannel = [FlutterMethodChannel methodChannelWithName:[NSString stringWithFormat:@"%@/%lli",kGMAdFeedViewId,viewId] binaryMessenger:messenger];
        FlutterMethodCall *call= [FlutterMethodCall methodCallWithMethodName:@"AdFeedView" arguments:args];
        [self showAd:call eventSink:plugin.eventSink];
    }
    return self;
}

- (UIView *)view{
    return self.feedView;
}

// 销毁广告
- (void) disposeAd{
    NSNumber *key=[NSNumber numberWithInteger:[self.adView hash]];
    // 删除广告缓存
    [FGMFeedAdManager.share removeAd:key];
    // 发送广告事件
    [self sendEventAction:onAdClosed];
    // 移除视图
    [self.adView.mediation.canvasView removeFromSuperview];
    // 发送关闭消息
    [self.methodChannel invokeMethod:@"adClose" arguments:nil];
}

- (void)loadAd:(FlutterMethodCall *)call{
    NSNumber *key=[NSNumber numberWithInteger:[self.posId integerValue]];
    self.adView=[FGMFeedAdManager.share getAd:key];
    self.adView.rootViewController=self.rootController;
    self.adView.delegate=self;
    [self.feedView addSubview:self.adView.mediation.canvasView];
    [self.adView.mediation render];
}


#pragma mark BUNativeAdDelegate

- (void)nativeAd:(BUNativeAd *)nativeAd didFailWithError:(NSError *_Nullable)error{
    NSLog(@"%s",__FUNCTION__);
    // 发送广告错误事件
    [self sendErrorEvent:error];
    [self disposeAd];
}

- (void)nativeAdDidBecomeVisible:(BUNativeAd *)nativeAd{
    NSLog(@"%s",__FUNCTION__);
    // 渲染成功
    self.adView.mediation.canvasView.center=self.feedView.center;
    // 发送广告事件
    [self sendEventAction:onAdExposure];
    // 发送 Ecmp 事件
    if (self.adView) {
        [self sendEcmpEvent:[self.adView.mediation getShowEcpmInfo]];
    }
}

- (void)nativeAdDidClick:(BUNativeAd *)nativeAd withView:(UIView *_Nullable)view{
    NSLog(@"%s",__FUNCTION__);
    // 发送广告事件
    [self sendEventAction:onAdClicked];
}

- (void) nativeAdDidCloseOtherController:(BUNativeAd *)nativeAd interactionType:(BUInteractionType)interactionType{
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdResume];
}

- (void)nativeAd:(BUNativeAd *_Nullable)nativeAd dislikeWithReason:(NSArray<BUDislikeWords *> *_Nullable)filterWords{
    NSLog(@"%s",__FUNCTION__);
    [self disposeAd];
}

@end
