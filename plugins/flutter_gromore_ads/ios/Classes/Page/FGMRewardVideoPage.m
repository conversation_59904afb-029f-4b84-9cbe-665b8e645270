//
//  FGMRewardVideoPage.m
//  flutter_gromore_ads
//
//  Created by Zero on 2023/10/7.
//

#import "FGMRewardVideoPage.h"

@implementation FGMRewardVideoPage

// 加载广告
- (void)loadAd:(FlutterMethodCall *)call{
    self.customData = call.arguments[@"customData"] ;
    self.userId = call.arguments[@"userId"];
    // 初始化激励视频广告
    BURewardedVideoModel *model = [[BURewardedVideoModel alloc] init];
    model.extra=self.customData;
    model.userId=self.userId;
    self.ad=[[BUNativeExpressRewardedVideoAd alloc] initWithSlotID:self.posId rewardedVideoModel:model];
    self.ad.delegate=self;
    self.ad.rewardPlayAgainInteractionDelegate=self;
    [self.ad loadAdData];
}


#pragma mark - BUNativeExpressRewardedVideoAdDelegate
- (void)nativeExpressRewardedVideoAdDidLoad:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
    if(self.ad){
        [self.ad showAdFromRootViewController:self.rootController];
    }
    // 发送广告事件
    [self sendEventAction:onAdLoaded];
}

- (void)nativeExpressRewardedVideoAd:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd didFailWithError:(NSError *_Nullable)error {
    NSLog(@"%s",__FUNCTION__);
    // 发送广告错误事件
    [self sendErrorEvent:error];
}

- (void)nativeExpressRewardedVideoAdCallback:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd withType:(BUNativeExpressRewardedVideoAdType)nativeExpressVideoType{
    NSLog(@"%s",__FUNCTION__);
}

- (void)nativeExpressRewardedVideoAdDidDownLoadVideo:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
}

- (void)nativeExpressRewardedVideoAdViewRenderSuccess:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送广告事件
    [self sendEventAction:onAdPresent];
}

- (void)nativeExpressRewardedVideoAdViewRenderFail:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd error:(NSError *_Nullable)error {
    NSLog(@"%s",__FUNCTION__);
    // 发送广告错误事件
    [self sendErrorEvent:error];
}

- (void)nativeExpressRewardedVideoAdWillVisible:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
}

- (void)nativeExpressRewardedVideoAdDidVisible:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送广告事件
    [self sendEventAction:onAdExposure];
    // 发送 Ecmp 事件
    if (self.ad) {
        [self sendEcmpEvent:[self.ad.mediation getShowEcpmInfo]];
    }
}

- (void)nativeExpressRewardedVideoAdWillClose:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
}

- (void)nativeExpressRewardedVideoAdDidClose:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
    self.ad=nil;
    // 发送广告事件
    [self sendEventAction:onAdClosed];
}

- (void)nativeExpressRewardedVideoAdDidClick:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送广告事件
    [self sendEventAction:onAdClicked];
}

- (void)nativeExpressRewardedVideoAdDidClickSkip:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送广告事件
    [self sendEventAction:onAdSkip];
}

- (void)nativeExpressRewardedVideoAdDidPlayFinish:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd didFailWithError:(NSError *_Nullable)error {
    NSLog(@"%s",__FUNCTION__);
    if(error){
        // 发送广告错误事件
        [self sendErrorEvent:error];
    }else{
        // 发送广告事件
        [self sendEventAction:onAdComplete];
    }
}

- (void)nativeExpressRewardedVideoAdServerRewardDidSucceed:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd verify:(BOOL)verify {
    NSLog(@"%s",__FUNCTION__);
    NSLog(@"%@",[NSString stringWithFormat:@"verify:%@ rewardType:%@ rewardName:%ld rewardMount:%ld",verify?@"true":@"false",rewardedVideoAd.rewardedVideoModel.rewardName,(long)rewardedVideoAd.rewardedVideoModel.rewardType,(long)rewardedVideoAd.rewardedVideoModel.rewardAmount]);
    BURewardedVideoModel *model=rewardedVideoAd.rewardedVideoModel;
    // 发送激励事件
    FGMAdRewardEvent *rewardEvent=[[FGMAdRewardEvent alloc] initWithAdId:self.posId rewardType:model.rewardType rewardVerify:verify rewardAmount:model.rewardAmount rewardName:model.rewardName customData:self.customData userId:self.userId errCode:0 errMsg:@"" transId:model.mediation.tradeId];
    [self sendEvent:rewardEvent];
    
    
}

- (void)nativeExpressRewardedVideoAdServerRewardDidFail:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd error:(NSError * _Nullable)error {
    NSLog(@"%s",__FUNCTION__);
    
    NSLog(@"%@",[NSString stringWithFormat:@"rewardName:%@ rewardMount:%ld error:%@",rewardedVideoAd.rewardedVideoModel.rewardName,(long)rewardedVideoAd.rewardedVideoModel.rewardAmount,error]);
    // 发送激励事件
    BURewardedVideoModel *model=rewardedVideoAd.rewardedVideoModel;
    FGMAdRewardEvent *rewardEvent=[[FGMAdRewardEvent alloc] initWithAdId:self.posId rewardType:model.rewardType rewardVerify:NO rewardAmount:model.rewardAmount rewardName:model.rewardName customData:self.customData userId:self.userId errCode:error.code  errMsg:error.localizedDescription transId:@""];
    [self sendEvent:rewardEvent];
}

- (void)nativeExpressRewardedVideoAdDidCloseOtherController:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd interactionType:(BUInteractionType)interactionType {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdResume];
}

@end
