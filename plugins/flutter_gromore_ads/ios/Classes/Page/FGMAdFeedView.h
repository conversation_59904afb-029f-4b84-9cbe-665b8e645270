//
//  FGMAdFeedView.h
//  flutter_gromore_ads
//
//  Created by Zero on 2023/10/7.
//

#import "FGMBasePage.h"
#import "FlutterGromoreAdsPlugin.h"

// 信息流view
@interface FGMAdFeedView : FGMBasePage<FlutterPlatformView,BUNativeAdDelegate>
@property (strong,nonatomic,nullable) FlutterGromoreAdsPlugin *plugin;
@property int64_t viewId;
- (nonnull instancetype) initWithFrame:(CGRect)frame
                        viewIdentifier:(int64_t)viewId
                             arguments:(id _Nullable)args
                       binaryMessenger:(NSObject<FlutterBinaryMessenger>* _Nullable)messenger plugin:(FlutterGromoreAdsPlugin* _Nullable) plugin;
- (nonnull UIView*) view;
@end
