//
//  FGMSplashPage.m
//  flutter_gromore_ads
//
//  Created by zero on 2021/12/11.
//

#import "FGMSplashPage.h"

@interface FGMSplashPage ()<BUSplashAdDelegate>

@end

@implementation FGMSplashPage

- (void)loadAd:(FlutterMethodCall *)call{
    NSLog(@"%s，%@",__FUNCTION__,self.posId);
    NSString* logo=call.arguments[@"logo"];
    double timeout=[call.arguments[@"timeout"] doubleValue];
    self.preload=[call.arguments[@"preload"] boolValue];
    // logo 判断为空，则全屏展示
    self.fullScreenAd=[logo isKindOfClass:[NSNull class]]||[logo length]==0;
    // 创建广告
    self.ad =[[BUSplashAd alloc] initWithSlotID:self.posId adSize:CGSizeZero];
    self.ad.delegate=self;
    self.ad.supportCardView = YES;
    self.ad.supportZoomOutView = YES;
    self.ad.tolerateTimeout=timeout;
    if (!self.fullScreenAd) {
        CGSize size=[[UIScreen mainScreen] bounds].size;
        CGFloat width=size.width;
        CGFloat height=112.5;// 这里按照 15% 进行logo 的展示，防止尺寸不够的问题，750*15%=112.5
        // 设置底部 logo
        self.bottomView=nil;
        self.bottomView=[[UIView alloc]initWithFrame:CGRectMake(0, 0,width, height)];
        self.bottomView.backgroundColor=[UIColor whiteColor];
        UIImageView *logoView=[[UIImageView alloc]initWithImage:[UIImage imageNamed:logo]];
        logoView.frame=CGRectMake(0, 0, width, height);
        logoView.contentMode=UIViewContentModeCenter;
        logoView.center=self.bottomView.center;
        [self.bottomView addSubview:logoView];
        [self.ad.mediation setCustomBottomView:self.bottomView];
    }
    [self.ad loadAdData];
}

// 显示开屏页面
- (void) showSplashPage{
    NSLog(@"%s",__FUNCTION__);
    if(self.ad !=nil){
        [self.ad showSplashViewInRootViewController:self.rootController];
        self.isDisplay=YES;
    }else{
        NSLog(@"%s","广告未加载不可以进行展示");
    }
}

// 销毁开屏页面
- (void) destoryPage{
    // 销毁广告
    if (self.ad) {
        [self.ad.mediation destoryAd];
    }
    self.preload=NO;
    self.isLoaded=NO;
    self.isDisplay=NO;
}

- (void)splashAdLoadSuccess:(nonnull BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdLoaded];
    // 显示页面
    if(!self.preload){
        [self showSplashPage];
    }else{
        self.isLoaded=YES;
    }
}
- (void)splashAdLoadFail:(nonnull BUSplashAd *)splashAd error:(BUAdError * _Nullable)error {
    NSLog(@"%s-error:%@", __func__, error);
    // 发送事件
    [self sendErrorEvent:error];
    // 销毁广告
    [self destoryPage];
}
// 渲染成功
- (void)splashAdRenderSuccess:(BUSplashAd *)splashAd{
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdPresent];
}

// 广告即将展示
- (void)splashAdWillShow:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    //  Fix:放在这里是解决有的厂商不回掉下面的广告展示方法的问题
    // 发送事件
    [self sendEventAction:onAdExposure];
    // 发送 Ecmp 事件
    if (self.ad) {
        [self sendEcmpEvent:[self.ad.mediation getShowEcpmInfo]];
    }
}
// 广告展示
- (void)splashAdDidShow:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
}

- (void)splashAdDidShowFailed:(BUSplashAd *)splashAd error:(NSError *)error{
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendErrorEvent:error];
    // 销毁广告
    [self destoryPage];
}

- (void)splashAdDidClick:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdClicked];
}

- (void)splashAdDidClose:(BUSplashAd *)splashAd closeType:(BUSplashAdCloseType)closeType {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdClosed];
    // 销毁广告
    [self destoryPage];
}


- (void)splashCardReadyToShow:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    [self.ad showSplashViewInRootViewController:self.rootController];
    // 发送事件
    [self sendEventAction:onAdLoaded];
}

- (void)splashCardViewDidClick:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdClicked];
}

- (void)splashCardViewDidClose:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdClosed];
    // 销毁广告
    [self destoryPage];
}

- (void)splashAdViewControllerDidClose:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
}

- (void)splashDidCloseOtherController:(BUSplashAd *)splashAd interactionType:(BUInteractionType)interactionType {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdResume];
}


- (void)splashVideoAdDidPlayFinish:(BUSplashAd *)splashAd didFailWithError:(NSError *)error {
    NSLog(@"%s",__FUNCTION__);
}

- (void)splashAdRenderFail:(nonnull BUSplashAd *)splashAd error:(BUAdError * _Nullable)error { 
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendErrorEvent:error];
    // 销毁广告
    [self destoryPage];
}



- (void)splashZoomOutViewDidClick:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdClicked];
}


- (void)splashZoomOutViewDidClose:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    // 发送事件
    [self sendEventAction:onAdClosed];
    // 销毁广告
    [self destoryPage];
}

- (void)splashZoomOutReadyToShow:(BUSplashAd *)splashAd {
    NSLog(@"%s",__FUNCTION__);
    // 接入方法一：使用SDK提供动画接入
    if (self.ad.zoomOutView) {
        [self.ad showZoomOutViewInRootViewController:self.rootController];
    }
}

@end
