#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint flutter_gromore_ads.podspec' to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'flutter_gromore_ads'
  s.version          = '3.7.4'
  s.summary          = '一款优质的 Flutter 广告插件（GroMore、穿山甲）'
  s.description      = <<-DESC
  FlutterAds 致力于构建优质的 Flutter 广告插件
                       DESC
  s.homepage         = 'https://flutterads.github.io/site/'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'ZeroFlutter' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.public_header_files = 'Classes/**/*.h'
  s.dependency 'Flutter'
  # 版本看这里：https://github.com/volcengine/volcengine-specs/tree/master/Ads-CN-Beta
  # Pod: https://github.com/CocoaPods/Specs/tree/master/Specs/7/e/0/Ads-CN-Beta
  # Pod：https://github.com/CocoaPods/Specs/tree/master/Specs/d/9/8/Ads-CN/
  # Pod: https://github.com/CocoaPods/Specs/tree/master/Specs/7/a/1/BUAdTestMeasurement-Beta
  # GroMore 聚合 SDK~测试版
  # s.dependency 'Ads-CN-Beta', '*******'
  # GroMore 测试 SDK（仅在 Debug 模式下有效）
  # s.dependency 'BUAdTestMeasurement-Beta', '*******', :configurations => ['Debug']
  # GroMore 聚合 SDK
  s.dependency 'Ads-CN', '*******'
  # GroMore 测试 SDK（仅在 Debug 模式下有效）
  s.dependency 'BUAdTestMeasurement', '*******', :configurations => ['Debug']
  s.platform = :ios, '12.0'
  s.static_framework = true
  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  
end
