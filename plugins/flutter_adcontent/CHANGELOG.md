## 1.7.5
* 🌟 短故事增加事件监听【进入、翻页、看完、退出】

## 1.7.4
* 🌟 增加倍速播放功能

## 1.7.3
* 🌟 升级短剧、小视频、小说 SDK 到 'v2.8.0.1'
* 🐛 解决小视频内容重复的问题

## 1.7.2
* 🌟 适配融合归因
* 🌟 适配新版 gromore 广告插件

## 1.6.8
* 🌟 支持青少年模式

## 1.6.7
* 🌟 支持只展示有 ICP 备案号内容

## 1.6.6
* 🌟 滑滑溜支持监听播放事件

## 1.6.5
* 🌟 统一短剧和短故事解锁流程
* 🌟 短故事新增自定义广告解锁
* 🚚 优化示例 App

## 1.6.3
* 🐛 修复短剧事件通知不回调监听的问题

## 1.6.2
* 🐛 修复小视频 iOS  暂停播放没方法的问题
* 🌟 新增小视频播放等事件监听

## 1.6.1
* 🚚 优化短故事类目接口返回
* ⚡️ 短剧实体新增一些字段



## 1.6.0
* 🎉【新增】支持短故事功能及相关接口
* 🍎 iOS 升级 SDK 至 `短剧_v5.3.0.0`、`小视频_v2.6.0.0`、`短故事_v2.6.0.0`、`广告_v6.5.0.9`
* 🤖 Android 升级 SDK 至 `短剧_v2.6.0.0`、`小视频_v4.2.1.0`、`短故事_v2.6.0.0`、`广告_v6.4.1.5`
* 🌟 短剧接口支持排序参数
* 🚚 优化示例 App，增加短故事示例页面

## 1.5.5
* 🌟 【新增短剧】支持自定义广告的解锁短剧剧集

## 1.5.4
* 🍎 iOS 广告升级 SDK 至 `v6.5.0.9`
* 🤖 Android 广告升级 SDK 至 `v6.4.1.5`

## 1.5.0
* 🌟 【新增短剧】收藏和取消收藏接口
* 🌟 【新增短剧】获取收藏列表接口
* 🎉 优化示例 App 增加收藏列表页面
* 🍎 iOS 升级 SDK 至 `短剧_v4.5.0`、`小视频_v1.1.8.8`
* 🤖 Android 升级 SDK 至 `短剧_v2.1.0.0`、`小视频_v4.2.1.0`

## 1.4.2
* 🌟 新增小视频滑动视频个数监听
* 🐛 修复打开短剧详情的错误
* 🐛 修复无网络打开短剧详情崩溃
* ➖ 删除无用的 http 依赖仓库导致的编译报错
* 🚚 优化示例 App

## 1.4.0
* 🌟 新增剧场和滑滑流模式支持 Flutter 组件嵌入页面
* 🌟 新增剧场和滑滑流模式支持跳转到自定义短剧详情页
* 🌟 统一剧场和滑滑流与详情页的参数复用
* 📱 支持小视频【全屏、宫格、双 Feed 流】 组件同时嵌入页面
* ❌ 删除小视频打开原生页面的功能
* ❌ 删除小视频个人中心页面
* 🎉 优化示例 App【短剧小视频】

## 1.3.0
* 🌟 新增短剧播放组件,可嵌入 Flutter 页面
    * 支持自定义解锁短剧
    * 支持自定义播放页面
    * 支持监听所有播放事件
* 🌟 新增小视频功能，可嵌入 Flutter 页面
    * 全屏视频模式
    * 宫格模式
    * 双 Feed 流模式
* 📦 短剧广告事件统一到 `GroMore` 广告中监听
* 📱 优化示例 App

## 0.4.0
* 🤖 支持登录、登出功能(可与自己系统用户 id 绑定)
* 🌟 优化多端适配性，保证 UI 一致性
* 📦 修改示例 App 名字为【短剧小视频】

## 0.3.0
* 🐛 修复 iOS 2 个返回按钮重叠问题
* 🌟 完善优化示例，看着更像一个完整的 App
* 🚚 优化两端参数对齐

## 0.2.0

* 🌟 支持 iOS 平台
* 🌟 支持全部的接口内容
* 🚚 优化 2 个平台参数对齐

## 0.1.0

* 📦 支持与【GroMorePro、AdSpark Pro】全完兼容
* 🌟 支持 Android平台
* 🌟 支持短剧聚合页面
* 🌟 支持短剧滑滑流页面
* 🌟 支持短剧详情页面
    * ✨ 支持数据形式获取短剧，Flutter 侧自由渲染
    * ✨ 获取所有短剧列表
    * ✨ 获取个性化推荐短剧列表
    * ✨ 获取短剧根据短剧 id
    * ✨ 获取短剧分类
    * ✨ 获取短剧列表根据分类
    * ✨ 搜索短剧列表
    * ✨ 获取观看历史记录
    * ✨ 清除历史纪录

## 0.0.1

* 开源版本无功能，🚀 [仅提供 Pro 版本](https://flutterads.top/)
