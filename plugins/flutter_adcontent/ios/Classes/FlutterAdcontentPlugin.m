#import "FlutterAdcontentPlugin.h"
#import "FACPNativeViewFactory.h"
#import <flutter_gromore_ads/FlutterGromoreAdsPlugin.h>
#import "FACPNovelReadViewController.h"


@implementation FlutterAdcontentPlugin
// 短剧剧场 View
NSString *const kFACPTheaterViewId=@"flutter_adcontent_view_theater";
// 短剧详情 View
NSString *const kFACPDramaDetailViewId=@"flutter_adcontent_view_drama";
// 小视频 View
NSString *const kFACPVideoViewId=@"flutter_adcontent_view_video";
// 短故事 View
NSString *const kFACPNovelStoryViewId=@"flutter_adcontent_view_novel_story";
// 短故事事件通道
NSString *const kFACPNovelStoryChannel=@"flutter_adcontent_channel_novel_story";
// 短故事事件通道
FlutterMethodChannel *novelChannel;

+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    FlutterMethodChannel* channel = [FlutterMethodChannel
                                     methodChannelWithName:@"flutter_adcontent"
                                     binaryMessenger:[registrar messenger]];
    FlutterAdcontentPlugin* instance = [[FlutterAdcontentPlugin alloc] init];
    [registrar addMethodCallDelegate:instance channel:channel];
    
    // 注册平台View 工厂
    FACPNativeViewFactory *dramaFactory=[[FACPNativeViewFactory alloc] initWithViewName:kFACPDramaDetailViewId withMessenger:registrar.messenger withPlugin:instance];
    FACPNativeViewFactory *theaterFactory=[[FACPNativeViewFactory alloc] initWithViewName:kFACPTheaterViewId withMessenger:registrar.messenger withPlugin:instance];
    FACPNativeViewFactory *videoFactory=[[FACPNativeViewFactory alloc] initWithViewName:kFACPVideoViewId withMessenger:registrar.messenger withPlugin:instance];
    // 注册 View
    [registrar registerViewFactory:dramaFactory withId:kFACPDramaDetailViewId];
    [registrar registerViewFactory:theaterFactory withId:kFACPTheaterViewId];
    [registrar registerViewFactory:videoFactory withId:kFACPVideoViewId];
    // 注册短故事 View
    FACPNativeViewFactory *novelStoryFactory = [[FACPNativeViewFactory alloc] initWithViewName:kFACPNovelStoryViewId withMessenger:registrar.messenger withPlugin:instance];
    [registrar registerViewFactory:novelStoryFactory withId:kFACPNovelStoryViewId];
    // 短故事事件通道
    novelChannel=[FlutterMethodChannel methodChannelWithName:kFACPNovelStoryChannel binaryMessenger:registrar.messenger];
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSString *methodStr=call.method;
    if ([@"init" isEqualToString:methodStr]) {
        [self initAdContent:call result:result];
    }else if ([@"showTheaterPage" isEqualToString:methodStr]){
        [self showTheaterPage:call result:result];
    }else if ([@"showDrawPage" isEqualToString:methodStr]){
        [self showDrawPage:call result:result];
    }else if ([@"showDetailPage" isEqualToString:methodStr]){
        [self showDetailPage:call result:result];
    }else if ([@"requestAllDramaByRecommend" isEqualToString:methodStr]){
        [self requestAllDramaByRecommend:call result:result];
    }else if ([@"requestAllDrama" isEqualToString:methodStr]){
        [self requestAllDrama:call result:result];
    }else if ([@"requestDrama" isEqualToString:methodStr]){
        [self requestDrama:call result:result];
    }else if ([@"requestDramaByCategory" isEqualToString:methodStr]){
        [self requestDramaByCategory:call result:result];
    }else if ([@"requestDramaCategoryList" isEqualToString:methodStr]){
        [self requestDramaCategoryList:call result:result];
    }else if ([@"searchDrama" isEqualToString:methodStr]){
        [self searchDrama:call result:result];
    }else if ([@"getDramaHistory" isEqualToString:methodStr]){
        [self getDramaHistory:call result:result];
    }else if ([@"clearDramaHistory" isEqualToString:methodStr]){
        [self clearDramaHistory:call result:result];
    }else if ([@"getFavorList" isEqualToString:methodStr]){
        [self getFavorList:call result:result];
    }else if ([@"favorDrama" isEqualToString:methodStr]){
        [self favorDrama:call result:result];
    }else if ([@"setGlobalSpeedPlay" isEqualToString:methodStr]){
        [self setGlobalSpeedPlay:call result:result];
    }else if ([@"getSignString" isEqualToString:methodStr]){
        [self getSignString:call result:result];
    }else if ([@"login" isEqualToString:methodStr]){
        [self login:call result:result];
    }else if ([@"isLogin" isEqualToString:methodStr]){
        [self isLogin:call result:result];
    }else if ([@"logout" isEqualToString:methodStr]){
        [self logout:call result:result];
    }else if ([@"getEpisodesStatus" isEqualToString:methodStr]){
        [self getEpisodesStatus:call result:result];
    }else if ([@"openNovelReaderPage" isEqualToString:methodStr]) {
        [self openNovelReaderPage:call result:result];
    }else if ([@"getStoryCategoryList" isEqualToString:methodStr]) {
        [self getStoryCategoryList:call result:result];
    }else if ([@"searchStory" isEqualToString:methodStr]) {
        [self searchStory:call result:result];
    }else if ([@"requestStoryByIds" isEqualToString:methodStr]) {
        [self requestStoryByIds:call result:result];
    }else if ([@"requestStoryFeed" isEqualToString:methodStr]) {
        [self requestStoryFeed:call result:result];
    }else if ([@"requestStoryByCategory" isEqualToString:methodStr]) {
        [self requestStoryByCategory:call result:result];
    }else if ([@"getStoryHistory" isEqualToString:methodStr]) {
        [self getStoryHistory:call result:result];
    }else if ([@"storyFavorite" isEqualToString:methodStr]) {
        [self storyFavorite:call result:result];
    }else if ([@"storyFavoriteCancel" isEqualToString:methodStr]) {
        [self storyFavoriteCancel:call result:result];
    }else if ([@"getStoryFavorite" isEqualToString:methodStr]) {
        [self getStoryFavorite:call result:result];
    }else {
        result(FlutterMethodNotImplemented);
    }
}

// 初始化
- (void) initAdContent:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    self.rootCon=[[[UIApplication sharedApplication] keyWindow] rootViewController];
    [DJXManager startOpenGLESActivity];
    self.initSkit= [call.arguments[@"initSkit"] boolValue];
    self.initVideo= [call.arguments[@"initVideo"] boolValue];
    self.initNovel= [call.arguments[@"initNovel"] boolValue];
    self.accessIDFA= YES;
//    self.accessIDFA= [call.arguments[@"allowAccessID"] boolValue];
    self.onTeenMode= [call.arguments[@"isTeenagerMode"] boolValue];
    self.onlyICPNumber=[call.arguments[@"isOnlyICPNumber"] boolValue];
    NSString *settingFile=call.arguments[@"settingFile"];
    NSString *configPath=[[NSBundle mainBundle] pathForResource:settingFile ofType:@"json"];
    
    // 根据初始化标记决定初始化顺序
    if (self.initSkit) {
        [self initSkitSdk:configPath result:result];
    } else if (self.initVideo) {
        [self initVideoSdk:configPath result:result];
    } else if (self.initNovel) {
        [self initNovelSdk:configPath result:result];
    }
}

// 初始化短剧
- (void) initSkitSdk:(NSString*) configPath result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    DJXConfig *config = [DJXConfig new];
    config.authorityDelegate = self;
#if DEBUG
    config.logLevel = DJXSDKLogLevelDebug;
#endif
    [DJXManager initializeWithConfigPath:configPath config:config];
    [DJXManager startWithCompleteHandler:^(BOOL initStatus, NSDictionary *userInfo) {
        if (initStatus) {
            NSLog(@"🎬短剧==> 初始化成功");
            // 小视频则继续初始化，否则
            if (self.initVideo) {
                [self initVideoSdk:configPath result:result];
            }else if (self.initNovel) {
                [self initNovelSdk:configPath result:result];
            } else {
                result(@(YES));
            }
        } else {
            NSLog(@"🎬短剧==> 初始化失败:%@", userInfo);
            if([userInfo.allKeys containsObject:@"msg"]){
                NSLog(@"🎬短剧==> 初始化失败:%@",userInfo[@"msg"]);
            }
            result(@(NO));
        }
    }];
}

// 初始化小视频
- (void) initVideoSdk:(NSString*) configPath result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    LCDConfig *config = [LCDConfig new];
#if DEBUG
    config.logLevel = LCDSDKLogLevelDebug;
#endif
    [LCDManager initializeWithConfigPath:configPath config:config];
    [LCDManager startWithCompleteHandler:^(LCDINITStatus initStatus, NSDictionary *userInfo) {
        if (initStatus == LCDINITStatus_success) {
            NSLog(@"📱小视频==> 初始化成功");
            // 短剧则继续初始化，否则
            if (self.initNovel) {
                [self initNovelSdk:configPath result:result];
            } else {
                result(@(YES));
            }
        } else {
            NSLog(@"📱小视频==> 初始化失败:%@", userInfo[@"msg"]);
            result(@(NO));
        }
    }];
}

// 添加短故事初始化方法
- (void) initNovelSdk:(NSString*) configPath result:(FlutterResult) result {
    NSLog(@"%s",__FUNCTION__);
    MNConfig *config = [MNConfig new];
    config.authorityDelegate = self;
#if DEBUG
    config.logLevel = DJXSDKLogLevelDebug;
#endif
    
    [MNManager initializeWithConfigPath:configPath config:config];
    [MNManager startWithCompleteHandler:^(BOOL initStatus, NSDictionary *userInfo) {
        if (initStatus) {
            NSLog(@"📚短故事==> 初始化成功");
            result(@(YES));
        } else {
            NSLog(@"📚短故事==> 初始化失败:%@", userInfo[@"msg"]);
            result(@(NO));
        }
    }];
}

#pragma mark - 隐私合规开关
// 是否允许获取idfa
- (BOOL)allowAccessIDFA{
    return self.accessIDFA;
}

// 是否打开青少年模式
- (BOOL)turnOnTeenMode{
    return self.onTeenMode;
}

// 仅展示有备案号的内容
- (BOOL)isOnlyICPNumber{
    return self.onlyICPNumber;
}

// 打开剧场页面 showTheaterPage
- (void) showTheaterPage:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    BOOL showBackBtn=[call.arguments[@"showBackBtn"] boolValue];
    BOOL showPageTitle=[call.arguments[@"showPageTitle"] boolValue];
    BOOL showChangeBtn=[call.arguments[@"showChangeBtn"] boolValue];
    int detailFree=[call.arguments[@"detailFree"] intValue];
    int unlockCount=[call.arguments[@"unlockCount"] intValue];
    
    DJXDrawVideoViewController *vc = [[DJXDrawVideoViewController alloc] initWithConfigBuilder:^(DJXDrawVideoVCConfig * _Nonnull config) {
        DJXPlayletConfig *playletConfig = [DJXPlayletConfig new];
        playletConfig.playletUnlockADMode = DJXPlayletUnlockADMode_Common;
        playletConfig.freeEpisodesCount = detailFree;
        playletConfig.unlockEpisodesCountUsingAD = unlockCount;
        config.playletConfig = playletConfig;
        config.drawVCTabOptions = DJXDrawVideoVCTabOptions_theater | DJXDrawVideoVCTabOptions_playlet_feed;
        config.viewSize = CGSizeMake(LCSScreenWidth, LCSScreenHeight);
        config.showCloseButton = showBackBtn;
        config.shouldHideTabBarView= !showPageTitle;
        config.hiddenPlayletEnterView=!showChangeBtn;
    }];
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    [self.rootCon presentViewController:vc animated:YES completion:^{}];
}

// 打开剧场滑滑溜页面 showDrawPage
- (void) showDrawPage:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    BOOL showBackBtn=[call.arguments[@"showBackBtn"] boolValue];
    BOOL hideInfo=[call.arguments[@"hideInfo"] boolValue];
    BOOL hideEnter=[call.arguments[@"hideEnter"] boolValue];
    int detailFree=[call.arguments[@"detailFree"] intValue];
    int unlockCount=[call.arguments[@"unlockCount"] intValue];
    
    DJXDrawVideoViewController *vc = [[DJXDrawVideoViewController alloc] initWithConfigBuilder:^(DJXDrawVideoVCConfig * _Nonnull config) {
        DJXPlayletConfig *playletConfig = [DJXPlayletConfig new];
        playletConfig.playletUnlockADMode = DJXPlayletUnlockADMode_Common;
        playletConfig.freeEpisodesCount = detailFree;
        playletConfig.unlockEpisodesCountUsingAD = unlockCount;
        config.playletConfig = playletConfig;
        config.drawVCTabOptions = DJXDrawVideoVCTabOptions_playlet_feed;
        config.viewSize = CGSizeMake(LCSScreenWidth, LCSScreenHeight);
        config.showCloseButton = showBackBtn;
        config.shouldHideTabBarView= hideInfo;
        config.hiddenPlayletEnterView= hideEnter;
    }];
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    [self.rootCon presentViewController:vc animated:YES completion:^{}];
}

// 显示短剧详情页
- (void) showDetailPage:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    int detailId=[call.arguments[@"id"] intValue];
    int index=[call.arguments[@"index"] intValue];
    int groupId=[call.arguments[@"groupId"] intValue];
    int detailFree=[call.arguments[@"detailFree"] intValue];
    int unlockCount=[call.arguments[@"unlockCount"] intValue];
    BOOL hideTopInfo=[call.arguments[@"hideTopInfo"] boolValue];
    int setTopOffset=[call.arguments[@"setTopOffset"] intValue];
    BOOL hideBack=[call.arguments[@"hideBack"] boolValue];
    BOOL hideBottomInfo=[call.arguments[@"hideBottomInfo"] boolValue];
    BOOL hideRewardDialog=[call.arguments[@"hideRewardDialog"] boolValue];
    BOOL hideMore=[call.arguments[@"hideMore"] boolValue];
    BOOL hideCellularToast=[call.arguments[@"hideCellularToast"] boolValue];
    
    // 创建短剧详情
    DJXDrawVideoViewController *vc = [[DJXDrawVideoViewController alloc] initWithConfigBuilder:^(DJXDrawVideoVCConfig * _Nonnull config) {
        DJXPlayletConfig *playletConfig = [DJXPlayletConfig new];
        playletConfig.skitId = detailId;
        playletConfig.episode = index;
        playletConfig.groupId = groupId;
        playletConfig.playletUnlockADMode = DJXPlayletUnlockADMode_Common;
        playletConfig.freeEpisodesCount = detailFree;
        playletConfig.unlockEpisodesCountUsingAD = unlockCount;
        playletConfig.hideTopInfo = hideTopInfo;
        playletConfig.fromTopMargin = setTopOffset;
        playletConfig.hideBackButton=hideBack;
        playletConfig.hideBottomInfo=hideBottomInfo;
        playletConfig.hideRewardDialog = hideRewardDialog;
        playletConfig.hideMoreButton = hideMore;
        playletConfig.hideCellularToast = hideCellularToast;
        
        config.playletConfig = playletConfig;
        config.drawVCTabOptions = DJXDrawVideoVCTabOptions_playlet;
        config.viewSize = CGSizeMake(LCSScreenWidth, LCSScreenHeight);
        config.shouldHideTabBarView= hideTopInfo;
        config.customAppear = YES;
    }];
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    [self.rootCon presentViewController:vc animated:YES completion:^{}];
}

// 通过个性化推荐获取所有短剧
- (void) requestAllDramaByRecommend:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    int page=[call.arguments[@"page"] intValue];
    int count=[call.arguments[@"count"] intValue];
    [[DJXPlayletManager shareInstance] requestRecommendedPlayletListPage:page num:count success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList, NSDictionary<NSString *,NSObject *> * _Nonnull info) {
        NSLog(@"requestCategoryPlayletLisWithSearchWord success");
        NSMutableArray *list= [self playletToList:playletList];
        result(list);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestCategoryPlayletLisWithSearchWord request fail");
        result(@"[]");
    }];
}

// 获取所有短剧
- (void) requestAllDrama:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    int page=[call.arguments[@"page"] intValue];
    int count=[call.arguments[@"count"] intValue];
    int order=[call.arguments[@"order"] intValue];
    [[DJXPlayletManager shareInstance] requestAllPlayletListPage:page num:count order:order success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList, NSDictionary<NSString *,NSObject *> * _Nonnull info) {
        NSLog(@"requestAllPlayletListPage success");
        NSMutableArray *list= [self playletToList:playletList];
        result(list);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestAllPlayletListPage request fail");
        result(@"[]");
    }];
}

// 获取短剧根据 ids
- (void) requestDrama:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    NSArray *list = call.arguments[@"dramaIds"];
    if (![list isKindOfClass:[NSArray class]]) {
        result(@"[]");
        return;
    }
    [[DJXPlayletManager shareInstance] requestPlayletListWithPlayletId:list success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList) {
        NSLog(@"requestPlayletListWithPlayletId success");
        NSMutableArray *list= [self playletToList:playletList];
        result(list);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestPlayletListWithPlayletId request fail");
        result(@"[]");
    }];
}

// 获取短剧列表根据分类
- (void) requestDramaByCategory:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    NSString *category=call.arguments[@"category"];
    int page=[call.arguments[@"page"] intValue];
    int count=[call.arguments[@"count"] intValue];
    int order=[call.arguments[@"order"] intValue];
    [[DJXPlayletManager shareInstance] requestCategoryPlayletLisWithCategory:category page:page num:count order:order success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList) {
            NSLog(@"requestCategoryPlayletLisWithCategory success");
            NSMutableArray *list= [self playletToList:playletList];
            result(list);
        } failure:^(NSError * _Nonnull error) {
            NSLog(@"requestCategoryPlayletLisWithCategory request fail");
            result(@"[]");
        }];
}

// 获取短剧分类列表
- (void) requestDramaCategoryList:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    [[DJXPlayletManager shareInstance] requestCategoryList:^(NSArray<NSString *> * _Nonnull categoryList) {
        NSLog(@"requestCategoryList success");
        result(categoryList);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestCategoryList request fail");
        result(@"[]");
    }];
}

// 搜索短剧
- (void) searchDrama:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    NSString *query=call.arguments[@"query"];
    BOOL isFuzzy =[call.arguments[@"isFuzzy"] boolValue];
    int page=[call.arguments[@"page"] intValue];
    int count=[call.arguments[@"count"] intValue];
    [[DJXPlayletManager shareInstance] requestCategoryPlayletLisWithSearchWord:query isFuzzy:isFuzzy page:page num:count success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList, BOOL hasMore) {
        NSLog(@"requestCategoryPlayletLisWithCategory success");
        NSMutableArray *list= [self playletToList:playletList];
        result(list);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestCategoryPlayletLisWithCategory request fail");
        result(@"[]");
    }];
}


// 获取短剧历史记录
- (void) getDramaHistory:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    int page=[call.arguments[@"page"] intValue];
    int count=[call.arguments[@"count"] intValue];
    [[DJXPlayletManager shareInstance] requestPlayletHistoryListWithPage:page num:count success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList) {
        NSLog(@"requestPlayletHistoryListWithPage success");
        NSMutableArray *list= [self playletToList:playletList];
        result(list);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestPlayletHistoryListWithPage request fail");
        result(@"[]");
    }];
}

// 清除短剧历史记录
- (void) clearDramaHistory:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    [[DJXPlayletManager shareInstance] requestPlayletHistoryCleanWithCompletion:^{
        NSLog(@"requestPlayletHistoryCleanWithCompletion success");
        result(@(YES));
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestPlayletHistoryCleanWithCompletion request fail");
        result(@(NO));
    }];
}

// 收藏短剧
- (void) favorDrama:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    int dramaId=[call.arguments[@"id"] intValue];
    BOOL state =[call.arguments[@"state"] boolValue];
    if(state){
        [[DJXPlayletManager shareInstance] collectShortplay:dramaId success:^{
                NSLog(@"collectShortplay success");
                result(@(YES));
        } failure:^(NSError * _Nonnull error) {
                NSLog(@"collectShortplay request fail");
                result(@(NO));
        }];
    }else{
        [[DJXPlayletManager shareInstance] cancelCollectShortplay:dramaId success:^{
            NSLog(@"cancelCollectShortplay success");
            result(@(YES));
        } failure:^(NSError * _Nonnull error) {
            NSLog(@"cancelCollectShortplay request fail");
            result(@(NO));
        }];
    }
    

}

// 获取短剧收藏列表
- (void) getFavorList:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    int page=[call.arguments[@"page"] intValue];
    int count=[call.arguments[@"count"] intValue];
    [[DJXPlayletManager shareInstance] requestCollectionList:page pageSize:count success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList, BOOL hasMore) {
        NSLog(@"requestCollectionList success");
        NSMutableArray *list= [self playletToList:playletList];
        result(list);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestCollectionList request fail");
        result(@"[]");
    }];
}

// 获取短剧解锁状态
- (void) getEpisodesStatus:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    int dramaId = [call.arguments[@"id"] intValue];
    int freeSet = [call.arguments[@"freeSet"] intValue];
    [[DJXPlayletManager shareInstance] requestPlayletDetailsUnlockInfo:dramaId freeEpisodeCount:freeSet success:^(NSArray<NSNumber *> * _Nonnull unlockStatusArray) {
        NSMutableArray *list = [NSMutableArray array];
        [unlockStatusArray enumerateObjectsUsingBlock:^(NSNumber * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            BOOL isLocked = ![obj boolValue];
            NSDictionary *data = @{
                @"index": @(idx),
                @"isLocked": @(isLocked)
            };
            [list addObject:data];
        }];
        result(list);
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"requestPlayletUnlockStatusWithPlayletId request fail");
        result(@"[]");
    }];
}

// 设置短剧全局的倍速
- (void) setGlobalSpeedPlay:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    double speed = [call.arguments[@"speed"] doubleValue];
    [[DJXPlayletManager shareInstance] setGlobalPlaySpeed:speed];
    result(@(YES));
}

//===========
// 用户相关接口
//===========

// 获取签名信息
- (void) getSignString:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    NSString *key=call.arguments[@"key"];
    NSString *nonce=call.arguments[@"nonce"];
    int time=[call.arguments[@"time"] intValue];
    NSDictionary<NSString *, NSString *> *params=call.arguments[@"params"];
    // 签名字符串
    NSString *signStr = [DJXManager getSignWithPaySecretKey:key nonce:nonce timeStamp:time params:params];
    result(signStr);
}

// 登录
- (void) login:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    NSString *params=call.arguments[@"params"];
    [DJXManager loginWithParamsString:params completionBlock:^(BOOL loginStatus, NSDictionary * _Nonnull userInfo) {
        result(@(loginStatus));
    }];
}

// 是否已登录
- (void) isLogin:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    BOOL isLogin=[DJXManager isLogin];
    result(@(isLogin));
}

// 退出登录
- (void) logout:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSLog(@"%s",__FUNCTION__);
    [DJXManager logoutWithCompletionBlock:^(BOOL logoutStatus, NSDictionary * _Nonnull userInfo) {
        result(@(logoutStatus));
    }];
}

// 对象转为 Map
- (NSDictionary *)playletToMap:(DJXPlayletInfoModel *) model{
    NSDictionary *data=@{@"id":[NSNumber numberWithInteger:model.shortplay_id],
                         @"groupId":[NSNumber numberWithInteger:model.group_id],
                         @"title":model.title ?: @"",
                         @"coverImage":model.cover_image ?: @"",
                         @"status":[NSNumber numberWithInteger:model.status],
                         @"total":[NSNumber numberWithInteger:model.total],
                         @"index":[NSNumber numberWithInteger:model.current_episode],
                         @"type":model.category_name ?: @"",
                         @"typeId":[NSNumber numberWithInteger:model.category_id],
                         @"desc":model.desc ?: @"",
                         @"createTime":[NSNumber numberWithInteger:model.create_time],
                         @"actionTime":[NSNumber numberWithInteger:model.action_time],
                         @"freeSet":[NSNumber numberWithInteger:model.unlock_index],
                         @"lockSet":[NSNumber numberWithInteger:model.unlock_index],
                         @"icpNumber":model.icp_number ?: @"",
                         @"isFavor":model.favorite_state ==1 ?@(YES):@(NO),
                         @"favoriteTime":[NSNumber numberWithInteger:model.favorite_time],
                         @"favoriteCount":[NSNumber numberWithInteger:model.favorite_count],
                         @"levelLabel":[NSNumber numberWithInteger:model.level_label],
                         @"isPotential":[NSNumber numberWithBool:model.is_potential],
                         @"duration":[NSNumber numberWithInteger:model.video_duration]};
    return  data;
}

// 对象列表转换为序列化列表
- (NSMutableArray *)playletToList:(NSArray<DJXPlayletInfoModel *>*) playletList{
    NSMutableArray *list= [[NSMutableArray alloc] init];
    for (DJXPlayletInfoModel *model in playletList) {
        NSLog(@"playletToList success:%@",model.title);
        [list addObject:[self playletToMap:model]];
    }
    return list;
}

// 打开短故事阅读页面
- (void)openNovelReaderPage:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s", __FUNCTION__);
    FACPNovelReadViewController *readVC = [[FACPNovelReadViewController alloc] initWithMethodCall:call];
    readVC.methodChannel=novelChannel;

    UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:readVC];
    navigationController.modalPresentationStyle = UIModalPresentationFullScreen; // 全屏模式

    NSLog(@"Opening mini story with params: %@, UINavigationController: %@", readVC, navigationController);
    
    [self.rootCon presentViewController:navigationController animated:NO completion:nil];
    // 直接返回成功
    result(@(YES));
}

// 辅助方法：获取当前顶层视图控制器（同上）
- (UIViewController *)getTopViewController {
    UIViewController *rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
    if ([rootViewController isKindOfClass:[UINavigationController class]]) {
        return (UINavigationController *)rootViewController;
    }
    return rootViewController;
}

#pragma mark - 短故事类目相关


// 获取短故事类目列表
- (void)getStoryCategoryList:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    
    [[MNStoryManager shareInstance] requestCategoryList:^(BOOL success, NSArray<MNStoryCategoryListItemModel *> * _Nonnull categoryList) {
        if (success) {
            NSMutableArray *list = [NSMutableArray array];
            for (MNStoryCategoryListItemModel *category in categoryList) {
                [list addObject:[self categoryToMap:category]];
            }
            result(list);
        } else {
            result(@[]);
        }
    }];
}

#pragma mark - 短故事内容相关

// 搜索短故事
- (void)searchStory:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSString *query = call.arguments[@"query"];
    BOOL isFuzzy = [call.arguments[@"isFuzzy"] boolValue];
    NSInteger page = [call.arguments[@"page"] integerValue];
    NSInteger count = [call.arguments[@"count"] integerValue];
    
    [[MNStoryManager shareInstance] requestStoryListWithSearchWord:query 
                                                         isFuzzy:isFuzzy 
                                                            page:page 
                                                             num:count 
                                                      completion:^(BOOL success, NSArray<MNStoryInfoModel *> * _Nonnull storyList, BOOL hasMore) {
        if (success) {
            result([self storyListToArray:storyList]);
        } else {
            result(@[]);
        }
    }];
}

// 根据ID获取短故事
- (void)requestStoryByIds:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSArray *ids = call.arguments[@"ids"];
    NSMutableArray *stringIds = [NSMutableArray array];
    for (NSNumber *idNum in ids) {
        [stringIds addObject:[idNum stringValue]];
    }
    
    [[MNStoryManager shareInstance] requestStoryListWithBookId:stringIds completion:^(BOOL success, NSArray<MNStoryInfoModel *> * _Nonnull storyList) {
        if (success) {
            result([self storyListToArray:storyList]);
        } else {
            result(@[]);
        }
    }];
}

// 获取短故事精选列表
- (void)requestStoryFeed:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSInteger order = [call.arguments[@"order"] integerValue];
    NSInteger page = [call.arguments[@"page"] integerValue];
    NSInteger count = [call.arguments[@"count"] integerValue];
    
    [[MNStoryManager shareInstance] requestAllStoryListPage:page 
                                                      num:count 
                                                    order:order 
                                               completion:^(BOOL success, NSArray<MNStoryInfoModel *> * _Nonnull storyList, NSDictionary<NSString *,NSObject *> *totalDict, BOOL hasMore) {
        if (success) {
            result([self storyListToArray:storyList]);
        } else {
            result(@[]);
        }
    }];
}

// 根据类目请求短故事
- (void)requestStoryByCategory:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSInteger categoryId = [call.arguments[@"id"] integerValue];
    NSInteger order = [call.arguments[@"order"] integerValue];
    NSInteger page = [call.arguments[@"page"] integerValue];
    NSInteger count = [call.arguments[@"count"] integerValue];
    
    [[MNStoryManager shareInstance] requestCategoryStoryListWithCategoryId:categoryId 
                                                                    page:page 
                                                                     num:count 
                                                                   order:order 
                                                              completion:^(BOOL success, NSArray<MNStoryInfoModel *> * _Nonnull storyList) {
        if (success) {
            result([self storyListToArray:storyList]);
        } else {
            result(@[]);
        }
    }];
}

#pragma mark - 用户交互相关

// 获取阅读历史
- (void)getStoryHistory:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSInteger page = [call.arguments[@"page"] integerValue];
    NSInteger count = [call.arguments[@"count"] integerValue];
    
    [[MNStoryManager shareInstance] requestHistoryStoryList:page 
                                                      num:count 
                                               completion:^(BOOL success, NSArray<MNStoryInfoModel *> * _Nonnull storyList) {
        if (success) {
            result([self storyListToArray:storyList]);
        } else {
            result(@[]);
        }
    }];
}

// 收藏短故事
- (void)storyFavorite:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSInteger storyId = [call.arguments[@"id"] integerValue];
    
    [[MNStoryManager shareInstance] requestCollectStory:storyId completeHandler:^(NSError * _Nullable error) {
        result(error == nil?@(YES):@(NO));
    }];
}

// 取消收藏短故事
- (void)storyFavoriteCancel:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSInteger storyId = [call.arguments[@"id"] integerValue];
    
    [[MNStoryManager shareInstance] requestCancelCollectStory:storyId completeHandler:^(NSError * _Nullable error) {
        result(error == nil?@(YES):@(NO));
    }];
}

// 获取收藏列表
- (void)getStoryFavorite:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    NSInteger page = [call.arguments[@"page"] integerValue];
    NSInteger count = [call.arguments[@"count"] integerValue];
    
    [[MNStoryManager shareInstance] requestStoryCollectionList:page 
                                                         num:count 
                                             completeHandler:^(BOOL success, NSArray<MNStoryInfoModel *> * _Nonnull storyList, NSDictionary<NSString *,NSObject *> *totalDict, BOOL hasMore) {
        if (success) {
            result([self storyListToArray:storyList]);
        } else {
            result(@[]);
        }
    }];
}

#pragma mark - Helper Methods

// 将短故事列表转换为数组
- (NSArray *)storyListToArray:(NSArray<MNStoryInfoModel *> *)storyList {
    NSMutableArray *list = [NSMutableArray array];
    for (MNStoryInfoModel *model in storyList) {
        // 时间戳转字符串
        NSString *favoriteTimeStr = @"";
        if (model.favorite_time > 0) {
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.favorite_time];
            NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
            [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
            favoriteTimeStr = [formatter stringFromDate:date];
        }
        
        NSDictionary *data = @{
            // 基本信息
            @"id": @(model.book_id),
            @"title": model.title ?: @"",
            @"desc": model.desc ?: @"",
            @"content": model.content_truncation ?: [NSNull null],  // 可选字段
            @"author": model.author ?: @"",
            @"coverImage": model.cover_image ?: @"",
            @"imageType": @(model.cover_type),
            
            // 分类信息
            @"categoryId": @(model.category_id),
            @"categoryName": model.category_name ?: @"",
            
            // 章节信息
            @"total": @(model.total),
            @"createTime": @(model.create_time),
            @"index": @(model.index),
            @"progress": @(model.progress),
            
            // 统计信息
            @"statsCount": @(model.stats_count),
            
            // 收藏信息
            @"isFavorite": model.favorite_state == 1 ? @(YES) : @(NO),
            @"favoriteTime": favoriteTimeStr,
            @"actionTime": @(model.action_time)
        };
        [list addObject:data];
    }
    return list;
}


// 将类目对象转换为字典
- (NSDictionary *)categoryToMap:(id)category {
    NSInteger categoryId;
    NSString *name;
    NSInteger level;
    NSArray *childrenArray;
    
    // 处理不同类型的类目对象
    if ([category isKindOfClass:[MNStoryCategoryListItemModel class]]) {
        MNStoryCategoryListItemModel *model = (MNStoryCategoryListItemModel *)category;
        categoryId = model.category_id;
        name = model.name;
        level = model.level;
        childrenArray = model.children;
    } else if ([category isKindOfClass:[MNStoryCategoryItemModel class]]) {
        MNStoryCategoryItemModel *model = (MNStoryCategoryItemModel *)category;
        categoryId = model.category_id;
        name = model.name;
        level = model.level;
        childrenArray = @[];
    } else {
        return @{};
    }
    
    // 递归处理子类目
    NSMutableArray *children = [NSMutableArray array];
    for (id child in childrenArray) {
        [children addObject:[self categoryToMap:child]];
    }
    
    // 构建与 Flutter 侧 NovelCategory 对应的字典
    return @{
        @"id": @(categoryId),
        @"name": name ?: @"",
        @"level": @(level),
        @"children": children,
    };
}

@end
