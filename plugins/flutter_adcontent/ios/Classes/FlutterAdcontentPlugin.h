#import <Flutter/Flutter.h>
#import "FACPPangrowth.h"
#import "LCSMacros.h"

@interface FlutterAdcontentPlugin : NSObject<FlutterPlugin,DJXAuthorityConfigDelegate>

// 跟控制器
@property (strong,nonatomic) UIViewController *rootCon;
// 是否初始化短剧
@property BOOL initSkit;
// 是否初始化小视频
@property BOOL initVideo;
// 是否初始化短故事
@property BOOL initNovel;
// 是否允许获取idfa
@property BOOL accessIDFA;
// 是否打开青少年模式
@property BOOL onTeenMode;
// 是否展示只有备案号的内容
@property BOOL onlyICPNumber;

// 短剧详情 ViewId
extern NSString *const kFACPDramaDetailViewId;
// 剧场 ViewId
extern NSString *const kFACPTheaterViewId;
// 小视频 ViewId
extern NSString *const kFACPVideoViewId;
// 短故事 View
extern NSString *const kFACPNovelStoryViewId;
extern NSString *const kFACPNovelStoryChannel;

// 短剧转换为 map
- (NSDictionary *)playletToMap:(DJXPlayletInfoModel *) model;
// 短剧列表转换为 List
- (NSMutableArray *)playletToList:(NSArray<DJXPlayletInfoModel *>*) playletList;



@end
