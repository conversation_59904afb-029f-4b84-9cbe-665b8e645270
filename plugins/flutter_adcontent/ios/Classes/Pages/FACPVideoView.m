//
//  FACPVideoView.m
//  flutter_adcontent
//
//  Created by zero on 2024/8/24.
//

#import "FACPVideoView.h"

@interface FACPVideoView()<FlutterPlatformView,LCDDrawVideoViewControllerDelegate>
@property (strong,nonatomic) FlutterMethodCall *methodCall;
@property (strong,nonatomic) FlutterMethodChannel *methodChannel;
@property (strong,nonatomic) UIView *contentView;
@property (strong,nonatomic) LCDDrawVideoViewController *vc;
@property (strong,nonatomic) LCDGridVideoViewController *gvc;
@property double width;
@property double height;

@end

@implementation FACPVideoView

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger
                       plugin:(FlutterAdcontentPlugin*) plugin{
    if (self = [super init]) {
        self.plugin=plugin;
        // 设置宽高，初始化容器 View
        self.width=[args[@"width"] floatValue];
        self.height=[args[@"height"] floatValue];
        self.contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.width, self.height)];
        self.contentView.backgroundColor = [UIColor redColor];
        self.contentView.clipsToBounds = YES;// 重要超过区域剪裁掉，不然会遮挡
        self.methodChannel = [FlutterMethodChannel methodChannelWithName:[NSString stringWithFormat:@"%@/%lli",kFACPVideoViewId,viewId] binaryMessenger:messenger];
        self.methodCall=[FlutterMethodCall methodCallWithMethodName:@"FACPVideoView" arguments:args];
        // 设置消息处理器
        __weak typeof(self) weakSelf = self;
        [self.methodChannel setMethodCallHandler:^(FlutterMethodCall *call, FlutterResult result) {
            [weakSelf handleMethodCall:call result:result];
        }];
        [self loadView:self.methodCall];
    }
    return self;
}

- (UIView*)view {
    return self.contentView;
}
// 添加到 ContentView 中
- (void)addContentView:(UIViewController *)vc {
    vc.view.frame = self.contentView.bounds;
    // 将 vc.view 添加到 contentView 中
    [self.contentView addSubview:vc.view];
    
    // 获取当前根视图控制器
    UIViewController *rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
    // 将 UIViewController 添加为子视图控制器
    [rootViewController addChildViewController:vc];
    [vc didMoveToParentViewController:rootViewController];
}

// 加载 View
- (void)loadView:(FlutterMethodCall *)call{
    int channelType = [call.arguments[@"channelType"] intValue];
    int contentType = [call.arguments[@"contentType"] intValue];
    int style=[call.arguments[@"style"] intValue];
    BOOL hideChannelName=[call.arguments[@"hideChannelName"] boolValue];
    BOOL showGuide=[call.arguments[@"showGuide"] boolValue];
    int bottomOffset=[call.arguments[@"bottomOffset"] intValue];
    // 获取 contentView 的大小
    CGSize contentViewSize = self.contentView.bounds.size;
    // 全屏沉浸式
    if (style == 2) {
        // 解析频道类型
        LCDDrawVideoVCTabOptions drawVCTabOptions=LCDDrawVideoVCTabOptions_recommand;
        if (channelType==1) {
            drawVCTabOptions=LCDDrawVideoVCTabOptions_recommand;
        } else if (channelType==2)  {
            drawVCTabOptions=LCDDrawVideoVCTabOptions_following;
        } else {
            drawVCTabOptions=LCDDrawVideoVCTabOptions_recommand|LCDDrawVideoVCTabOptions_following;
        }
        // 解析内容类型
        NSMutableArray<LCDDrawFeatureString> *featureArray = [NSMutableArray array];
        if (contentType==1) {
            [featureArray addObject:kLCDDrawSingleTinyVideo];
        } else if (contentType==2)  {
            [featureArray addObject:kLCDDrawWebcastonly];
        } else {
            [featureArray addObject:kLCDDrawSingleTinyVideo];
            [featureArray addObject:kLCDDrawWebcastonly];
        }
        
        LCDDrawVideoViewController *vc = [[LCDDrawVideoViewController alloc] initWithConfigBuilder:^(LCDDrawVideoVCConfig * _Nonnull config) {
            config.customAppear = YES;
            config.viewSize = contentViewSize;
            config.shouldHideTabBarView = hideChannelName;
            config.drawVCTabOptions = drawVCTabOptions;
            config.featureValuesArr = featureArray;
            config.showCloseButton = NO;
            config.hiddenGuideGeste = !showGuide;
            config.out_bottomOffset = bottomOffset;
            config.delegate = self;
            }];
        // 将 UIViewController 的视图添加到这个 UIView 中
        self.vc = vc;
        // 添加到 contentView 中
        [self addContentView:vc];
    } else {
        LCDGridVideoViewController *gvc = [[LCDGridVideoViewController alloc] initWithConfigBuilder:^(LCDGridVideoVCConfig * _Nonnull config) {
            config.gridVideoVCType = style==3?LCDGridVideoVCType_grid:LCDGridVideoVCType_waterfall;
            config.customAppear = YES;
            config.viewSize = contentViewSize;
//            config.delegate = self;
//            config.adDelegate = self;
        }];
        self.gvc = gvc;
        // 添加到 contentView 中
        [self addContentView:gvc];
    }

    
}

- (void)dispose {
    // 当 Flutter 视图被销毁时调用
    [self cleanupViewController];
}


- (void)cleanupViewController {
    // 确保 vc 不为空
    if (self.vc) {
        // 视图消失
        [self.vc drawVideoViewControllerDidDisappear];
        // 1. 从父视图中移除视图
        [self.vc.view removeFromSuperview];
        
        // 2. 从父视图控制器中移除子视图控制器
        [self.vc willMoveToParentViewController:nil];
        [self.vc removeFromParentViewController];
        
        // 3. 清理引用
        self.vc = nil;
    }
}

#pragma mark 事件通讯方法

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    if ([call.method isEqualToString:@"pause"]) {
        // 暂停
        if (self.vc) {
            [self.vc pauseCurrentVideo];
        }
        result(@(YES));
    } else if ([call.method isEqualToString:@"resume"]) {
        // 继续
        if (self.vc) {
            [self.vc playCurrentVideo];
        }
        result(@(YES));
    } else if ([call.method isEqualToString:@"dispose"]) {
        // 销毁
        [self dispose];
        result(@(YES));
    } else {
        result(FlutterMethodNotImplemented);
    }
}

#pragma mark LCDDrawVideoViewControllerDelegate

- (void)drawVideoCurrentVideoChanged:(UIViewController *)viewController event:(LCDEvent *)event{
    long position =  [event.params[@"position"] longValue];
    NSLog(@"drawVideoCurrentVideoChanged position:%ld",position);
    [self.methodChannel invokeMethod:@"onDPPageChange" arguments: @(position)];
}

- (void)drawVideoCloseButtonClicked:(UIViewController *)viewController {
    NSLog(@"drawVideoCurrentVideoChanged close button clicked");
}

- (void)drawVideoCurrentPageChanged:(UIViewController *)viewController event:(LCDEvent *)event {
    NSLog(@"drawVideoCurrentVideoChanged current page changed - params:%@", event.params);
}

#pragma mark - LCDPlayerCallBackProtocol

- (void)drawVideoStartPlay:(UIViewController *)viewController event:(LCDEvent *)event {
    NSLog(@"drawVideoCurrentVideoChanged draw video start play - group_id:%ld, params:%@", event.group_id, event.params);
    [self.methodChannel invokeMethod:@"onDPVideoPlay" arguments: @{}];
}

- (void)drawVideoPause:(UIViewController *)viewController event:(LCDEvent *)event {
    NSLog(@"drawVideoCurrentVideoChanged draw video pause - group_id:%ld, params:%@", event.group_id, event.params);
    [self.methodChannel invokeMethod:@"onDPVideoPause" arguments: @{}];
}

- (void)drawVideoContinue:(UIViewController *)viewController event:(LCDEvent *)event {
    NSLog(@"drawVideoCurrentVideoChanged draw video continue - group_id:%ld, params:%@", event.group_id, event.params);
    [self.methodChannel invokeMethod:@"onDPVideoContinue" arguments: @{}];
}

- (void)drawVideoPlayCompletion:(UIViewController *)viewController event:(LCDEvent *)event {
    NSLog(@"drawVideoCurrentVideoChanged draw video play completion - group_id:%ld, params:%@", event.group_id, event.params);
    [self.methodChannel invokeMethod:@"onDPVideoCompletion" arguments: @{}];
}

- (void)drawVideoOverPlay:(UIViewController *)viewController event:(LCDEvent *)event {
    NSLog(@"drawVideoCurrentVideoChanged draw video over play - group_id:%ld, params:%@", event.group_id, event.params);
    [self.methodChannel invokeMethod:@"onDPVideoOver" arguments: @{}];
}

@end
