//
//  FACPTheaterView.h
//  flutter_adcontent
//
//  Created by zero on 2024/8/16.
//

#import <Foundation/Foundation.h>
#import "FlutterAdcontentPlugin.h"

NS_ASSUME_NONNULL_BEGIN

@interface FACPTheaterView : NSObject<FlutterPlatformView>
@property (strong,nonatomic,nullable) FlutterAdcontentPlugin *plugin;
- (nonnull instancetype)initWithFrame:(CGRect)frame
                       viewIdentifier:(int64_t)viewId
                            arguments:(id _Nullable)args
                      binaryMessenger:(NSObject<FlutterBinaryMessenger>* _Nullable)messenger plugin:(FlutterAdcontentPlugin* _Nullable) plugin;

- (nonnull UIView*)view;
@end

NS_ASSUME_NONNULL_END
