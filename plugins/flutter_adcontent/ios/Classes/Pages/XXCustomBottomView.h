//
//  XXCustomBottomView.h
//  flutter_adcontent
//
//  Created by 非大鱼 on 2025/8/10.
//

#import <UIKit/UIKit.h>
#import "FlutterAdcontentPlugin.h"
NS_ASSUME_NONNULL_BEGIN

@interface XXCustomBottomView : UIView
@property(nonatomic,copy)dispatch_block_t clickThumbBlock;
@property(nonatomic,copy)dispatch_block_t pushDetailBlock;
@property(nonatomic,copy)dispatch_block_t chooseBlock;
@property(nonatomic,copy)dispatch_block_t favBlock;

-(instancetype)initWithFrame:(CGRect)frame;

/// 根据数据更新UI
/// @param playletInfoModel 答题的数据
- (void)updateWithData:(DJXPlayletInfoModel *)playletInfoModel;

/// 根据数据更新UI
/// @param playletInfoModel 答题的数据
- (void)updateWithData:(DJXPlayletInfoModel *)playletInfoModel favStatue:(BOOL)favState;

@end

NS_ASSUME_NONNULL_END
