//
//  FACPNativeViewFactory.m
//  flutter_adcontent
//
//  Created by zero on 2024/8/16.
//

#import "FACPNativeViewFactory.h"
#import "FACPTheaterView.h"
#import "FACPDramaDetailView.h"
#import "FACPVideoView.h"
#import "FACPNovelStoryView.h"

@implementation FACPNativeViewFactory


- (instancetype)initWithViewName:(NSString *)viewName withMessenger:(NSObject<FlutterBinaryMessenger> *)messenger withPlugin:(FlutterAdcontentPlugin *)plugin{
    self = [super init];
    if (self) {
        self.viewName = viewName;
        self.messenger = messenger;
        self.plugin = plugin;
    }
    return self;
}

- (NSObject<FlutterMessageCodec>*)createArgsCodec {
    return [FlutterStandardMessageCodec sharedInstance];
}

- (NSObject<FlutterPlatformView>*)createWithFrame:(CGRect)frame
                                   viewIdentifier:(int64_t)viewId
                                        arguments:(id _Nullable)args {
    if (self.viewName==kFACPTheaterViewId) {
        return [[FACPTheaterView alloc] initWithFrame:frame
                                    viewIdentifier:viewId
                                         arguments:args
                                   binaryMessenger:self.messenger
                                            plugin:self.plugin];
    }else if (self.viewName==kFACPDramaDetailViewId) {
        return [[FACPDramaDetailView alloc] initWithFrame:frame
                                    viewIdentifier:viewId
                                         arguments:args
                                   binaryMessenger:self.messenger
                                            plugin:self.plugin];
    }else if (self.viewName==kFACPVideoViewId) {
        return [[FACPVideoView alloc] initWithFrame:frame
                                    viewIdentifier:viewId
                                         arguments:args
                                   binaryMessenger:self.messenger
                                            plugin:self.plugin];
    }else if (self.viewName==kFACPNovelStoryViewId) {
        return [[FACPNovelStoryView alloc] initWithFrame:frame
                                    viewIdentifier:viewId
                                         arguments:args
                                   binaryMessenger:self.messenger
                                            plugin:self.plugin];
    }
    return nil;
}

@end
