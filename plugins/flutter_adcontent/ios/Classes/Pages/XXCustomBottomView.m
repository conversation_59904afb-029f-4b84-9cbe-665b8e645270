//
//  XXCustomBottomView.m
//  flutter_adcontent
//
//  Created by 非大鱼 on 2025/8/10.
//

#import "XXCustomBottomView.h"
#import <SDWebImage/SDWebImage.h>

@interface XXCustomBottomView()
@property (strong,nonatomic) UIImageView *thumbnailImageView;
@property (strong,nonatomic) UILabel *titleLabel;
@property (strong,nonatomic) UIView *categoryContainerView;
@property (strong,nonatomic) UILabel *categoryLabel;
@property (strong,nonatomic) UILabel *descriptionLabel;
@property (strong,nonatomic) UIView *episodeContainerView;
@property (strong,nonatomic) UILabel *episodeLabel;

@property (strong,nonatomic) UIButton *favButton;
@end

@implementation XXCustomBottomView

-(instancetype)initWithFrame:(CGRect)frame{
    if(self = [super initWithFrame:frame]){
        [self setupUI];
        [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(updateFav:) name:@"updateVideoFav" object:nil];
    }
    return self;
}
-(void)updateFav:(NSNotification *)nofi{
    BOOL isfav = [(NSNumber *)nofi.object boolValue];
    self.favButton.selected = isfav;
}
-(void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
- (void)setupUI {
    // 设置背景色为深色
    self.backgroundColor = [UIColor clearColor];
    
    // 创建视频缩略图
    self.thumbnailImageView = [[UIImageView alloc] init];
    self.thumbnailImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.thumbnailImageView.clipsToBounds = YES;
    self.thumbnailImageView.layer.cornerRadius = 8.0;
    [self addSubview:self.thumbnailImageView];
    self.thumbnailImageView.userInteractionEnabled = true;
    UITapGestureRecognizer * tap0 = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(clickThumb)];
    [self.thumbnailImageView addGestureRecognizer:tap0];
    
    
    // 创建标题标签
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.titleLabel.numberOfLines = 1;
    self.titleLabel.userInteractionEnabled = true;
    [self addSubview:self.titleLabel];
    
    UITapGestureRecognizer * tap1 = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(pushDetail)];
    [self.titleLabel addGestureRecognizer:tap1];
    
    
    // 创建分类标签
    self.categoryContainerView = [[UIView alloc] init];
    self.categoryContainerView.backgroundColor = [UIColor colorWithRed:1 green:1 blue:1 alpha:0.1];
    self.categoryContainerView.layer.cornerRadius = 8;
    [self addSubview:self.categoryContainerView];
    
    self.categoryLabel = [[UILabel alloc] init];
    self.categoryLabel.textColor = [UIColor whiteColor];
    self.categoryLabel.font = [UIFont systemFontOfSize:14];
    [self addSubview:self.categoryLabel];
    
    
    // 创建描述标签
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.textColor = [UIColor whiteColor];
    self.descriptionLabel.font = [UIFont systemFontOfSize:14];
    self.descriptionLabel.numberOfLines = 2;
    [self addSubview:self.descriptionLabel];
    
    // 创建集数容器视图
    self.episodeContainerView = [[UIView alloc] init];
    self.episodeContainerView.backgroundColor = [UIColor colorWithRed:1 green:1 blue:1 alpha:0.2];
    self.episodeContainerView.layer.cornerRadius = 8;
    [self addSubview:self.episodeContainerView];
    
    UITapGestureRecognizer * tap2 = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(pushChoose)];
    [self.episodeContainerView addGestureRecognizer:tap2];
    
    // 创建集数标签
    self.episodeLabel = [[UILabel alloc] init];
    self.episodeLabel.textColor = [UIColor whiteColor];
    self.episodeLabel.font = [UIFont systemFontOfSize:16];
    self.episodeLabel.textAlignment = NSTextAlignmentLeft;
    [self addSubview:self.episodeLabel];
    
    
    //创建收藏按钮
    self.favButton = [UIButton buttonWithType:UIButtonTypeCustom];

    [self.favButton setBackgroundImage:[UIImage imageNamed:@"favButtom"] forState:UIControlStateSelected];
    [self.favButton setBackgroundImage:[UIImage imageNamed:@"unfavButtom"] forState:UIControlStateNormal];
    self.favButton.frame = CGRectMake(self.frame.size.width - 55, -60, 35, 35);
    [self addSubview:self.favButton];
    self.favButton.hidden = true;
    [self.favButton addTarget:self action:@selector(favClick) forControlEvents:UIControlEventTouchUpInside];
    
    [self setupConstraints];
}

-(void)clickThumb{
    if(self.clickThumbBlock){
        self.clickThumbBlock();
    }
}

-(void)pushDetail{
    if(self.pushDetailBlock){
        self.pushDetailBlock();
    }
}

-(void)pushChoose{
    if(self.chooseBlock){
        self.chooseBlock();
    }
}
-(void)favClick{
//    self.favButton.selected = !self.favButton.selected;
    if(self.favBlock){
        self.favBlock();
    }
}
- (void)setupConstraints {
    // 设置约束
    self.thumbnailImageView.translatesAutoresizingMaskIntoConstraints = NO;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.categoryContainerView.translatesAutoresizingMaskIntoConstraints = NO;
    self.categoryLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.episodeContainerView.translatesAutoresizingMaskIntoConstraints = NO;
    self.episodeLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    // 集数容器约束
    [NSLayoutConstraint activateConstraints:@[
        [self.episodeContainerView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:12],
        [self.episodeContainerView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-12],
        [self.episodeContainerView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor constant:-15],
        [self.episodeContainerView.heightAnchor constraintEqualToConstant:36]
    ]];
    
    // 集数标签约束
    [NSLayoutConstraint activateConstraints:@[
        [self.episodeLabel.leadingAnchor constraintEqualToAnchor:self.episodeContainerView.leadingAnchor  constant:16],
        [self.episodeLabel.trailingAnchor constraintEqualToAnchor:self.episodeContainerView.trailingAnchor   constant:-16],
        [self.episodeLabel.heightAnchor constraintEqualToConstant:36],
        [self.episodeLabel.topAnchor constraintEqualToAnchor:self.episodeContainerView.topAnchor],
    ]];
    
    // 描述约束
    [NSLayoutConstraint activateConstraints:@[
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor  constant:12],
        [self.descriptionLabel.bottomAnchor constraintEqualToAnchor:self.episodeContainerView.topAnchor constant:-12],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.episodeContainerView.trailingAnchor constant:-50],
        [self.descriptionLabel.heightAnchor constraintEqualToConstant:36]
    ]];
    
    // 缩略图约束
    [NSLayoutConstraint activateConstraints:@[
        [self.thumbnailImageView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor  constant:12],
//        [self.thumbnailImageView.topAnchor constraintEqualToAnchor:self.topAnchor],
        [self.thumbnailImageView.bottomAnchor constraintEqualToAnchor:self.descriptionLabel.topAnchor constant:-12],
        [self.thumbnailImageView.widthAnchor constraintEqualToConstant:44],
        [self.thumbnailImageView.heightAnchor constraintEqualToConstant:62]
    ]];
    
    // 标题约束
    [NSLayoutConstraint activateConstraints:@[
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.thumbnailImageView.trailingAnchor constant:12],
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.thumbnailImageView.topAnchor],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.descriptionLabel.trailingAnchor], // This line was changed to self.descriptionLabel.leadingAnchor
        [self.titleLabel.heightAnchor constraintEqualToConstant:33]
    ]];
    

    // 分类容器约束 - 根据内容适应宽度
    [NSLayoutConstraint activateConstraints:@[
        [self.categoryContainerView.leadingAnchor constraintEqualToAnchor:self.thumbnailImageView.trailingAnchor constant:12],
        [self.categoryContainerView.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:2.5],
        [self.categoryContainerView.heightAnchor constraintEqualToConstant:17]
    ]];
    
    // 分类标签约束 - 在容器内左右间距16
    [NSLayoutConstraint activateConstraints:@[
        [self.categoryLabel.leadingAnchor constraintEqualToAnchor:self.categoryContainerView.leadingAnchor constant:16],
        [self.categoryLabel.trailingAnchor constraintEqualToAnchor:self.categoryContainerView.trailingAnchor constant:-16],
        [self.categoryLabel.topAnchor constraintEqualToAnchor:self.categoryContainerView.topAnchor],
        [self.categoryLabel.bottomAnchor constraintEqualToAnchor:self.categoryContainerView.bottomAnchor]
    ]];
    
    

}

/// 根据数据更新UI
/// @param playletInfoModel 答题的数据
- (void)updateWithData:(DJXPlayletInfoModel *)playletInfoModel{
    if (!playletInfoModel) return;
    
    // 更新标题
    self.titleLabel.text = [NSString stringWithFormat:@"%@ ›", playletInfoModel.title ?: @""];
    
    // 更新分类
    self.categoryLabel.text = playletInfoModel.category_name ?: @"";
    if(playletInfoModel.category_name){
        self.categoryContainerView.hidden = false;
    }else{
        self.categoryContainerView.hidden = true;
    }
    
   
    // 更新描述
    self.descriptionLabel.text = playletInfoModel.desc ?: @"";
    
    // 更新集数信息
    NSString *episodeText = [NSString stringWithFormat:@"第%ld集 - 共%ld集", 
                             (long)playletInfoModel.current_episode, 
                             (long)playletInfoModel.total];
    self.episodeLabel.text = episodeText;
    
    // 更新缩略图（如果有图片URL）
    if (playletInfoModel.cover_image && playletInfoModel.cover_image.length > 0) {
        // 这里可以使用图片加载库如SDWebImage来异步加载图片
         [self.thumbnailImageView sd_setImageWithURL:[NSURL URLWithString:playletInfoModel.cover_image]];
    }
}

/// 根据数据更新UI
/// @param playletInfoModel 答题的数据
- (void)updateWithData:(DJXPlayletInfoModel *)playletInfoModel favStatue:(BOOL)favState{
    [self updateWithData:playletInfoModel];
    self.favButton.selected = favState;
    self.favButton.hidden = false;
}

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    CGPoint newPoint = [self convertPoint:point toView:self.favButton];
        
        if ([self.favButton pointInside:newPoint withEvent:event]) {
            return self.favButton;
        }
        return [super hitTest:point withEvent:event];
}

@end
