//
//  FACPNovelStoryView.m
//  flutter_adcontent
//
//  Created by zero on 2024/3/21.
//

#import "FACPNovelStoryView.h"
#import "FACPNovelReadViewController.h"

@interface FACPNovelStoryView()<FlutterPlatformView,MNStoryReaderDelegate>
@property (strong,nonatomic) FlutterMethodCall *methodCall;
@property (strong,nonatomic) FlutterMethodChannel *methodChannel;
@property (strong,nonatomic) UIView *contentView;
@property (strong,nonatomic) MNSMainViewController *vc;
@property (strong,nonatomic) UIViewController *rootCon;
@property (nonatomic, copy) void (^onADWillShow)(NSString * cpm);
@property (nonatomic, copy) void (^onADRewardDidVerified)(MNStoryRewardADResult *rewardResult);
@property (strong,nonatomic) NSString *cpm;
@property double width;
@property double height;

@end

@implementation FACPNovelStoryView

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger
                       plugin:(FlutterAdcontentPlugin*) plugin{
    if (self = [super init]) {
        self.plugin = plugin;
        // 设置宽高，初始化容器 View
        self.width = [args[@"width"] floatValue];
        self.height = [args[@"height"] floatValue];
        self.contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.width, self.height)];
        self.contentView.backgroundColor = [UIColor whiteColor];
//        self.contentView.clipsToBounds = YES;
        
        // 初始化方法通道
        self.methodChannel = [FlutterMethodChannel methodChannelWithName:[NSString stringWithFormat:@"%@/%lli",kFACPNovelStoryViewId,viewId] binaryMessenger:messenger];
        self.methodCall = [FlutterMethodCall methodCallWithMethodName:@"FACPNovelStoryView" arguments:args];
        
        // 设置消息处理器
        __weak typeof(self) weakSelf = self;
        [self.methodChannel setMethodCallHandler:^(FlutterMethodCall *call, FlutterResult result) {
            [weakSelf handleMethodCall:call result:result];
        }];
        
        [self loadView:self.methodCall];
    }
    return self;
}

- (UIView*)view {
    return self.contentView;
}

// 加载 View
- (void)loadView:(FlutterMethodCall *)call {
    int channelType = [call.arguments[@"channelType"] intValue];
    int setTopOffset = [call.arguments[@"setTopOffset"] intValue];
    BOOL canPullRefresh = [call.arguments[@"canPullRefresh"] boolValue];
    int recPageSize = [call.arguments[@"recPageSize"] intValue];
    int rewardAdMode = [call.arguments[@"rewardAdMode"] intValue];
    int pageTurnMode = [call.arguments[@"pageTurnMode"] intValue];
    int defaultTextSize = [call.arguments[@"defaultTextSize"] intValue];
    int endPageCardStyle = [call.arguments[@"endPageCardStyle"] intValue];
    
    // 获取当前根视图控制器
    self.rootCon = [UIApplication sharedApplication].delegate.window.rootViewController;

    // 创建阅读器参数配置
    MNStoryReaderOpenParams *params = [[MNStoryReaderOpenParams alloc] init];
    params.customRewardAD = (rewardAdMode == 1); // 是否自定义激励解锁
    params.addCustomRewardPoint = NO;
    params.showCustomBottomBannerAD = NO;
    params.showCustomMiddleAD = NO;
    params.pageIntervalForMiddleAD = 4;
    params.customRewardEntryView = NO;
    // 4. 设置阅读器配置
    params.customRewardAD = (rewardAdMode==1);  // 激励广告模式
    params.pageTurnType = pageTurnMode;  // 翻页模式
    params.fontSize = defaultTextSize; // 默认字体大小
    params.endPageCellStyle = endPageCardStyle; // 文末卡片样式
    params.delegate = self;
//    params.responder =rootViewController;
    
    // 创建短故事主视图控制器
    MNSMainViewController *mvc = [[MNSMainViewController alloc] initWithReadConfig:params];
    mvc.view.frame =self.contentView.frame;
    UINavigationController *vc = [[UINavigationController alloc] initWithRootViewController:mvc];
//    vc.modalPresentationStyle = UIModalPresentationAutomatic; // 全屏模式
    params.responder =vc;
    vc.view.frame = self.contentView.frame;
    
    // 将视图控制器添加为子控制器
    [self.rootCon addChildViewController:vc];
    
    [self.contentView addSubview:vc.view];
    [vc didMoveToParentViewController:self.rootCon];
}

- (void)dispose {
    [self cleanupViewController];
}

- (void)cleanupViewController {
    if (self.vc) {
        [self.vc.view removeFromSuperview];
        [self.vc willMoveToParentViewController:nil];
        [self.vc removeFromParentViewController];
        self.vc = nil;
    }
}

#pragma mark - Method Channel Handler

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    if ([call.method isEqualToString:@"setCustomAdOnShow"]) {
        // 设置广告 CPM
        NSString *cpm=call.arguments;
        [self setCustomAdOnShow:cpm];
        result(@(YES));
    } else if ([call.method isEqualToString:@"setCustomAdOnReward"]) {
        // 设置激励结果
        BOOL verify=[call.arguments[@"verify"] boolValue];
        NSDictionary<NSString *, id> *data=call.arguments[@"extraData"];
        [self setCustomAdOnReward:verify extraData:data];
        result(@(YES));
    } else {
        result(FlutterMethodNotImplemented);
    }
}
#pragma mark - sendEvent
// 发送事件
- (void) sendEvent:(NSString *) method data:(id _Nullable)data{
    if (self.methodChannel) {
        [self.methodChannel invokeMethod:method arguments: data];
    }
}

// 设置自定义广告价格信息
- (void)setCustomAdOnShow:(NSString *) cpm{
    self.cpm=cpm;
    if (self.onADWillShow) {
        self.onADWillShow(cpm);
    }
}

// 设置自定义广告解锁情况
- (void)setCustomAdOnReward:(BOOL) verify extraData:(NSDictionary<NSString *, id> *)data{
    if (self.onADRewardDidVerified) {
        MNStoryRewardADResult *result= [[MNStoryRewardADResult alloc] init];
        result.success=verify;
        self.onADRewardDidVerified(result);
        self.onADRewardDidVerified = nil;
    }
}

#pragma mark - MNSMainViewControllerDelegate


#pragma mark - MNStoryReaderDelegate

- (BOOL)mns_shouldShowEntryView:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    return YES;
}

- (void)mns_onUnlockFlowStart:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    [self sendEvent:@"unlockFlowStart" data:@{
        // 基本信息
        @"id": @(sessionContext.storyId),
        @"categoryId": @(sessionContext.categoryId),
    }];
}

- (void)mns_showCustomAD:(MNStorySessionContext *)sessionContext onADWillShow:(void (^)(NSString * cpm))onADWillShow onADRewardDidVerified:(void (^)(MNStoryRewardADResult * _Nonnull))onADRewardDidVerified {
    NSLog(@"#短故事# %s", __FUNCTION__);
    // 将 UnlockFlowShowCustomAD 回调赋值
    self.onADWillShow = onADWillShow;
    self.onADRewardDidVerified = onADRewardDidVerified;
    [self sendEvent:@"showCustomAd" data:NULL];
}

- (void)mns_onUnlockFlowEnd:(MNStorySessionContext *)sessionContext success:(BOOL)success error:(NSError *)error {
    NSLog(@"#短故事# %s", __FUNCTION__);
    NSDictionary *data=@{@"errCode":@(success?200:error.code),
                         @"errMsg":success?@"正常解锁":error.description ?: @"",};
    // 解锁结束
    [self sendEvent:@"unlockFlowEnd" data:data];
}

- (void)mns_startReading:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    // 发送开始阅读事件
    [self sendStoryEvent:@"NovOnEnter" story:sessionContext];
}

- (void)mns_turnPage:(MNStorySessionContext *)sessionContext from:(NSInteger)fromPage to:(NSInteger)toPage {
    NSLog(@"#短故事# %s", __FUNCTION__);
    [self sendStoryEvent:@"NovOnPageSelected" story:sessionContext];
}

- (void)mns_stopReading:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    [self sendStoryEvent:@"NovOnExit" story:sessionContext];
}

- (void)mns_finishReading:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    [self sendStoryEvent:@"NovOnBookEnd" story:sessionContext];
}

// 发送短故事事件
- (void)sendStoryEvent:(NSString *)eventName story:(MNStorySessionContext *)story {
    if (self.methodChannel && story) {
        [self sendEvent:eventName data:@{
        // 基本信息
        @"id": @(story.storyId),
        @"categoryId": @(story.categoryId),
        // 其他信息
        @"title": @"",
        @"desc":  @"",
        @"author":  @"",
        @"coverImage":  @"",
        @"imageType":@(0),
        @"categoryName": @"",
        @"total":@(0),
        @"createTime": @(0),
        @"index": @(0),
        @"progress": @(0.0),
        @"statsCount": @(0),
        @"isFavorite": @(NO),
        @"favoriteTime": @"",
        @"actionTime": @(0),
    }];
    }
}

@end 
