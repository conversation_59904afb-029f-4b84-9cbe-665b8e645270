#import "FACPNovelReadViewController.h"

@interface FACPNovelReadViewController ()

@property (nonatomic, strong) FlutterMethodCall *methodCall;
@property (nonatomic, copy) void (^onADWillShow)(NSString * cpm);
@property (nonatomic, copy) void (^onADRewardDidVerified)(MNStoryRewardADResult *rewardResult);
@property (strong,nonatomic) NSString *cpm;

@end

@implementation FACPNovelReadViewController

- (instancetype)initWithMethodCall:(FlutterMethodCall *)call {
    self = [super init];
    if (self) {
        _methodCall = call;
        self.modalPresentationStyle = UIModalPresentationFullScreen;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    
    // 解析短故事数据
    NSDictionary *storyDict = self.methodCall.arguments[@"novelStory"];
    if (!storyDict) {
        NSLog(@"Opening mini story error: missing novelStory");
        [self dismissPage];
        return;
    }
    // 设置消息处理器
    __weak typeof(self) weakSelf = self;
    [self.methodChannel setMethodCallHandler:^(FlutterMethodCall *call, FlutterResult result) {
        [weakSelf handleMethodCall:call result:result];
    }];

    // 构造阅读器参数
    MNStoryReaderOpenParams *params = [[MNStoryReaderOpenParams alloc] init];
    params.storyId = [storyDict[@"id"] intValue];
    params.customRewardAD = [self.methodCall.arguments[@"rewardAdMode"] intValue] == 1;
    params.pageTurnType = [self.methodCall.arguments[@"pageTurnMode"] intValue];
    params.endPageCellStyle = [self.methodCall.arguments[@"endPageCardStyle"] intValue];
    params.delegate = self;
    params.responder = self;

    // 打开阅读器
    [[MNStoryManager shareInstance] openMiniStory:params];
}

// 销毁页面
- (void)dismissPage {
    [self dismissViewControllerAnimated:NO completion:nil];
}

#pragma mark - Method Channel Handler

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    if ([call.method isEqualToString:@"setCustomAdOnShow"]) {
        // 设置广告 CPM
        NSString *cpm=call.arguments;
        [self setCustomAdOnShow:cpm];
        result(@(YES));
    } else if ([call.method isEqualToString:@"setCustomAdOnReward"]) {
        // 设置激励结果
        BOOL verify=[call.arguments[@"verify"] boolValue];
        NSDictionary<NSString *, id> *data=call.arguments[@"extraData"];
        [self setCustomAdOnReward:verify extraData:data];
        result(@(YES));
    } else {
        result(FlutterMethodNotImplemented);
    }
}

#pragma mark - sendEvent
// 发送事件
- (void) sendEvent:(NSString *) method data:(id _Nullable)data{
    if (self.methodChannel) {
        [self.methodChannel invokeMethod:method arguments: data];
    }
}

// 设置自定义广告价格信息
- (void)setCustomAdOnShow:(NSString *) cpm{
    self.cpm=cpm;
    if (self.onADWillShow) {
        self.onADWillShow(cpm);
    }
}

// 设置自定义广告解锁情况
- (void)setCustomAdOnReward:(BOOL) verify extraData:(NSDictionary<NSString *, id> *)data{
    if (self.onADRewardDidVerified) {
        MNStoryRewardADResult *result= [[MNStoryRewardADResult alloc] init];
        result.success=verify;
        self.onADRewardDidVerified(result);
        self.onADRewardDidVerified = nil;
    }
}

#pragma mark - MNStoryReaderDelegate

//- (Class<MNStoryReaderBannerAdViewProtocol>)mns_bottomBannerADClass {
//    return [MNStoryBottomAdView class];
//}
//
//- (Class<MNStoryReaderNewAdViewControllerProtocol>)mns_middleADClass {
//    return [MNStoryMiddleAdViewController class];
//}
//
//- (UIView<MNStoryThemeViewProtocol> *)mns_insertViewInsideContent:(CGSize)containerSize {
//    return [[LCSInsertView alloc] initWithFrame:CGRectMake(0, 0, containerSize.width, containerSize.height)];
//}

- (BOOL)mns_shouldShowEntryView:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    return YES;
}

//- (UIView<MNStoryRewardADEntryViewProtocol> *)mns_rewardEntryView:(MNStorySessionContext *)sessionContext {
//    NSLog(@"#短故事# %s", __FUNCTION__);
//}

- (void)mns_onUnlockFlowStart:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    [self sendEvent:@"unlockFlowStart" data:@{
        // 基本信息
        @"id": @(sessionContext.storyId),
        @"categoryId": @(sessionContext.categoryId),
    }];
}

- (void)mns_showCustomAD:(MNStorySessionContext *)sessionContext onADWillShow:(void (^)(NSString * cpm))onADWillShow onADRewardDidVerified:(void (^)(MNStoryRewardADResult * _Nonnull))onADRewardDidVerified {
    NSLog(@"#短故事# %s", __FUNCTION__);
    self.onADWillShow = onADWillShow;
    self.onADRewardDidVerified = onADRewardDidVerified;
    [self sendEvent:@"showCustomAd" data:NULL];
}

- (void)mns_onUnlockFlowEnd:(MNStorySessionContext *)sessionContext success:(BOOL)success error:(NSError *)error {
    NSLog(@"#短故事# %s", __FUNCTION__);
    NSDictionary *data=@{@"errCode":@(success?200:error.code),
                         @"errMsg":success?@"正常解锁":error.description ?: @"",};
    // 解锁结束
    [self sendEvent:@"unlockFlowEnd" data:data];
}

- (void)mns_startReading:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
}

- (void)mns_turnPage:(MNStorySessionContext *)sessionContext from:(NSInteger)fromPage to:(NSInteger)toPage {
    NSLog(@"#短故事# %s", __FUNCTION__);
}

- (void)mns_stopReading:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
    [self dismissPage];
}

- (void)mns_finishReading:(MNStorySessionContext *)sessionContext {
    NSLog(@"#短故事# %s", __FUNCTION__);
}

@end
