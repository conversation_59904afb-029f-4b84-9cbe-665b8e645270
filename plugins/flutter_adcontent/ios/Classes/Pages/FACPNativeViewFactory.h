//
//  FACPNativeViewFactory.h
//  flutter_adcontent
//
//  Created by zero on 2024/8/16.
//

#import <Flutter/Flutter.h>
#import <Foundation/Foundation.h>
#import "FlutterAdcontentPlugin.h"

NS_ASSUME_NONNULL_BEGIN

@interface FACPNativeViewFactory : NSObject<FlutterPlatformViewFactory>
@property (strong,nonatomic) NSObject<FlutterBinaryMessenger> *messenger;
@property (strong,nonatomic) FlutterAdcontentPlugin *plugin;
@property (strong,nonatomic) NSString *viewName;
- (instancetype)initWithViewName:(NSString*) viewName withMessenger:(NSObject<FlutterBinaryMessenger>*)messenger withPlugin:(FlutterAdcontentPlugin*) plugin;
@end

NS_ASSUME_NONNULL_END
