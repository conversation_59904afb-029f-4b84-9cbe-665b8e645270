//
//  FACPTheaterView.m
//  flutter_adcontent
//
//  Created by zero on 2024/8/16.
//

#import "FACPDramaDetailView.h"
#import <flutter_gromore_ads/FlutterGromoreAdsPlugin.h>
#import "XXCustomBottomView.h"
@interface FACPDramaDetailView()<FlutterPlatformView,DJXPlayletInterfaceProtocol,DJXPlayletPlayerProtocol,DJXPlayletAdvertProtocol,DJXPlayletDetailCellDelegate,DJXDrawVideoCellAddSubviewDelegate>
@property (strong,nonatomic) FlutterMethodCall *methodCall;
@property (strong,nonatomic) FlutterMethodChannel *methodChannel;
@property (strong,nonatomic) UIView *contentView;
@property (strong,nonatomic) DJXDrawVideoViewController *vc;
@property (nonatomic, copy) void (^unlockInfoHandler)(DJXPlayletUnlockModel *unlockInfo); // 广告解锁回调
@property (nonatomic, copy) void (^onADWillShow)(NSString * cpm);// 广告展示价格
@property (nonatomic, copy) void (^onADRewardDidVerified)(DJXRewardAdResult *rewardResult);// 广告激励结果
@property (strong,nonatomic) NSString *cpm;
@property double width;
@property double height;

@property (assign,nonatomic) BOOL favState;

@end

@implementation FACPDramaDetailView

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger
                       plugin:(FlutterAdcontentPlugin*) plugin{
    if (self = [super init]) {
        self.plugin=plugin;
        // 设置宽高，初始化容器 View
        self.width=[args[@"width"] floatValue];
        self.height=[args[@"height"] floatValue];
        self.contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.width, self.height)];
        self.contentView.backgroundColor = [UIColor redColor];
        self.contentView.clipsToBounds = YES;// 重要超过区域剪裁掉，不然会遮挡
        self.methodChannel = [FlutterMethodChannel methodChannelWithName:[NSString stringWithFormat:@"%@/%lli",kFACPDramaDetailViewId,viewId] binaryMessenger:messenger];
        self.methodCall=[FlutterMethodCall methodCallWithMethodName:@"FACPTheaterView" arguments:args];
        // 设置消息处理器
        __weak typeof(self) weakSelf = self;
        [self.methodChannel setMethodCallHandler:^(FlutterMethodCall *call, FlutterResult result) {
            [weakSelf handleMethodCall:call result:result];
        }];
        [self loadView:self.methodCall];
    }
    return self;
}

- (UIView*)view {
    return self.contentView;
}
// 加载 View
- (void)loadView:(FlutterMethodCall *)call{
    int detailId=[call.arguments[@"id"] intValue];
    int index=[call.arguments[@"index"] intValue];
    int groupId=[call.arguments[@"groupId"] intValue];
    int detailFree=[call.arguments[@"detailFree"] intValue];
    int unlockCount=[call.arguments[@"unlockCount"] intValue];
    int unlockAdMode=[call.arguments[@"unlockAdMode"] intValue];
    BOOL hideTopInfo=[call.arguments[@"hideTopInfo"] boolValue];
    int setTopOffset=[call.arguments[@"setTopOffset"] intValue];
    BOOL hideBack=[call.arguments[@"hideBack"] boolValue];
    BOOL hideBottomInfo=[call.arguments[@"hideBottomInfo"] boolValue];
    int setBottomOffset=[call.arguments[@"setBottomOffset"] intValue];
    BOOL hideRewardDialog=[call.arguments[@"hideRewardDialog"] boolValue];
    BOOL hideMore=[call.arguments[@"hideMore"] boolValue];
    BOOL hideCellularToast=[call.arguments[@"hideCellularToast"] boolValue];
    BOOL hideLikeButton=[call.arguments[@"hideLikeButton"] boolValue];
    BOOL hideFavorButton=[call.arguments[@"hideFavorButton"] boolValue];
    // 获取 contentView 的大小
    CGSize contentViewSize = self.contentView.bounds.size;
    // 判断广告解锁模式
    DJXPlayletUnlockADModeOptions unlockADMode = unlockAdMode == 0 ? DJXPlayletUnlockADMode_Common : DJXPlayletUnlockADMode_Specific;
    // 创建短剧详情
    DJXDrawVideoViewController *vc = [[DJXDrawVideoViewController alloc] initWithConfigBuilder:^(DJXDrawVideoVCConfig * _Nonnull config) {
        DJXPlayletConfig *playletConfig = [DJXPlayletConfig new];
        playletConfig.skitId = detailId;
        playletConfig.episode = index;
        playletConfig.groupId = groupId;
        playletConfig.playletUnlockADMode = unlockADMode;
        playletConfig.freeEpisodesCount = detailFree;
        playletConfig.unlockEpisodesCountUsingAD = unlockCount;
        playletConfig.interfaceDelegate=self;
        playletConfig.playerDelegate=self;
        playletConfig.adDelegate = self;
        playletConfig.hideTopInfo = hideTopInfo;
        playletConfig.fromTopMargin = setTopOffset;
        playletConfig.hideBackButton=hideBack;
        playletConfig.hideBottomInfo=hideBottomInfo;
        playletConfig.hideRewardDialog = hideRewardDialog;
        playletConfig.hideMoreButton = hideMore;
        playletConfig.hideCellularToast = hideCellularToast;
        playletConfig.hideLikeIcon = hideLikeButton;
        playletConfig.hideCollectIcon = hideFavorButton;
        playletConfig.customViewDelegate = self;

        config.playletConfig = playletConfig;
        config.drawVCTabOptions = DJXDrawVideoVCTabOptions_playlet;
        config.viewSize = contentViewSize;
        config.shouldHideTabBarView= hideTopInfo;
        config.customAppear = YES;
        config.hideLikeIcon = hideLikeButton;
        config.hideCollectIcon = hideFavorButton;
//        config.drawVideoCellAddSubviewDelegate = self;
    }];
    // 将 UIViewController 的视图添加到这个 UIView 中
    self.vc = vc;
    // 获取当前根视图控制器
    UIViewController *rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
    
    // 将 UIViewController 添加为子视图控制器
    [rootViewController addChildViewController:vc];
    vc.view.frame = self.contentView.bounds;
    [self.contentView addSubview:vc.view];
    
    // 通知 UIViewController 它已被添加到父视图控制器中
    [vc didMoveToParentViewController:rootViewController];
}

/// 短剧自定义View协议
//@protocol DJXPlayletDetailCellDelegate <NSObject>

/// 创建自定义View直接返回，外部不要持有，cell自己持有复用
/// @param cell 短剧的cell
- (UIView *)djx_playletDetailCellCustomView:(UITableViewCell *)cell{
    return [[XXCustomBottomView alloc]initWithFrame:CGRectMake(0, self.height - 245, self.width, 180)];
}

/// 根据数据更新UI
/// @param cell 短剧的cell
/// @param customView `djx_playletDetailCellCustomView:`返回的自定义View，短剧的cell内部会持有
/// @param playletInfo 短剧的数据
- (void)djx_playletDetailCell:(UITableViewCell *)cell updateCustomView:(UIView *)customView withPlayletData:(DJXPlayletInfoModel *)playletInfo{
    [(XXCustomBottomView *)customView updateWithData:playletInfo favStatue:self.favState];
    ((XXCustomBottomView *)customView).clickThumbBlock = ^{
        [self sendDramaEvent:@"onOpenSelfDetail" infoModel:playletInfo];
    };
    ((XXCustomBottomView *)customView).pushDetailBlock = ^{
        [self sendDramaEvent:@"onOpenSelfDetail" infoModel:playletInfo];
    };
    
    ((XXCustomBottomView *)customView).chooseBlock = ^{
//        [self sendDramaEvent:@"onOpenSelfDetail" infoModel:playletInfo];
        [self.vc chooseEpisode];
    };
    ;
    ((XXCustomBottomView *)customView).favBlock = ^{
        self.favState = !self.favState;
        [self sendEvent:@"dramaFav" data:@{
            @"isFav":self.favState?@"1":@"0",
            @"viewId":@"0"
        }];
    };
}

/// 当cell执行到layoutSubviews时会回调此协议方法
/// @param cell 短剧的cell
/// @param customView `djx_playletDetailCellCustomView:`返回的自定义View
- (void)djx_playletDetailCell:(UITableViewCell *)cell layoutSubviews:(UIView *)customView{
    
}

/// 当cell执行到layoutSubviews后会回调此协议方法
/// @param cell 短剧的cell
/// @param customView `djx_playletDetailCellCustomView:`返回的自定义View
- (void)djx_playletDetailCell:(UITableViewCell *)cell afterLayoutSubviews:(UIView *)customView{
    
}


- (void)dispose {
    // 当 Flutter 视图被销毁时调用
    [self cleanupViewController];
}


- (void)cleanupViewController {
    // 确保 vc 不为空
    if (self.vc) {
        // 视图消失
        [self.vc drawVideoViewControllerDidDisappear];
        // 1. 从父视图中移除视图
        [self.vc.view removeFromSuperview];
        
        // 2. 从父视图控制器中移除子视图控制器
        [self.vc willMoveToParentViewController:nil];
        [self.vc removeFromParentViewController];
        
        // 3. 清理引用
        self.vc = nil;
    }
}


#pragma mark 事件通讯方法

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSLog(@"%s",__FUNCTION__);
    if ([call.method isEqualToString:@"unlock"]) {
        NSLog(@"%s %@",__FUNCTION__,@"unlock");
        // 解锁
        int detailId=[call.arguments[@"id"] intValue];
        int lockSet=[call.arguments[@"lockSet"] intValue];
        BOOL cancel=[call.arguments[@"cancel"] boolValue];
        [self unlockDrama:cancel detailId:detailId lockSet:lockSet];
        result(@(YES));
    } else if ([call.method isEqualToString:@"setCustomAdOnShow"]) {
        // 设置广告 CPM
        NSString *cpm=call.arguments;
        [self setCustomAdOnShow:cpm];
        result(@(YES));
    } else if ([call.method isEqualToString:@"setCustomAdOnReward"]) {
        // 设置激励结果
        BOOL verify=[call.arguments[@"verify"] boolValue];
        NSDictionary<NSString *, id> *data=call.arguments[@"extraData"];
        [self setCustomAdOnReward:verify extraData:data];
        result(@(YES));
    } else if ([call.method isEqualToString:@"setCurrentIndex"]) {
        int index=[call.arguments intValue];
        // 设置当前集数
        [self.vc setCurrentPlayletEpisode:index];
        result(@(YES));
    } else if ([call.method isEqualToString:@"openDramaGallery"]) {
        // 选集弹窗
        [self.vc chooseEpisode];
        result(@(YES));
    } else if ([call.method isEqualToString:@"openMoreDialog"]) {
        // 右上角弹窗
        [self.vc playletMoreButtonClick];
        result(@(YES));
    } else if ([call.method isEqualToString:@"pause"]) {
        // 暂停
        [self.vc drawVideoViewControllerDidDisappear];
        result(@(YES));
    } else if ([call.method isEqualToString:@"resume"]) {
        // 继续
        [self.vc drawVideoViewControllerDidAppear];
        result(@(YES));
    } else if ([call.method isEqualToString:@"dispose"]) {
        // 销毁
        [self dispose];
        result(@(YES));
    }
    else if([call.method isEqualToString:@"dramaFavNative"]){
        
        // 设置激励结果
        BOOL isFav=[call.arguments[@"isFav"] boolValue];
        self.favState = isFav;
        [[NSNotificationCenter defaultCenter]postNotificationName:@"updateVideoFav" object:@(isFav)];
        
    } else if ([call.method isEqualToString:@"setSpeedPlay"]) {
        double speed = [call.arguments[@"speed"] doubleValue];
        int scope = [call.arguments[@"scope"] intValue];
        // 设置倍速
        [self.vc setPlaySpeed:speed scope:scope==0?DJXDrawVideoSpeedScopeEpisode:DJXDrawVideoSpeedScopeShortPlay];
        result(@(YES));
    }
    else {
        result(FlutterMethodNotImplemented);
    }
}

// 发送短剧事件
- (void) sendDramaEvent:(NSString *) method infoModel:(DJXPlayletInfoModel *)infoModel{
    [self sendEvent:method data:[self.plugin playletToMap:infoModel]];
}

// 发送事件
- (void) sendEvent:(NSString *) method data:(id _Nullable)data{
    if (self.methodChannel) {
        [self.methodChannel invokeMethod:method arguments: data];
    }
}

// 解锁短剧
- (void)unlockDrama:(BOOL)cancel detailId:(int)detailId lockSet:(int)lockSet {
    DJXPlayletUnlockModel *unlockInfo = [[DJXPlayletUnlockModel alloc] init];
    if(detailId != 0){
        unlockInfo.playletId = detailId;
    }
    unlockInfo.unlockEpisodeCount = lockSet;
    unlockInfo.cancelUnlock= cancel;
    unlockInfo.unlockModeType = DJXPlayletUnlockModeType_Default;
    if (self.unlockInfoHandler) {
        self.unlockInfoHandler(unlockInfo);
    }
}

// 设置自定义广告价格信息
- (void)setCustomAdOnShow:(NSString *) cpm{
    self.cpm=cpm;
    if (self.onADWillShow) {
        self.onADWillShow(cpm);
    }
}

// 设置自定义广告解锁情况
- (void)setCustomAdOnReward:(BOOL) verify extraData:(NSDictionary<NSString *, id> *)data{
    if (self.onADRewardDidVerified) {
        DJXRewardAdResult *result= [[DJXRewardAdResult alloc] init];
        result.success=verify;
        result.cpm=self.cpm;
        result.extraData = data;
        self.onADRewardDidVerified(result);
    }
}

#pragma mark DJXPlayletInterfaceProtocol

- (void)playletDetailUnlockFlowStart:(DJXPlayletInfoModel *)infoModel
                   unlockInfoHandler:(void (^)(DJXPlayletUnlockModel *unlockInfo))unlockInfoHandler
                           extraInfo:(NSDictionary * _Nullable)extraInfo {
    NSLog(@"%s",__FUNCTION__);
    // 将 unlockInfoHandler 赋值给属性
    self.unlockInfoHandler = unlockInfoHandler;
    // 开始解锁
    [self sendDramaEvent:@"unlockFlowStart" infoModel:infoModel];
}

- (void)playletDetailUnlockFlowEnd:(DJXPlayletInfoModel *)infoModel
                           success:(BOOL)success
                             error:(NSError * _Nullable)error
                         extraInfo:(NSDictionary * _Nullable)extraInfo {
    NSLog(@"%s",__FUNCTION__);
    NSDictionary *drama=[self.plugin playletToMap:infoModel];
    NSDictionary *data=@{@"drama":drama,
                         @"errCode":@(success?200:error.code),
                         @"errMsg":success?@"正常解锁":error.description ?: @"",};
    // 解锁结束
    [self.methodChannel invokeMethod:@"unlockFlowEnd" arguments: data];
}

- (void)playletDetailUnlockFlowShowCustomAD:(DJXPlayletInfoModel *)infoModel
                               onADWillShow:(void (^)(NSString * cpm))onADWillShow
                      onADRewardDidVerified:(void (^)(DJXRewardAdResult *rewardResult))onADRewardDidVerified {
    NSLog(@"%s",__FUNCTION__);
    // 将 UnlockFlowShowCustomAD 回调赋值
    self.onADWillShow = onADWillShow;
    self.onADRewardDidVerified = onADRewardDidVerified;
    // 开始展示自定义广告
    [self sendDramaEvent:@"showCustomAd" infoModel:infoModel];
}


#pragma mark 短剧播放校验(接口接入必须实现)
/// 本剧集观看完毕，切到下一部短剧回调
- (void)nextPlayletWillPlay:(DJXPlayletInfoModel *)infoModel {
    NSLog(@"[短剧回调]接口形式回调 %s skitID:%ld skitName:%@", __func__, infoModel.shortplay_id, infoModel.title);
}
/// 点击混排中进入跳转播放页的按钮
- (void)clickEnterView:(DJXPlayletInfoModel *)infoModel {
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onOpenDetail" infoModel:infoModel];
}

#pragma mark DJXPlayletPlayerProtocol 播放器回调
/*! 视频开始播放的回调 */
- (void)drawVideoStartPlay:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoPlay" infoModel:infoModel];
}
/*! 视频播放结束的回调（视频结束播放（退出或者中断）） */
- (void)drawVideoOverPlay:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoOver" infoModel:infoModel];
}
/*! 视频暂停播放 */
- (void)drawVideoPause:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoPause" infoModel:infoModel];
}
/*! 视频继续播放 */
- (void)drawVideoContinue:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoContinue" infoModel:infoModel];
}
/*! 视频完整播放结束一遍的回调 */
- (void)drawVideoPlayCompletion:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoCompletion" infoModel:infoModel];
}
/*! 进度条拖动感到某个点 */
- (void)onVideSeekToTime:(NSTimeInterval)endTime inPosition:(NSInteger)position{
    NSLog(@"%s",__FUNCTION__);
    [self sendEvent:@"onSeekTo" data:@(position)];
}
/*! 播放进度 */
- (void)drawVideo:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel progress:(CGFloat)progress{
    NSLog(@"%s",__FUNCTION__);
    // 计算最终的播放进度
    CGFloat duration = infoModel.video_duration * progress;
    [self sendEvent:@"onDurationChange" data:@(duration)];
}
/// 播放器内部错误
- (void)drawVideoError:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onError" infoModel:infoModel];
}

#pragma mark DJXPlayletAdvertProtocol 广告回调

- (void)djxAdFillFail:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
    [self sendDramaAdErrorEvent:[NSError errorWithDomain:@"flutterads.top" code:-200 userInfo:@{ NSLocalizedDescriptionKey: @"渲染失败" }]  event:event];
}

- (void)djxAdLoadFail:(DJXAdTrackEvent *)event error:(nonnull NSError *)error {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
    // 创建一个新的 err，判断 error 是否为空，如果为空则创建一个新的 err
    NSError *err = error ?: [NSError errorWithDomain:@"flutterads.top" code:-200 userInfo:@{ NSLocalizedDescriptionKey: @"加载失败" }];
    [self sendDramaAdErrorEvent:err event:event];
}

- (void)djxAdLoadSuccess:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s ad_id:%@", __func__,event.adSlotID);
    [self sendDramaAdEvent:onAdLoaded event:event];
}

- (void)djxAdWillShow:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
    [self sendDramaAdEvent:onAdExposure event:event];
}

- (void)djxClickAdViewEvent:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
    [self sendDramaAdEvent:onAdClicked event:event];
}

- (void)djxSendAdRequest:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
}

- (void)djxVideoAdContinue:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
}

- (void)djxVideoAdOverPlay:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
    [self sendDramaAdEvent:onAdComplete event:event];
}

- (void)djxVideoAdPause:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
}

- (void)djxVideoAdStartPlay:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
}

- (void)djxVideoBufferEvent:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
}

- (void)djxVideoRewardFinishEvent:(DJXAdTrackEvent *)event {
    NSNumber *verift = event.params[@"ad_verify"];
    NSLog(@"[短剧回调]广告回调 %s 激励是否完成：%d", __func__, verift.boolValue);
    FGMAdRewardEvent *rewardEvent=[[FGMAdRewardEvent alloc] initWithAdId:event.adSlotID rewardType:0 rewardVerify:verift.boolValue rewardAmount:0 rewardName:@"不支持" customData:@"不支持" userId:@"不支持" errCode:0  errMsg:@"none" transId:@""];
    [self sendDramaAdEvent:rewardEvent];
}

- (void)djxVideoRewardSkipEvent:(DJXAdTrackEvent *)event {
    NSLog(@"[短剧回调]广告回调 %s", __func__);
    [self sendDramaAdEvent:onAdSkip event:event];
}

// 发送短剧广告事件
-(void)sendDramaAdEvent:(FGMAdEvent *)event {
    [[FlutterGromoreAdsPlugin shared] sendEvent:event];
}

// 发送短剧广告事件
-(void)sendDramaAdEvent:(NSString *)action event:(DJXAdTrackEvent *)event {
    FGMAdEvent *adEvent=[[FGMAdEvent alloc] initWithAdId:event.adSlotID action: action];
    [self sendDramaAdEvent:adEvent];
}

// 发送错误广告事件
-(void)sendDramaAdErrorEvent:(NSError *)error event:(DJXAdTrackEvent *)event {
    FGMAdErrorEvent *adEvent=[[FGMAdErrorEvent alloc] initWithAdId:event.adSlotID error:error];
    [self sendDramaAdEvent:adEvent];
}


@end
