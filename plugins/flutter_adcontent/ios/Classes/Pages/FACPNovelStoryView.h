//
//  FACPNovelStoryView.h
//  flutter_adcontent
//
//  Created by zero on 2024/3/21.
//

#import <Flutter/Flutter.h>
#import "FlutterAdcontentPlugin.h"
#import <PangrowthMiniStory/MNSMainViewController.h>

NS_ASSUME_NONNULL_BEGIN

@interface FACPNovelStoryView : NSObject<FlutterPlatformView>
@property (strong,nonatomic,nullable) FlutterAdcontentPlugin *plugin;

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger
                       plugin:(FlutterAdcontentPlugin*) plugin;
- (nonnull UIView*)view;

@end

NS_ASSUME_NONNULL_END 
