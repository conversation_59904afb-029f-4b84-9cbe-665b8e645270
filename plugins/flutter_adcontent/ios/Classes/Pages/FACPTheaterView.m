//
//  FACPTheaterView.m
//  flutter_adcontent
//
//  Created by zero on 2024/8/16.
//

#import "FACPTheaterView.h"
#import "XXCustomBottomView.h"
@interface FACPTheaterView()<FlutterPlatformView,DJXPlayletInterfaceProtocol,DJXDrawVideoCellAddSubviewDelegate,DJXPlayletPlayerProtocol>
@property (strong,nonatomic) FlutterMethodCall *methodCall;
@property (strong,nonatomic) FlutterMethodChannel *methodChannel;
@property (strong,nonatomic) UIView *contentView;
@property (strong,nonatomic) DJXDrawVideoViewController *vc;
@property double width;
@property double height;

@end

@implementation FACPTheaterView

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger
                       plugin:(FlutterAdcontentPlugin*) plugin{
    if (self = [super init]) {
        self.plugin=plugin;
        // 设置宽高，初始化容器 View
        self.width=[args[@"width"] floatValue];
        self.height=[args[@"height"] floatValue];
        self.contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.width, self.height)];
        self.contentView.backgroundColor = [UIColor redColor];
        self.contentView.clipsToBounds = YES;// 重要超过区域剪裁掉，不然会遮挡
        self.methodChannel = [FlutterMethodChannel methodChannelWithName:[NSString stringWithFormat:@"%@/%lli",kFACPTheaterViewId,viewId] binaryMessenger:messenger];
        self.methodCall=[FlutterMethodCall methodCallWithMethodName:@"FACPTheaterView" arguments:args];
        // 设置消息处理器
        __weak typeof(self) weakSelf = self;
        [self.methodChannel setMethodCallHandler:^(FlutterMethodCall *call, FlutterResult result) {
            [weakSelf handleMethodCall:call result:result];
        }];
        [self loadView:self.methodCall];
    }
    return self;
}

- (UIView*)view {
    return self.contentView;
}
// 加载 View
- (void)loadView:(FlutterMethodCall *)call{
    int channelType = [call.arguments[@"channelType"] intValue];
    BOOL hideBack=[call.arguments[@"hideBack"] boolValue];
    BOOL hideTopInfo=[call.arguments[@"hideTopInfo"] boolValue];
    BOOL showChangeBtn=[call.arguments[@"showChangeBtn"] boolValue];
    int detailFree=[call.arguments[@"detailFree"] intValue];
    int unlockCount=[call.arguments[@"unlockCount"] intValue];
    BOOL hideLikeButton=[call.arguments[@"hideLikeButton"] boolValue];
    BOOL hideFavorButton=[call.arguments[@"hideFavorButton"] boolValue];
    // 解析频道类型
    DJXDrawVideoVCTabOptions drawVCTabOptions=DJXDrawVideoVCTabOptions_playlet_feed;
    if (channelType==1) {
        drawVCTabOptions=DJXDrawVideoVCTabOptions_playlet_feed;
    } else if (channelType==2)  {
        drawVCTabOptions=DJXDrawVideoVCTabOptions_theater;
    } else {
        drawVCTabOptions=DJXDrawVideoVCTabOptions_playlet_feed|DJXDrawVideoVCTabOptions_theater;
    }
    // 获取 contentView 的大小
    CGSize contentViewSize = self.contentView.bounds.size;
    // 创建剧场
    DJXDrawVideoViewController *vc = [[DJXDrawVideoViewController alloc] initWithConfigBuilder:^(DJXDrawVideoVCConfig * _Nonnull config) {
        DJXPlayletConfig *playletConfig = [DJXPlayletConfig new];
        playletConfig.playletUnlockADMode = DJXPlayletUnlockADMode_Specific;
        playletConfig.freeEpisodesCount = detailFree;
        playletConfig.unlockEpisodesCountUsingAD = unlockCount;
        playletConfig.interfaceDelegate=self;
        
        playletConfig.playerDelegate =  self;
        
        config.playletConfig = playletConfig;
        config.drawVCTabOptions = drawVCTabOptions;
        config.viewSize = contentViewSize;
        config.showCloseButton = !hideBack;
        config.shouldHideTabBarView= hideTopInfo;
        config.hiddenPlayletEnterView=!showChangeBtn;
        config.hideLikeIcon = hideLikeButton;
        config.hideCollectIcon = hideFavorButton;
        config.customAppear = YES;
        
        config.drawVideoCellAddSubviewDelegate = self;
        
    }];
    // 将 UIViewController 的视图添加到这个 UIView 中
    self.vc = vc;
    // 获取当前根视图控制器
    UIViewController *rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
    
    // 将 UIViewController 添加为子视图控制器
    [rootViewController addChildViewController:vc];
    vc.view.frame = self.contentView.bounds;
    [self.contentView addSubview:vc.view];
    
    // 通知 UIViewController 它已被添加到父视图控制器中
    [vc didMoveToParentViewController:rootViewController];
}


//@protocol DJXDrawVideoCellAddSubviewDelegate <NSObject>

/// 创建subview直接返回，外部不用持有，cell自己持有复用
/// @param cell 小视频的cell
- (UIView *)djx_drawVideoCellSubview:(UITableViewCell *)cell{
    return [[XXCustomBottomView alloc]initWithFrame:CGRectMake(0, self.height - 200, self.width, 170)];
}

/// 根据数据更新UI
/// @param cell Draw短剧的cell
/// @param subview `djx_drawVideoCellSubview:`返回的subview
/// @param playletInfoModel 答题的数据
- (void)djx_drawVideoCell:(UITableViewCell *)cell updateSubview:(UIView *)subview withData:(DJXPlayletInfoModel *)playletInfoModel{
    __weak typeof(self) weakSelf = self;
    NSString *idStr = [NSString stringWithFormat:@"%ld", (long)playletInfoModel.shortplay_id];
    [[DJXPlayletManager shareInstance] requestPlayletListWithPlayletId:@[idStr] success:^(NSArray<DJXPlayletInfoModel *> * _Nonnull playletList) {
        if (playletList.count != 0) {
            DJXPlayletInfoModel *info = playletList.firstObject;
            [(XXCustomBottomView *)subview updateWithData:info];
            ((XXCustomBottomView *)subview).clickThumbBlock = ^{
                [weakSelf.methodChannel invokeMethod:@"onOpenSelfDetail" arguments: [weakSelf.plugin playletToMap:info]];
            };
            ((XXCustomBottomView *)subview).pushDetailBlock = ^{
                [weakSelf.methodChannel invokeMethod:@"onOpenSelfDetail" arguments: [weakSelf.plugin playletToMap:info]];
            };
            
            ((XXCustomBottomView *)subview).chooseBlock = ^{
                [weakSelf.methodChannel invokeMethod:@"onOpenDetail" arguments: [weakSelf.plugin playletToMap:info]];
            };
        }
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"根据ID获取短剧内容失败");
    }];
    
//    //// 跳转短剧详情页
//    - (void)enterPlayPage;
//    // 跳转选集面板
//    - (void)chooseEpisode;
}

/// 当cell执行到layoutSubviews时会回调此协议方法
/// @param cell Draw短剧的cell
/// @param subview `djx_drawVideoCellSubview:`返回的subview
- (void)djx_drawVideoCell:(UITableViewCell *)cell layoutSubviews:(UIView *)subview{
    
}

/// 当cell执行到layoutSubviews后会回调此协议方法
/// @param cell Draw短剧的cell
/// @param subview `djx_drawVideoCellSubview:`返回的subview
- (void)djx_drawVideoCell:(UITableViewCell *)cell afterLayoutSubviews:(UIView *)subview{
   
}


- (void)dispose {
    // 当 Flutter 视图被销毁时调用
    [self cleanupViewController];
}


- (void)cleanupViewController {
    // 确保 vc 不为空
    if (self.vc) {
        // 视图消失
        [self.vc drawVideoViewControllerDidDisappear];
        // 1. 从父视图中移除视图
        [self.vc.view removeFromSuperview];
        
        // 2. 从父视图控制器中移除子视图控制器
        [self.vc willMoveToParentViewController:nil];
        [self.vc removeFromParentViewController];
        
        // 3. 清理引用
        self.vc = nil;
    }
}

#pragma mark 事件通讯方法

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    if ([call.method isEqualToString:@"openMoreDialog"]) {
        [self.vc playletMoreButtonClick];
        result(@(YES));
    } else if ([call.method isEqualToString:@"pause"]) {
        NSLog(@"1111111 pause");
        // 暂停
        [self.vc drawVideoViewControllerDidDisappear];
        result(@(YES));
    } else if ([call.method isEqualToString:@"resume"]) {
        NSLog(@"1111111 resume");
        // 继续
        [self.vc drawVideoViewControllerDidAppear];
        result(@(YES));
    } else if ([call.method isEqualToString:@"dispose"]) {
        // 销毁
        [self dispose];
        result(@(YES));
    }
    else if ([call.method isEqualToString:@"UIApplicationWillResignActiveNotification"]) {
       NSLog(@"1111111 UIApplicationWillResignActiveNotification");
        [[NSNotificationCenter defaultCenter]postNotificationName:UIApplicationWillResignActiveNotification object:nil];
//        [[NSNotificationCenter defaultCenter]postNotificationName:UIApplicationDidBecomeActiveNotification object:nil];
       result(@(YES));
   }
    else if ([call.method isEqualToString:@"UIApplicationDidBecomeActiveNotification"]) {
        NSLog(@"1111111 UIApplicationDidBecomeActiveNotification");
       // 继续
        [[NSNotificationCenter defaultCenter]postNotificationName:UIApplicationDidBecomeActiveNotification object:nil];
       result(@(YES));
   } else if ([call.method isEqualToString:@"setSpeedPlay"]) {
        double speed = [call.arguments[@"speed"] doubleValue];
        int scope = [call.arguments[@"scope"] intValue];
        // 设置倍速
        [self.vc setPlaySpeed:speed scope:scope==0?DJXDrawVideoSpeedScopeEpisode:DJXDrawVideoSpeedScopeShortPlay];
        result(@(YES));
    }
    else {
        result(FlutterMethodNotImplemented);
    }
}


#pragma mark DJXPlayletInterfaceProtocol

- (void)playletDetailUnlockFlowStart:(DJXPlayletInfoModel *)infoModel
                   unlockInfoHandler:(void (^)(DJXPlayletUnlockModel *unlockInfo))unlockInfoHandler
                           extraInfo:(NSDictionary * _Nullable)extraInfo {
    NSLog(@"%s",__FUNCTION__);
}

- (void)playletDetailUnlockFlowEnd:(DJXPlayletInfoModel *)infoModel
                           success:(BOOL)success
                             error:(NSError * _Nullable)error
                         extraInfo:(NSDictionary * _Nullable)extraInfo {
    NSLog(@"%s",__FUNCTION__);
}

- (void)playletDetailUnlockFlowShowCustomAD:(DJXPlayletInfoModel *)infoModel
                               onADWillShow:(void (^)(NSString * cpm))onADWillShow
                      onADRewardDidVerified:(void (^)(DJXRewardAdResult *rewardResult))onADRewardDidVerified {
    NSLog(@"%s",__FUNCTION__);
}


#pragma mark DJXPlayletPlayerProtocol 播放器回调
/*! 视频开始播放的回调 */
- (void)drawVideoStartPlay:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoPlay" infoModel:infoModel];
}
/*! 视频播放结束的回调（视频结束播放（退出或者中断）） */
- (void)drawVideoOverPlay:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoOver" infoModel:infoModel];
}
/*! 视频暂停播放 */
- (void)drawVideoPause:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoPause" infoModel:infoModel];
}
/*! 视频继续播放 */
- (void)drawVideoContinue:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoContinue" infoModel:infoModel];
}
/*! 视频完整播放结束一遍的回调 */
- (void)drawVideoPlayCompletion:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onDJXVideoCompletion" infoModel:infoModel];
}
/*! 进度条拖动感到某个点 */
- (void)onVideSeekToTime:(NSTimeInterval)endTime inPosition:(NSInteger)position{
    NSLog(@"%s",__FUNCTION__);
    [self sendEvent:@"onSeekTo" data:@(position)];
}
/*! 播放进度 */
- (void)drawVideo:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel progress:(CGFloat)progress{
    NSLog(@"%s",__FUNCTION__);
    // 计算最终的播放进度
    CGFloat duration = infoModel.video_duration * progress;
    [self sendEvent:@"onDurationChange" data:@(duration)];
}
/// 播放器内部错误
- (void)drawVideoError:(UIViewController *)viewController config:(DJXPlayletInfoModel *)infoModel{
    NSLog(@"%s",__FUNCTION__);
    [self sendDramaEvent:@"onError" infoModel:infoModel];
}

#pragma mark 短剧播放校验(接口接入必须实现)
/// 本剧集观看完毕，切到下一部短剧回调
- (void)nextPlayletWillPlay:(DJXPlayletInfoModel *)infoModel {
    NSLog(@"[短剧回调]接口形式回调 %s skitID:%ld skitName:%@", __func__, infoModel.shortplay_id, infoModel.title);
}
/// 点击混排中进入跳转播放页的按钮
- (void)clickEnterView:(DJXPlayletInfoModel *)infoModel {
    NSLog(@"%s",__FUNCTION__);
    [self sendEvent:@"onOpenDetail" data: [self.plugin playletToMap:infoModel]];
}

// 发送短剧事件
- (void) sendDramaEvent:(NSString *) method infoModel:(DJXPlayletInfoModel *)infoModel{
    [self sendEvent:method data:[self.plugin playletToMap:infoModel]];
}

// 发送事件
- (void) sendEvent:(NSString *) method data:(id _Nullable)data{
    if (self.methodChannel) {
        [self.methodChannel invokeMethod:method arguments: data];
    }
}

@end
