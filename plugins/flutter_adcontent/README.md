<h1 align="center">Flutter AdContent Pro</h1>
<h5 align="center">一款优质的穿山甲内容输出插件，支持「短剧」和「小视频」，可加入(激励视频、视频流广告、信息流广告)</h5>

<p align="center">
<a href="https://pub.dev/packages/flutter_adcontent">
<img src="https://img.shields.io/pub/v/flutter_adcontent?logo=dart"/>
<img src="https://img.shields.io/badge/OS-iOS%20%7C%20Android-blue?logo=preact"/>
</a>
<a href="https://github.com/FlutterAds/flutter_adcontent">
<img src="https://github.com/FlutterAds/flutter_adcontent/actions/workflows/flutter.yml/badge.svg">
<img src=https://img.shields.io/github/stars/FlutterAds/flutter_adcontent?color=brightgreen>
</a>
<a href="https://flutterads.top/">
<img src="https://img.shields.io/badge/Pro-v1.6.1-success?logo=flutter&logoColor=FFD700"/>
<a href="https://flutterads.top/">
<img src="https://img.shields.io/badge/Site-FlutterAds-success?logo=webtrees&logoColor=FFD700"/>
</a>
</p>

## 💻 支持平台

- 穿山甲-短剧
- 穿山甲-小视频
- 穿山甲-小说(短故事)
- 安卓
- iOS

## 🚀 核心功能

- 🎬 短剧
- 📱 小视频
- 📚 小说(短故事)
- 🎮 广告变现
    - 激励视频广告
    - 信息流广告
    - 视频流广告
- 🎉 【独家支持】可 Widget 组件嵌入Flutter 页面
- 🎨 【独家支持】可自定义 UI 样式，与业务完美契合
- 📦 【独家支持】广告事件回调全局打通

## 🎬 示例效果

|短剧剧场|短剧滑滑流|短剧详情|激励解锁|
|--|--|--|--|
| <img src='https://flutterads.top/imgs/adcontent/img_theater_widget.webp'/> | <img src='https://flutterads.top/imgs/adcontent/img_theater_widget2.webp'/> | <img src='https://flutterads.top/imgs/adcontent/img_detail.webp'/>| <img src='https://flutterads.top/imgs/adcontent/img_detail_reward.webp'/>|
|推荐和所有|短剧分类|短剧搜素|观看历史|
| <img src='https://flutterads.top/imgs/adcontent/img_theater_11.webp'/> | <img src="https://flutterads.top/imgs/adcontent/img_theater_12.webp"/>| <img src='https://flutterads.top/imgs/adcontent/img_theater_13.webp'/>| <img src='https://flutterads.top/imgs/adcontent/img_theater_14.webp'/> |
|全屏模式|宫格模式|双列模式|广告展示|
| <img src='https://flutterads.top/imgs/adcontent/img_video_01.webp'/> | <img src='https://flutterads.top/imgs/adcontent/img_video_03.webp'/> | <img src='https://flutterads.top/imgs/adcontent/img_video_04.webp'/>| <img src='https://flutterads.top/imgs/adcontent/img_video_02.webp'/>| 


## 📃 接入文档

- [ 🎯 极速接入、快速体验、永久更新](https://flutterads.top/start/guide/adcontent/install.html)

- [ 💰 变现套装 = 【GroMore】+【AdSpark】+【AdContent】](https://flutterads.top/)

## 📌 广告系列插件（FlutterAds）
|插件|描述|
|-|-|
|[flutter_gromore_pro](https://flutterads.top/)|🏆🏆🏆 帮你大幅提升广告收益，发挥出最大的用户价值|
|[flutter_gromore_ads](https://github.com/FlutterAds/flutter_gromore_ads)|字节跳动、穿山甲、GroMore 聚合 Flutter 广告开源版插件|
|[flutter_pangle_ads](https://github.com/FlutterAds/flutter_pangle_ads)|字节跳动、穿山甲 Flutter 广告插件|
|[flutter_qq_ads](https://github.com/FlutterAds/flutter_qq_ads)|腾讯广告、广点通、优量汇 Flutter 广告插件|
|[flutter_adspark](https://github.com/FlutterAds/flutter_adspark)|巨量广告/穿山甲的广告监测、增长分析、归因上报、事件管理 Flutter 版插件|
|[flutter_adcontent](https://github.com/FlutterAds/flutter_adcontent)|穿山甲内容输出 Flutter 版插件，支持短剧和小视频|

