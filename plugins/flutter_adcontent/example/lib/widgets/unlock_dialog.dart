import 'package:flutter/material.dart';

/// 解锁弹窗
class UnlockDialog extends StatelessWidget {
  final String title;
  final String indexText;
  final String unlockText;
  final String tipText;
  final VoidCallback onUnlock;
  final VoidCallback onClose;

  const UnlockDialog({
    Key? key,
    required this.title,
    required this.indexText,
    required this.unlockText,
    required this.tipText,
    required this.onUnlock,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      // backgroundColor: const Color(0xBD000000),
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: PopScope(
        // 设置无法返回来关闭弹窗
        canPop: false,
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Image.asset(
              "assets/imgs/unlock_dialog_bg.png",
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(height: 68),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFCEBB8),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        indexText,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFCEBB8),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        "assets/imgs/unlock_dialog_icon_unlock.png",
                        width: 26,
                        height: 26,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        unlockText,
                        style: const TextStyle(
                          fontSize: 26,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFCEBB8),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Text(
                    tipText,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFFFAEFAE),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 20),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: onUnlock,
                    child: Image.asset(
                      "assets/imgs/unlock_dialog_btn_unlock.png",
                      width: 200,
                      height: 62,
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 20,
              child: InkWell(
                onTap: onClose,
                borderRadius: BorderRadius.circular(20),
                child: CircleAvatar(
                  backgroundColor: Colors.transparent,
                  radius: 20,
                  child: Image.asset(
                    "assets/imgs/unlock_dialog_icon_close.png",
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
