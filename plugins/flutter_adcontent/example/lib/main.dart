import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_adcontent_example/app_config.dart';
import 'package:flutter_adcontent_example/home.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

void main() async {
  // 绑定引擎
  WidgetsFlutterBinding.ensureInitialized();
  await init();
  runApp(const MyApp());
}

/// 初始化
Future<void> init() async {
  // 事件监听
  setAdEvent();
  // 先初始化广告插件，再初始化短剧插件
  // FlutterAds 所有的插件保证兼容性，与其他第三方使用时，不保证兼容性
  bool adResult = await FlutterGromoreAds.initAd(
    AppConfig.appId,
    useMediation: false,
  );
  if (!adResult) {
    print("初始化穿山甲失败");
    return;
  }
  print("初始化穿山甲成功");
  // 初始化短剧小视频
  bool result = await FlutterAdcontent.init(
    initSkit: true, // 初始化短剧
    initVideo: true, // 初始化小视频
    initNovel: true, // 初始化短故事(小说)
    isOnlyICPNumber: true, // 是否只展示有 ICP 备案号内容
    isTeenagerMode: false, // 是否开启青少年模式
    settingFile: AppConfig.settingFile, // 配置文件，不需要传.json
  );
  print("初始化短剧:$result");
}

/// 设置广告监听
Future<void> setAdEvent() async {
  FlutterGromoreAds.onEventListener((event) {
    // 广告相关的事件都会回调到这里，无论是 GroMore 还是短剧都是
    debugPrint('onEventListener adId:${event.adId} action:${event.action} ');
    if (event is AdRewardEvent) {
      // 这里是激励视频的回调
      print(
          "onEventListener AdRewardEvent rewardVerify:${event.rewardVerify} rewardType:${event.rewardType}");
    } else if (event is AdErrorEvent) {
      print(
          "onEventListener AdErrorEvent code:${event.errCode} message:${event.errMsg}");
    }
  });
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Banner(
        message: 'Pro',
        location: BannerLocation.topEnd,
        child: HomePage(),
      ),
    );
  }
}
