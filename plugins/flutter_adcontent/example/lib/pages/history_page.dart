import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';

import 'drama_widgets.dart';

/// 历史记录页面
class HistoryPage extends StatefulWidget {
  const HistoryPage({Key? key}) : super(key: key);

  @override
  _HistoryPageState createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  // 短剧列表
  List<Drama> dramaList = [];

  @override
  void initState() {
    getDramaHistory();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('历史记录'),
        actions: [
          IconButton(
            onPressed: () {
              clearHistory();
            },
            icon: const Icon(Icons.cleaning_services_outlined),
          ),
        ],
      ),
      body: dramaList.isEmpty
          ? const EmptyPageCenter(title: '还没看过短剧哦，快去看看吧！')
          : ListView.separated(
              padding: const EdgeInsets.all(10),
              itemBuilder: (context, index) {
                return DramaListItem(drama: dramaList[index]);
              },
              separatorBuilder: (context, index) => const Divider(height: 0.5),
              itemCount: dramaList.length,
            ),
    );
  }

  /// 请求短剧列表
  Future<void> getDramaHistory() async {
    List<Drama> list = await FlutterAdcontent.getDramaHistory();
    // 刷新列表
    setState(() {
      dramaList = list;
    });
  }

  /// 清除历史记录
  Future<void> clearHistory() async {
    await FlutterAdcontent.clearDramaHistory();
    // 刷新列表
    setState(() {
      dramaList = [];
    });
  }
}
