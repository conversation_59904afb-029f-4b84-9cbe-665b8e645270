import 'package:flutter/material.dart';
import 'package:flutter_adcontent/entity/novel_story.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_adcontent_example/app_config.dart';
import 'package:flutter_adcontent_example/pages/drama_widgets.dart';
import 'package:flutter_adcontent_example/pages/favor_page.dart';

import 'history_page.dart';
import 'novel/novel_test_page.dart';
import 'video_page.dart';

/// 我的页面
class MyPage extends StatefulWidget {
  const MyPage({Key? key, required this.title}) : super(key: key);
  final String title;

  @override
  _MyPageState createState() => _MyPageState();
}

class _MyPageState extends State<MyPage> {
  int count = 0;
  // 短剧列表
  List<Drama> dramaList = [];
  // 是否已登录
  bool isLogin = false;

  late ProVideoController novelController;

  @override
  void initState() {
    novelController = ProVideoController();
    isLoginSuccess();
    super.initState();
  }

  /// 构建标题
  List<Widget> buildTitle(String title) {
    return [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ),
      const Divider(
        endIndent: 280,
        height: 0.5,
      ),
    ];
  }

  /// 构建项目
  List<Widget> buildItem(
      {required String title, String? subtitle, required VoidCallback onTap}) {
    return [
      ListTile(
        onTap: onTap,
        title: Text(title),
        subtitle: subtitle == null ? null : Text(subtitle),
        dense: true,
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
        ),
      ),
      const Divider(height: 0.5),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('我的'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...buildTitle("🧑‍💻 用户"),
            ...buildItem(
              title: "登录",
              subtitle: isLogin ? "已登录：FlutterAds" : "未登录",
              onTap: login,
            ),
            ...buildItem(
              title: "登出",
              onTap: logout,
            ),
            ...buildTitle("🎬 短剧"),
            ...buildItem(
              title: "短剧聚合页",
              onTap: () {
                // 打开剧场页面（短剧聚合页）
                // FlutterAdcontent.showTheaterPage(
                //   showBackBtn: true,
                //   showPageTitle: true,
                //   showChangeBtn: true,
                //   detailFree: 10,
                // );
                toTheater(
                  context,
                  channelType: VideoDrawParams.drawChannelTypeTheater,
                  setTopOffset: 84,
                );
              },
            ),
            ...buildItem(
              title: "短剧滑滑流",
              onTap: () {
                // 打开短剧滑滑流
                // FlutterAdcontent.showDrawPage(
                //   hideInfo: true,
                //   detailFree: 3,
                // );
                toTheater(
                  context,
                  channelType: VideoDrawParams.drawChannelTypeRecommend,
                  setTopOffset: 54,
                );
              },
            ),
            ...buildItem(
              title: "短剧详情页",
              onTap: () {
                // 打开短剧详情页）
                // FlutterAdcontent.showDetailPage(
                //   id: 2328,
                //   index: 1,
                //   groupId: "136223",
                //   hideTopInfo: false,
                //   hideBack: false,
                //   setTopOffset: 20,
                //   detailFree: 5,
                //   hideLikeButton: true,
                //   hideFavorButton: true,
                // );
                toDetail(
                    context,
                    Drama(
                      id: 2328,
                      title: "绝世殿主下山",
                      index: 1,
                      desc: "测试短剧内容",
                      coverImage: '',
                      status: 0,
                      total: 100,
                      type: '1',
                      typeId: 1,
                      createTime: DateTime.now().millisecondsSinceEpoch,
                      actionTime: DateTime.now().millisecondsSinceEpoch,
                      freeSet: 2,
                      lockSet: 5,
                      icpNumber: '1234567890',
                      isFavor: true,
                      favoriteTime: 1710588717,
                      favoriteCount: 100,
                      levelLabel: 1,
                      isPotential: true,
                    ));
              },
            ),
            ...buildItem(
                title: "历史记录",
                onTap: () {
                  // 打开历史记录页面
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const HistoryPage(),
                    ),
                  );
                }),
            ...buildItem(
                title: "收藏记录",
                onTap: () {
                  // 打开收藏记录页面
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const FavorPage(),
                    ),
                  );
                }),
            ...buildItem(
                title: "设置全局倍速～2x",
                onTap: () {
                  FlutterAdcontent.setGlobalSpeedPlay(2);
                }),
            ...buildTitle("📱 小视频"),
            ...buildItem(
              title: "全屏小视频",
              subtitle: '请查看 Tab 栏【小视频】',
              onTap: () {
                // 打开全屏小视频
              },
            ),
            ...buildItem(
                title: "宫格小视频",
                onTap: () {
                  // 打开宫格小视频
                  goPage(const VideoPage(
                    title: "宫格小视频",
                    style: VideoDrawParams.drawStyleGrid,
                  ));
                }),
            ...buildItem(
                title: "双 Feed 流小视频",
                onTap: () {
                  // 打开双 Feed 流小视频
                  goPage(
                    const VideoPage(
                      title: "双 Feed 流",
                      style: VideoDrawParams.drawStyleDoubleFeed,
                    ),
                  );
                }),
            ...buildTitle("📚 短故事"),
            ...buildItem(
              title: "打开短故事阅读页",
              onTap: () {
                // 短故事实体
                NovelStory novelStory = NovelStory(
                  id: 2887,
                  title: "繁花绝",
                  desc:
                      "我叫元祯，本是女儿身，却因生命的错位被推上了大魏皇帝的宝座。原以为，萧寒将永远成为心头的朱砂痣，成为窗前那一抹白月光，我只能独自品尝暗恋的苦涩。可命运，却再次捉弄了我！今生，我注定要颠沛流离，孤苦一生……",
                  author: "一江明月",
                  coverImage:
                      "https://p3-csj-sign.byteimg.com/tos-cn-i-4g66r8cj84/929049a100ce42f0b60edf31d44536e7~tplv-4g66r8cj84-jpeg.image?rk3s=363bf5d8&x-…",
                  imageType: 0,
                  categoryId: 32,
                  categoryName: "古代言情",
                  total: 42,
                  createTime: 1714495559,
                  index: 1,
                  progress: 0.0,
                  statsCount: 111461,
                  isFavorite: false,
                  favoriteTime: "0",
                  actionTime: 0,
                );
                // 打开短故事阅读页
                FlutterAdcontent.openNovelReaderPage(
                  novelStory,
                  // rewardCodeId: '961264507',
                  rewardAdMode: 1,
                  controller: novelController,
                  unlockFlowListener: UnlockFlowListener(
                    unlockFlowStart: (data) {
                      var novel = NovelStory.fromJson(data);
                      print('MyPage unlockFlowStart title: ${novel.title}');
                    },
                    unlockFlowEnd: (errCode, errMsg) {
                      print(
                          'MyPage unlockFlowEnd errCode:$errCode errMsg:$errMsg');
                    },
                    showCustomAd: () {
                      print('MyPage showCustomAd');
                      showCustomAd();
                    },
                  ),
                );
              },
            ),
            ...buildItem(
              title: "短故事接口列表",
              onTap: () {
                // 打开短故事测试页面
                goPage(const NovelTestPage());
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 登录
  Future<void> login() async {
    // 1、获取登录签名
    String nonce = FlutterAdcontent.getNonce();
    int time = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    String ouid = "E932453D-D16B-43B1-A273-DCB1B3918E58";
    String signStr = await FlutterAdcontent.getSignString(
        AppConfig.skitSecureKey, nonce, time, ouid);
    if (signStr.isEmpty) {
      print("短剧 login signStr is empty");
      return;
    }
    print("短剧 login signStr:$signStr");
    // 2、开始登录
    bool result = await FlutterAdcontent.login(signStr);
    print("短剧 login result:$result signStr:$signStr");
    isLoginSuccess();
  }

  /// 是否已登录
  Future<void> isLoginSuccess() async {
    bool result = await FlutterAdcontent.isLogin();
    print("短剧 isLogin result:$result");
    setState(() {
      isLogin = result;
    });
  }

  /// 登出
  Future<void> logout() async {
    bool result = await FlutterAdcontent.logout();
    print("短剧 logout result:$result");
    isLoginSuccess();
  }

  /// 去页面
  Future<void> goPage(Widget page) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => page,
      ),
    );
  }

  /// 自定义广告解锁短剧
  /// 设置展示价格
  Future<void> setCustomAdOnShow() async {
    novelController.setCustomAdOnShow("1000");
    print('VideoPlayListener setCustomAdOnShow 1000');
  }

  /// 自定义广告解锁短剧
  /// 设置激励结果
  Future<void> setCustomAdOnReward(bool verify) async {
    novelController.setCustomAdOnReward(verify, extraData: {"key": "value"});
    print('MyPage setCustomAdOnReward $verify');
  }

  /// 调用自定义的广告
  Future<void> showCustomAd() async {
    // 这里调用广告
    // FlutterGromoreAds.showRewardVideoAd('787678678');
    // 假设广告调用成功
    // 1、先设置广告价格
    setCustomAdOnShow();
    await Future.delayed(const Duration(seconds: 3));
    // 2、设置激励结果
    setCustomAdOnReward(true);
  }
}
