import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';

import 'drama_widgets.dart';

/// 收藏记录页面
class FavorPage extends StatefulWidget {
  const FavorPage({Key? key}) : super(key: key);

  @override
  _FavorPageState createState() => _FavorPageState();
}

class _FavorPageState extends State<FavorPage> {
  // 短剧列表
  List<Drama> dramaList = [];

  @override
  void initState() {
    getDramaFavor();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('收藏记录'),
      ),
      body: dramaList.isEmpty
          ? const EmptyPageCenter(title: '还没收藏过短剧哦，快去看看吧！')
          : ListView.separated(
              padding: const EdgeInsets.all(10),
              itemBuilder: (context, index) {
                return DramaListItem(drama: dramaList[index]);
              },
              separatorBuilder: (context, index) => const Divider(height: 0.5),
              itemCount: dramaList.length,
            ),
    );
  }

  /// 请求短剧列表
  Future<void> getDramaFavor() async {
    List<Drama> list = await FlutterAdcontent.getFavorList();
    // 刷新列表
    setState(() {
      dramaList = list;
    });
  }

  /// 清除历史记录
  Future<void> clearHistory() async {
    await FlutterAdcontent.clearDramaHistory();
    // 刷新列表
    setState(() {
      dramaList = [];
    });
  }
}
