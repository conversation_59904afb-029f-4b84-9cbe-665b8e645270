import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_adcontent_example/pages/ui_utils.dart';

import 'drama_widgets.dart';
import 'search_page.dart';

/// 剧场页面
class TheaterPage extends StatefulWidget {
  const TheaterPage({Key? key, required this.title}) : super(key: key);
  final String title;

  @override
  _TheaterPageState createState() => _TheaterPageState();
}

class _TheaterPageState extends State<TheaterPage>
    with SingleTickerProviderStateMixin {
  // 短剧列表
  List<Drama> dramaList = [];
  // 短剧分类列表,请求接口为：requestCategoryList(); 这里测试数据就写死了。
  List<String> categoryList = ["都市", "现言", "玄幻", "热血"];
  // 选中分类
  String category = '都市';
  // TabController
  late TabController _tabController;

  @override
  void initState() {
    requestDramaByCategory(category);
    super.initState();
    _tabController = TabController(length: categoryList.length, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    double itemAspectRatio = UiUtils.childAspectRatio(
      context,
      spacing: 10 * 3,
      itemHeight: (280 + 60),
    );
    print("itemAspectRatio: $itemAspectRatio");

    return Scaffold(
      appBar: AppBar(
        title: const Text('短剧'),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SearchPage(),
                ),
              );
            },
            icon: const Icon(Icons.search),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelPadding: const EdgeInsets.symmetric(
            vertical: 10,
            horizontal: 10,
          ),
          labelStyle: Theme.of(context).textTheme.titleSmall,
          tabs: categoryList.map((e) => Text(e)).toList(),
          onTap: (value) {
            requestDramaByCategory(categoryList[value]);
          },
        ),
      ),
      body: GridView.builder(
          padding: const EdgeInsets.all(10),
          itemBuilder: (context, index) {
            Drama drama = dramaList[index];
            return DramaCard(
              drama: drama,
              imgHeight: 280,
              imgWidth: double.maxFinite,
            );
          },
          itemCount: dramaList.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: itemAspectRatio,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
          )),
    );
  }

  /// 请求分类列表
  Future<void> requestCategoryList() async {
    List<String> list = await FlutterAdcontent.requestDramaCategoryList();
    setState(() {
      categoryList = list;
    });
  }

  /// 请求短剧列表
  Future<void> requestDramaByCategory(String category) async {
    setState(() {
      this.category = category;
    });
    List<Drama> list = await FlutterAdcontent.requestDramaByCategory(
      category: category,
    );
    // 清空现有列表
    dramaList = [];
    // 刷新列表
    setState(() {
      dramaList.addAll(list);
    });
  }
}
