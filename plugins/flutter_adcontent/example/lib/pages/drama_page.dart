import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:loadany/loadany.dart';

import 'drama_widgets.dart';
import 'ui_utils.dart';

/// 短剧页面
class DramaPage extends StatefulWidget {
  const DramaPage({Key? key, required this.title}) : super(key: key);
  final String title;

  @override
  _DramaPageState createState() => _DramaPageState();
}

class _DramaPageState extends State<DramaPage> with KeepAliveParentDataMixin {
  int page = 1;
  // 热门短剧列表
  List<Drama> hotDramaList = [];
  // 热门短剧列表
  List<Drama> allDramaList = [];
  // 加载状态
  LoadStatus status = LoadStatus.normal;

  @override
  void initState() {
    requestHot();
    requestAll();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // 热门宽高比
    double hotItemWidth = (UiUtils.screenWidth(context) - 10 * 3) / 3;
    double hotHeight = hotItemWidth * 2 + 100;
    // 精选宽高比
    double itemAspectRatio = UiUtils.childAspectRatio(
      context,
      spacing: 10 * 3,
      itemHeight: (280 + 60),
    );
    print("hotItemWidth:$hotItemWidth itemAspectRatio: $itemAspectRatio");
    return Scaffold(
      appBar: AppBar(
        title: const Text('剧场'),
        actions: [
          IconButton(
            onPressed: () {
              FlutterAdcontent.showTheaterPage(
                showBackBtn: true,
                showPageTitle: true,
                showChangeBtn: true,
                detailFree: 3,
              );
            },
            icon: const Text(
              "🎬",
              style: TextStyle(
                fontSize: 20,
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(10),
        child: LoadAny(
          status: status,
          onLoadMore: () async {
            requestAll();
          },
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Text(
                    "🔥 热门",
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Container(
                  height: hotHeight,
                  color: Colors.grey[80],
                  width: double.maxFinite,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          for (var item in hotDramaList)
                            SizedBox(
                              width: hotItemWidth,
                              child: DramaCard(
                                drama: item,
                                imgWidth: hotItemWidth,
                                imgHeight: hotItemWidth * 2,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      TextButton(
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          minimumSize: const Size(double.infinity, 40),
                        ),
                        child: const Text("换一换"),
                        onPressed: () {
                          requestHot();
                        },
                      ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Text(
                    "📺 精选",
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
              ),
              SliverGrid.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: itemAspectRatio,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                  ),
                  itemBuilder: (context, index) {
                    Drama drama = allDramaList[index];
                    return DramaCard(
                      drama: drama,
                      imgHeight: 280,
                      imgWidth: double.maxFinite,
                    );
                  },
                  itemCount: allDramaList.length),
            ],
          ),
        ),
      ),
    );
  }

  /// 请求热门短剧列表
  Future<void> requestHot() async {
    List<Drama> list = await FlutterAdcontent.requestAllDramaByRecommend(
      page: 1,
      count: 3,
    );
    // 添加到列表
    hotDramaList = list;
    setState(() {});
  }

  /// 请求热门短剧列表
  Future<void> requestAll() async {
    setState(() {
      status = LoadStatus.loading;
    });
    List<Drama> list = await FlutterAdcontent.requestAllDrama(
      page: page,
      count: 10,
      order: 0,
    );
    if (list.isEmpty || list.length < 10) {
      status = LoadStatus.completed;
    } else {
      status = LoadStatus.normal;
    }
    page += 1;
    // 添加到列表
    allDramaList.addAll(list);
    setState(() {});
  }

  @override
  void detach() {}

  @override
  bool get keptAlive => true;
}
