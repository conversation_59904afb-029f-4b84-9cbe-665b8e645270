import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';

import 'drama_detail.dart';
import 'drama_theater.dart';

/// 短剧卡片
class DramaCard extends StatelessWidget {
  const DramaCard({
    Key? key,
    required this.drama,
    this.imgWidth = 120,
    this.imgHeight = 240,
  }) : super(key: key);

  final Drama drama;
  final double imgWidth;
  final double imgHeight;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        toDetail(context, drama);
      },
      child: Column(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.network(
              drama.coverImage,
              width: imgWidth,
              height: imgHeight,
              fit: BoxFit.cover,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(
              drama.title,
              maxLines: 1,
              overflow: TextOverflow.fade,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
            ),
          ),
          Text(
            "${drama.status == 0 ? '已完结' : '连载中'}(${drama.total}集)",
            maxLines: 1,
            overflow: TextOverflow.fade,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ],
      ),
    );
  }
}

/// 短剧列表项
class DramaListItem extends StatelessWidget {
  const DramaListItem({
    Key? key,
    required this.drama,
  }) : super(key: key);

  final Drama drama;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        toDetail(context, drama);
      },
      title: Text("${drama.title}~(${drama.index}/${drama.total}集)"),
      subtitle: Text(
        drama.desc,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      leading: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image.network(
          drama.coverImage,
          width: 60,
          height: 60,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}

/// 空页面
class EmptyPageCenter extends StatelessWidget {
  const EmptyPageCenter({
    Key? key,
    required this.title,
  }) : super(key: key);

  final String title;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.code, size: 100, color: Colors.grey),
          Text(title),
        ],
      ),
    );
  }
}

/// 去短剧详情页
Future<T?> toDetail<T extends Object?>(
    BuildContext context, Drama drama) async {
  return await Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => DramaDetail(drama: drama),
    ),
  );
  // FlutterAdcontent.showDetailPage(
  //   id: drama.id,
  //   index: drama.index,
  //   groupId: "1",
  //   hideTopInfo: false,
  //   setTopOffset: 20,
  // );
}

/// 图剧场页面
void toTheater(BuildContext context,
    {int channelType = VideoDrawParams.drawChannelTypeRecommend,
    int setTopOffset = 54}) {
  Navigator.of(context).push(MaterialPageRoute(
    builder: (context) => DramaTheater(
      channelType: channelType,
      setTopOffset: setTopOffset,
    ),
  ));
}
