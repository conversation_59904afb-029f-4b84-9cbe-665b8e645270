import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';

/// 页面
class NovelHomePage extends StatefulWidget {
  const NovelHomePage({Key? key}) : super(key: key);

  @override
  _NovelHomePageState createState() => _NovelHomePageState();
}

class _NovelHomePageState extends State<NovelHomePage> {
  late ProVideoController controller;

  @override
  void initState() {
    controller = ProVideoController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          // 获取 body 的大小
          var bodyWidth = constraints.maxWidth;
          var bodyHeight = constraints.maxHeight;
          print(
              'NovelHomePage build constraints: $constraints bodyWidth:$bodyWidth bodyHeight:$bodyHeight');
          return NovelStoryWidget(
            controller: controller,
            width: bodyWidth,
            height: bodyHeight,
            setTopOffset: 40,
            canPullRefresh: true,
            recPageSize: 9,
            rewardCodeId: '961264507', // 仅 Android 支持
            rewardAdMode: 1,
            novelStoryHomeListener: NovelStoryHomeListener(
              onItemClick: (novelStory) {
                // iOS 没有这个事件
                print('NovelHomePage onItemClick: ${novelStory.toJson()}');
              },
            ),

            novelStoryListener: NovelStoryListener(
              onEnter: (novelStory) {
                // iOS 没有这个事件
                print('NovelStoryListener onEnter: ${novelStory.toJson()}');
              },
              onExit: (novelStory) {
                // iOS 没有这个事件
                print('NovelStoryListener onExit: ${novelStory.toJson()}');
              },
              onPageSelected: (novelStory) {
                // iOS 没有这个事件
                print(
                    'NovelStoryListener onPageSelected: ${novelStory.toJson()}');
              },
              onBookEnd: (novelStory) {
                print('NovelStoryListener onBookEnd: ${novelStory.toJson()}');
              },
            ),
            unlockFlowListener: UnlockFlowListener(
              unlockFlowStart: (data) {
                var novel = NovelStory.fromJson(data); // iOS 获取不到
                print('NovelHomePage unlockFlowStart title: ${novel.title}');
              },
              unlockFlowEnd: (errCode, errMsg) {
                print(
                    'NovelHomePage unlockFlowEnd errCode:$errCode errMsg:$errMsg');
              },
              showCustomAd: () {
                print('NovelHomePage showCustomAd');
                showCustomAd();
              },
            ),
          );
        },
      ),
    );
  }

  /// 自定义广告解锁短剧
  /// 设置展示价格
  Future<void> setCustomAdOnShow() async {
    controller.setCustomAdOnShow("1000");
    print('VideoPlayListener setCustomAdOnShow 1000');
  }

  /// 自定义广告解锁短剧
  /// 设置激励结果
  Future<void> setCustomAdOnReward(bool verify) async {
    controller.setCustomAdOnReward(verify, extraData: {"key": "value"});
    print('VideoPlayListener setCustomAdOnReward $verify');
  }

  /// 调用自定义的广告
  Future<void> showCustomAd() async {
    // 这里调用广告
    // FlutterGromoreAds.showRewardVideoAd('787678678');
    // 假设广告调用成功
    // 1、先设置广告价格
    setCustomAdOnShow();
    await Future.delayed(const Duration(seconds: 3));
    // 2、设置激励结果
    setCustomAdOnReward(true);
  }
}
