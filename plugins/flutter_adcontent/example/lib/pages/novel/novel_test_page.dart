import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';

/// 短故事测试页面
class NovelTestPage extends StatefulWidget {
  const NovelTestPage({Key? key}) : super(key: key);

  @override
  _NovelTestPageState createState() => _NovelTestPageState();
}

class _NovelTestPageState extends State<NovelTestPage> {
  // 测试用的故事ID
  final int testStoryId = 2887;
  // 搜索关键词控制器
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('短故事接口列表'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildSection(
              '分类相关',
              [
                ElevatedButton(
                  onPressed: _getStoryCategoryList,
                  child: const Text('获取类目列表'),
                ),
                ElevatedButton(
                  onPressed: _requestStoryByCategory,
                  child: const Text('根据类目请求短故事'),
                ),
              ],
            ),
            _buildSection(
              '搜索相关',
              [
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: '请输入搜索关键词',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _searchStory(false),
                        child: const Text('精确搜索'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _searchStory(true),
                        child: const Text('模糊搜索'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            _buildSection(
              'ID相关',
              [
                ElevatedButton(
                  onPressed: _requestStoryByIds,
                  child: const Text('根据ID请求短故事'),
                ),
              ],
            ),
            _buildSection(
              '精选/推荐',
              [
                ElevatedButton(
                  onPressed: _requestStoryFeed,
                  child: const Text('获取精选列表'),
                ),
              ],
            ),
            _buildSection(
              '历史记录',
              [
                ElevatedButton(
                  onPressed: _getStoryHistory,
                  child: const Text('获取历史记录'),
                ),
              ],
            ),
            _buildSection(
              '收藏相关',
              [
                ElevatedButton(
                  onPressed: _storyFavorite,
                  child: const Text('收藏短故事'),
                ),
                ElevatedButton(
                  onPressed: _storyFavoriteCancel,
                  child: const Text('取消收藏'),
                ),
                ElevatedButton(
                  onPressed: _getStoryFavorite,
                  child: const Text('获取收藏列表'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ...children,
        const SizedBox(height: 16),
      ],
    );
  }

  // 根据类目请求短故事
  Future<void> _requestStoryByCategory() async {
    try {
      final stories = await FlutterAdcontent.requestStoryByCategory(
        id: 32,
        order: 0,
        page: 1,
        count: 20,
      );
      _showResult('根据类目请求短故事', stories);
    } catch (e) {
      _showError('根据类目请求短故事失败', e);
    }
  }

  // 根据ID请求短故事
  Future<void> _requestStoryByIds() async {
    try {
      final stories = await FlutterAdcontent.requestStoryByIds(
        ids: [testStoryId],
        page: 1,
        count: 20,
      );
      _showResult('根据ID请求短故事', stories);
    } catch (e) {
      _showError('根据ID请求短故事失败', e);
    }
  }

  // 获取精选列表
  Future<void> _requestStoryFeed() async {
    try {
      final stories = await FlutterAdcontent.requestStoryFeed(
        order: 0,
        count: 20,
        page: 1,
      );
      _showResult('获取精选列表', stories);
    } catch (e) {
      _showError('获取精选列表失败', e);
    }
  }

  // 获取历史记录
  Future<void> _getStoryHistory() async {
    try {
      final stories = await FlutterAdcontent.getStoryHistory(
        page: 1,
        count: 20,
      );
      _showResult('获取历史记录', stories);
    } catch (e) {
      _showError('获取历史记录失败', e);
    }
  }

  // 收藏短故事
  Future<void> _storyFavorite() async {
    try {
      final result = await FlutterAdcontent.storyFavorite(testStoryId);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('收藏${result ? '成功' : '失败'}')),
      );
    } catch (e) {
      _showError('收藏失败', e);
    }
  }

  // 取消收藏
  Future<void> _storyFavoriteCancel() async {
    try {
      final result = await FlutterAdcontent.storyFavoriteCancel(testStoryId);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('取消收藏${result ? '成功' : '失败'}')),
      );
    } catch (e) {
      _showError('取消收藏失败', e);
    }
  }

  // 获取收藏列表
  Future<void> _getStoryFavorite() async {
    try {
      final stories = await FlutterAdcontent.getStoryFavorite(
        page: 1,
        count: 20,
      );
      _showResult('获取收藏列表', stories);
    } catch (e) {
      _showError('获取收藏列表失败', e);
    }
  }

  // 显示结果
  void _showResult(String title, List<NovelStory>? stories) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Text(
            stories == null
                ? '获取数据失败'
                : stories.isEmpty
                    ? '暂无数据'
                    : stories.map((e) => e.title).join('\n'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 显示错误
  void _showError(String title, dynamic error) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$title: $error')),
    );
  }

  // 获取类目列表
  Future<void> _getStoryCategoryList() async {
    try {
      final categories = await FlutterAdcontent.getStoryCategoryList();
      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('类目列表'),
          content: SingleChildScrollView(
            child: Text(
              categories.isEmpty
                  ? '获取数据失败'
                  : categories.isEmpty
                      ? '暂无数据'
                      : categories.map((e) => '${e.toJson()}').join('\n'),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('确定'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showError('获取类目列表失败', e);
    }
  }

  // 搜索短故事
  Future<void> _searchStory(bool isFuzzy) async {
    if (_searchController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入搜索关键词')),
      );
      return;
    }

    try {
      final stories = await FlutterAdcontent.searchStory(
        query: _searchController.text.trim(),
        isFuzzy: isFuzzy,
        page: 1,
        count: 20,
      );
      _showResult(isFuzzy ? '模糊搜索结果' : '精确搜索结果', stories);
    } catch (e) {
      _showError('搜索失败', e);
    }
  }
}
