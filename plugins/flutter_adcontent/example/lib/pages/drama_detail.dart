import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter/widgets.dart'; // Add this import for WidgetsBindingObserver
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

import '../widgets/unlock_dialog.dart';
import 'ui_utils.dart';

/// 短剧页面
class DramaDetail extends StatefulWidget {
  const DramaDetail({Key? key, required this.drama}) : super(key: key);
  final Drama drama;

  @override
  _VideoPage2State createState() => _VideoPage2State();
}

class _VideoPage2State extends State<DramaDetail> with WidgetsBindingObserver {
  late ProVideoController controller;
  int currentIndex = 1; // 当前已播放的集数
  int newIndex = 1; // 当前最新的集数

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Add observer
    controller = ProVideoController();
    getUnlockStatus();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // Remove observer
    // controller.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      controller.pause(); // Pause video when app goes to background
    } else if (state == AppLifecycleState.resumed) {
      controller.resume(); // Resume video when app comes to foreground
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽高
    Size size = UiUtils.screenSize(context);
    return Scaffold(
      body: Stack(
        children: [
          DramaWidget(
            controller: controller,
            width: size.width,
            height: size.height,
            id: widget.drama.id,
            index: widget.drama.index,
            groupId: "1",
            hideTopInfo: true,
            hideBottomInfo: false,
            setBottomOffset: 0,
            hideLikeButton: true,
            hideFavorButton: false,
            hideMore: true,
            unlockAdMode: 1, // 0:SDK 广告解锁 1：自定义广告解锁
            unlockCount: 2,
            unlockFlowListener: UnlockFlowListener(
              unlockFlowStart: (data) {
                var unlockDrama = Drama.fromJson(data);
                // 解锁流程开始
                print('VideoPlayListener unlockFlowStart index:${unlockDrama.index} title:${unlockDrama.title}');
                showUnlockDialog(unlockDrama);
              },
              unlockFlowEnd: (int? errCode, String? errMsg) {
                // 解锁流程结束
                print('VideoPlayListener unlockFlowEnd: errCode:$errCode errMsg:$errMsg');
                if (errCode != 200) {
                  // 解锁失败
                  backIndex();
                } else {
                  // 解锁成功
                }
              },
              showCustomAd: () {
                // 显示自定义广告
                print('VideoPlayListener showCustomAd');
                showCustomAd();
              },
            ),
            videoPlayListener: VideoPlayListener(
              onDJXPageChange: (Drama drama) {
                // 视频切换
                print('VideoPlayListener onDJXPageChange: ${drama.id}:${drama.title}:${drama.index} duration:${drama.duration}');
              },
              onDJXVideoPlay: (Drama drama) {
                // 视频播放开始
                print('VideoPlayListener onDJXVideoPlay: ${drama.id}:${drama.title}:${drama.index} duration:${drama.duration}');
                currentIndex = drama.index;
                setState(() {
                  newIndex = currentIndex;
                });
              },
              onDJXVideoPause: (Drama drama) {
                // 视频暂停
                print('VideoPlayListener onDJXVideoPause:  ${drama.id}:${drama.title}:${drama.index}');
              },
              onDJXVideoContinue: (Drama drama) {
                // 视频继续
                print('VideoPlayListener onDJXVideoContinue: ${drama.id}:${drama.title}:${drama.index}');
              },
              onDJXVideoCompletion: (Drama drama) {
                // 视频播放完成
                print('VideoPlayListener onDJXVideoCompletion: ${drama.id}:${drama.title}:${drama.index}');
              },
              onDJXVideoOver: (Drama drama) {
                // 视频结束
                print('VideoPlayListener onDJXVideoOver: ${drama.id}:${drama.title}:${drama.index}');
              },
              onVideoClose: () {
                // 视频关闭
                print('VideoPlayListener onDJXVideoClose');
              },
              onSeekTo: (duration) {
                // 拖动进度
                print("VideoPlayListener onDJXSeekTo:$duration");
              },
              onDurationChange: (duration) {
                // 播放进度
                // print("VideoPlayListener onDurationChange:$duration");
              },
              onError: (int? errCode, String? errMsg) {
                // 播放错误
                print("VideoPlayListener onError:$errCode:$errMsg");
              },
            ),
          ),
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: AppBar(
                backgroundColor: Colors.transparent,
                centerTitle: true,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
                title: Column(
                  children: [
                    Text(
                      '第 $newIndex 集',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                      ),
                    ),
                    Text(
                      widget.drama.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    )
                  ],
                ),
                actions: [
                  //选集
                  TextButton(
                    child: const Text(
                      '2x',
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    ),
                    onPressed: () {
                      setSpeed();
                    },
                  ),
                  //选集
                  IconButton(
                    icon: const Icon(Icons.list, color: Colors.white),
                    onPressed: () {
                      openDramaGallery();
                    },
                  ),
                  //更多和选集
                  IconButton(
                    icon: const Icon(Icons.more_horiz, color: Colors.white),
                    onPressed: () {
                      openMoreSetting();
                    },
                  ),
                ],
              )),
        ],
      ),
    );
  }

  /// 返回上一集
  void backIndex() {
    controller.setCurrentIndex(currentIndex);
  }

  /// 解锁短剧
  Future<void> unLockDrama(Drama drama, bool cancel) async {
    controller.unLock(drama.id, 2, cancel);
  }

  /// 自定义广告解锁短剧
  /// 设置展示价格
  Future<void> setCustomAdOnShow() async {
    controller.setCustomAdOnShow("1000");
    print('VideoPlayListener setCustomAdOnShow 1000');
  }

  /// 自定义广告解锁短剧
  /// 设置激励结果
  Future<void> setCustomAdOnReward(bool verify) async {
    controller.setCustomAdOnReward(verify, extraData: {"key": "value"});
    print('VideoPlayListener setCustomAdOnReward $verify');
  }

  /// 调用自定义的广告
  Future<void> showCustomAd() async {
    // bool bRet = await FlutterGromoreAds.showRewardVideoAd("103515414");
    // 假设广告调用成功
    // 1、先设置广告价格
    setCustomAdOnShow();
    await Future.delayed(const Duration(seconds: 3));
    // 2、设置激励结果
    // setCustomAdOnReward(true);
  }

  /// 显示 Dialog
  Future<void> showUnlockDialog(Drama drama) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UnlockDialog(
        title: '${drama.title} ',
        indexText: '第${drama.index}集',
        unlockText: '免费解锁 2 集',
        tipText: "解锁还能提高红包奖励哦~",
        onClose: () {
          Navigator.pop(context);
          unLockDrama(drama, true);
        },
        onUnlock: () {
          Navigator.pop(context);
          unLockDrama(drama, false);
        },
      ),
    );
  }

  /// 获取解锁状态列表
  Future<void> getUnlockStatus() async {
    List<UnlockStatus> unlockStatus = await FlutterAdcontent.getEpisodesStatus(
        id: widget.drama.id, freeSet: 2);
    for (var status in unlockStatus) {
      print('unlockStatus: ${status.toJson()}');
    }
  }

  /// 打开选集面板
  Future<void> openDramaGallery() async {
    controller.openDramaGallery();
  }

  /// 打开更多弹窗
  Future<void> openMoreSetting() async {
    controller.openMoreDialog();
  }

  /// 设置倍速
  Future<void> setSpeed() async {
    controller.setSpeedPlay(speed: 2, scope: 1);
  }
}
