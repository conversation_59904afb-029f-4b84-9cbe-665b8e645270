import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_adcontent_example/pages/drama_widgets.dart';
import 'package:flutter_adcontent_example/pages/history_page.dart';

import 'search_page.dart';

/// 剧场页面
class DramaTheater extends StatefulWidget {
  const DramaTheater(
      {Key? key,
      this.channelType = VideoDrawParams.drawChannelTypeRecommend,
      this.setTopOffset = 54})
      : super(key: key);

  final int channelType;
  final int setTopOffset;

  @override
  _VideoPage2State createState() => _VideoPage2State();
}

class _VideoPage2State extends State<DramaTheater> {
  late ProVideoController controller;
  int currentIndex = 1; // 当前已播放的集数
  int newIndex = 1; // 当前最新的集数

  @override
  void initState() {
    controller = ProVideoController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              print('DramaTheater build constraints: $constraints');
              // 获取 body 的大小
              var bodyWidth = constraints.maxWidth;
              var bodyHeight = constraints.maxHeight;
              return TheaterWidget(
                controller: controller,
                width: bodyWidth,
                height: bodyHeight,
                channelType: widget.channelType,
                hideTopInfo: true,
                setTopOffset: widget.setTopOffset,
                hideBottomInfo: true,
                setBottomOffset: 0,
                hideEnter: false,
                hideLikeButton: false,
                hideFavorButton: false,
                showChangeBtn: true,
                theaterListener: TheaterListener(
                  onOpenDetail: (drama) async {
                    // 暂停
                    controller.pause();
                    // 打开短剧详情
                    await toDetail(context, drama);
                    // 继续播放
                    controller.resume();
                  },
                ),
                videoPlayListener: VideoPlayListener(
                  onDJXPageChange: (drama) {
                    // 这里不支持获取信息，请使用 onDJXVideoPlay
                    print('DramaTheater onDJXPageChange: ${drama.title}');
                  },
                  onDJXVideoPlay: (drama) {
                    // 仅支持 Android 暂不支持 iOS
                    print('DramaTheater onDJXVideoPlay: ${drama.title}');
                  },
                ),
              );
            },
          ),
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: AppBar(
                backgroundColor: Colors.transparent,
                centerTitle: true,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
                actions: [
                  //选集
                  IconButton(
                    icon: const Icon(Icons.search, color: Colors.white),
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const SearchPage()));
                    },
                  ),
                  //更多和选集
                  IconButton(
                    icon: const Icon(Icons.history, color: Colors.white),
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const HistoryPage()));
                    },
                  ),
                ],
              )),
        ],
      ),
    );
  }
}
