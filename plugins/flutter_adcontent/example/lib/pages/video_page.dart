import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter/widgets.dart'; // Add this import for WidgetsBindingObserver

/// 短视频页面
class VideoPage extends StatefulWidget {
  const VideoPage(
      {Key? key,
      required this.title,
      this.style = VideoDrawParams.drawStyleFullscreen,
      this.controller})
      : super(key: key);
  final String title;
  final int style;
  final ProVideoController? controller;

  @override
  _VideoPage2State createState() => _VideoPage2State();
}

class _VideoPage2State extends State<VideoPage> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Add observer
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // Remove observer
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (widget.controller != null) {
      if (state == AppLifecycleState.paused) {
        // widget.controller!.pause(); // Pause video when app goes to background
      } else if (state == AppLifecycleState.resumed) {
        // widget.controller!.resume(); // Resume video when app comes to foreground
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽高
    Size size = MediaQuery.of(context).size;
    // 获取屏幕安全距离
    EdgeInsets padding = MediaQuery.of(context).padding;
    print('build size: $size');
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          print('build constraints: $constraints');
          // 获取 body 的大小
          var bodyWidth = constraints.maxWidth;
          var bodyHeight = constraints.maxHeight;

          return VideoWidget(
            controller: widget.controller,
            width: bodyWidth,
            height: bodyHeight,
            channelType: VideoDrawParams.drawChannelTypeRecommend,
            contentType: VideoDrawParams.drawContentTypeOnlyVideo,
            titleTopMargin: padding.top.toInt(),
            hideChannelName: true,
            hideFollow: true,
            style: widget.style,
            videoPlayListener: VideoPlayListener(
              onDPPageChange: (index) {
                print('VideoPage onDPPageChange: $index');
              },
              onVideoClose: () {
                print('VideoPage onVideoClose');
              },
              onSeekTo: (duration) {
                print('VideoPage onSeekTo: $duration');
              },
              onDurationChange: (duration) {
                print('VideoPage onDurationChange: $duration');
              },
              onError: (int? errCode, String? errMsg) {
                print('VideoPage onError: $errCode:$errMsg');
              },
              onDPVideoPlay: () {
                print('VideoPage onDPVideoPlay');
              },
              onDPVideoPause: () {
                print('VideoPage onDPVideoPause');
              },
              onDPVideoContinue: () {
                print('VideoPage onDPVideoContinue');
              },
              onDPVideoCompletion: () {
                print('VideoPage onDPVideoCompletion');
              },
              onDPVideoOver: () {
                print('VideoPage onDPVideoOver');
              },
            ),
          );
        },
      ),
    );
  }
}
