import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';

import 'drama_widgets.dart';

/// 搜索页面
class SearchPage extends StatefulWidget {
  const SearchPage({Key? key}) : super(key: key);

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  // 短剧列表
  List<Drama> dramaList = [];
  // 文字控制器
  late TextEditingController controller;

  @override
  void initState() {
    getDramaHistory();
    controller = TextEditingController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 2,
        title: SearchBar(
          controller: controller,
          hintText: "搜索",
          leading: const Icon(
            Icons.search,
            color: Colors.grey,
          ),
          constraints: const BoxConstraints(
            minHeight: 36,
            maxHeight: 36,
          ),
          elevation: WidgetStateProperty.resolveWith<double?>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return 1.0;
              }
              if (states.contains(WidgetState.hovered)) {
                return 1.0;
              }
              return 0.0; // Default elevation
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              searchDrama();
            },
            child: const Text('搜索'),
          ),
        ],
      ),
      body: dramaList.isEmpty
          ? const EmptyPageCenter(title: '没有此短剧')
          : ListView.separated(
              padding: const EdgeInsets.all(10),
              itemBuilder: (context, index) {
                return DramaListItem(drama: dramaList[index]);
              },
              separatorBuilder: (context, index) => const Divider(height: 0.5),
              itemCount: dramaList.length,
            ),
    );
  }

  /// 请求短剧列表
  Future<void> getDramaHistory() async {
    List<Drama> list = await FlutterAdcontent.getDramaHistory();
    // 刷新列表
    setState(() {
      dramaList = list;
    });
  }

  /// 搜索短剧
  Future<void> searchDrama() async {
    String query = controller.text;
    if (query.isEmpty) {
      ScaffoldMessenger.of(context)
          .showSnackBar(const SnackBar(content: Text("请输入搜索内容")));
      return;
    }
    List<Drama> list = await FlutterAdcontent.searchDrama(
      query: query,
      isFuzzy: true,
      page: 1,
      count: 10,
    );
    // 刷新列表
    setState(() {
      dramaList = list;
    });
  }
}
