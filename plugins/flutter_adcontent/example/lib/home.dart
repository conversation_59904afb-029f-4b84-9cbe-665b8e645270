import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';

import 'pages/drama_page.dart';
import 'pages/my_page.dart';
import 'pages/novel/novel_home_page.dart';
import 'pages/theater_page.dart';
import 'pages/video_page.dart';

// 首页
class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // 当前子项索引
  int currentIndex = 0;
  // 子项集
  late List<Widget> children;
  // 播放控制器
  late ProVideoController _videoController;

  @override
  void initState() {
    _videoController = ProVideoController();
    super.initState();
    // 初始化子项集合
    children = [
      const DramaPage(title: '短剧'),
      const TheaterPage(title: '剧场'),
      const NovelHomePage(),
      VideoPage(
        title: '小视频',
        controller: _videoController,
      ),
      // const DramaTheater(),
      const MyPage(title: '我的'),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 这里改为 PageView
      body: IndexedStack(
        index: currentIndex,
        children: children,
        alignment: Alignment.center,
      ),
      // 底部导航栏
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        // 当前页面索引
        currentIndex: currentIndex,
        // 导航子项集
        items: const [
          // 导航子项
          BottomNavigationBarItem(
            // 图标
            icon: Icon(Icons.video_camera_front),
            // 文字内容
            label: '剧场',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.ondemand_video_rounded),
            label: '短剧',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book_rounded),
            label: '书城',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.play_circle_outline),
            label: '小视频',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: '我的',
          ),
        ],
        onTap: (value) {
          // 点击事件，用于改变当前索引，然后刷新
          currentIndex = value;
          // 如果是小视频页面，播放视频
          if (currentIndex == 3) {
            Future.delayed(const Duration(milliseconds: 300), () {
              _videoController.resume();
            });
          } else {
            _videoController.pause();
          }
          setState(() {});
        },
      ),
    );
  }
}
