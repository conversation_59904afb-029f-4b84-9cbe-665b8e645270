plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.zero.flutter_adcontent_example"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        // applicationId "com.bytedance.djxdemo"
        applicationId "com.banjixiaoguanjia.app"
        minSdk = 24
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        sign {
//            storeFile new File("${project.projectDir}/keystore/default.keystore")
//            keyAlias "pangolin"
//            keyPassword "pangolin"
//            storePassword "pangolin"
            storeFile new File("${project.projectDir}/keystore/xgjapp.jks")
            storePassword "xmkj2020"
            keyAlias 'xgjapp'
            keyPassword "xmkj2020"
            //2个版本的签名
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.sign
            ndk {
                abiFilters 'armeabi', 'armeabi-v7a'
            }
        }

        debug {
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.sign
        }
    }
}

flutter {
    source = "../.."
}
