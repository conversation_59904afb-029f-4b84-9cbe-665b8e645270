import 'dart:async';
import 'dart:math';

import 'package:flutter/services.dart';

import 'ctrls/video_controller.dart';
import 'entity/drama.dart';
import 'entity/novel_story.dart';

export 'const/video_draw_params.dart';
export 'entity/drama.dart';
export 'entity/novel_story.dart';
export 'view/drama_widget.dart';
export 'view/theater_widget.dart';
export 'view/video_widget.dart';
export 'view/novel_story_widget.dart';
export 'ctrls/video_controller.dart';

/// 内容赋能-短剧小视频
/// 短剧：https://www.csjplatform.com/supportcenter/28151
class FlutterAdcontent {
  static const MethodChannel _channel = MethodChannel('flutter_adcontent');
  static const MethodChannel _novelChannel =
      MethodChannel('flutter_adcontent_channel_novel_story');

  /// 初始化
  /// [appId] 广告应用ID
  /// [initSkit] 是否初始化短剧
  /// [initVideo] 是否初始化小视频
  /// [initNovel] 是否初始化短故事(小说)
  /// [isOnlyICPNumber] 是否只展示有 ICP 备案号内容
  /// [isTeenagerMode] 是否开启青少年模式
  /// [settingFile] 短剧配置文件名称
  static Future<bool> init(
      {String appId = "appid",
      bool initSkit = true,
      bool initVideo = false,
      bool initNovel = false,
      bool isOnlyICPNumber = false,
      bool isTeenagerMode = false,
      String settingFile = "fileName.json"}) async {
    return await _channel.invokeMethod('init', {
      "appId": appId,
      "initSkit": initSkit,
      "initVideo": initVideo,
      "initNovel": initNovel,
      "isOnlyICPNumber": isOnlyICPNumber,
      "isTeenagerMode": isTeenagerMode,
      "settingFile": settingFile,
    });
  }

  /// 显示剧场页面
  /// [showBackBtn] 是否显示返回按钮
  /// [showPageTitle] 是否显示页面标题
  /// [showChangeBtn] 是否显示【换一换】按钮
  /// [detailFree] 详情页免费集数
  /// [unlockCount] 看广告解锁几集，默认 1 集
  /// [unlockAdMode] 解锁广告模式，0：SDK 解锁，1：自定义解锁
  static Future<bool> showTheaterPage(
      {bool showBackBtn = true,
      bool showPageTitle = true,
      bool showChangeBtn = true,
      int detailFree = 5,
      int unlockCount = 1,
      int unlockAdMode = 0}) async {
    return await _channel.invokeMethod('showTheaterPage', {
      "showBackBtn": showBackBtn,
      "showPageTitle": showPageTitle,
      "showChangeBtn": showChangeBtn,
      "detailFree": detailFree,
      "unlockCount": unlockCount,
      "hideRewardDialog": false,
      "unlockAdMode": unlockAdMode,
    });
  }

  /// 显示短剧滑滑流
  /// [hideInfo] 是否隐藏短剧标题信息
  /// [hideEnter] 是否隐藏[下一集]按钮
  /// [topDramaId] 混排承接的短剧id
  /// [detailFree] 详情页免费集数
  /// [unlockCount] 看广告解锁几集，默认 1 集
  /// [unlockAdMode] 解锁广告模式，0：SDK 解锁，1：自定义解锁
  /// 参考文档：https://www.csjplatform.com/supportcenter/28150
  static Future<bool> showDrawPage(
      {bool hideInfo = false,
      bool hideEnter = false,
      int topDramaId = -1,
      int detailFree = 5,
      int unlockCount = 1,
      int unlockAdMode = 0}) async {
    return await _channel.invokeMethod('showDrawPage', {
      "hideInfo": hideInfo,
      "hideEnter": hideEnter,
      "topDramaId": topDramaId,
      "detailFree": detailFree,
      "unlockCount": unlockCount,
      "hideRewardDialog": false,
      "unlockAdMode": unlockAdMode,
    });
  }

  /// 显示短剧详情页面
  /// [id] 短剧 id
  /// [index] 集数索引，从1开始
  /// [groupId] 组 id，经量传可能影响推荐效果
  /// [detailFree] 详情页免费集数
  /// [unlockCount] 看广告解锁几集，默认 1 集
  /// [hideTopInfo] 是否隐藏顶部信息
  /// [setTopOffset] 设置顶部偏移
  /// [hideBottomInfo] 是否隐藏底部信息
  /// [setBottomOffset] 设置底部偏移
  /// [hideRewardDialog] 是否隐藏奖励弹窗
  /// [hideMore] 是否隐藏更多
  /// [hideCellularToast] 是否隐藏流量提示
  /// [hideBack] 是否隐藏返回按钮
  /// [unlockAdMode] 解锁广告模式，0：SDK 解锁，1：自定义解锁
  /// 参考文档：https://www.csjplatform.com/supportcenter/28151
  static Future<bool> showDetailPage(
      {int id = 0,
      int index = 1,
      String groupId = "0",
      int detailFree = 5,
      int unlockCount = 1,
      bool hideTopInfo = false,
      int setTopOffset = -1,
      bool hideBottomInfo = false,
      int setBottomOffset = -1,
      bool hideRewardDialog = false,
      bool hideLikeButton = false,
      bool hideFavorButton = false,
      bool hideMore = false,
      bool hideCellularToast = false,
      bool hideBack = false,
      int unlockAdMode = 0}) async {
    return await _channel.invokeMethod('showDetailPage', {
      "id": id,
      "index": index,
      "groupId": groupId,
      "detailFree": detailFree,
      "unlockCount": unlockCount,
      "hideTopInfo": hideTopInfo,
      "setTopOffset": setTopOffset,
      "hideBottomInfo": hideBottomInfo,
      "setBottomOffset": setBottomOffset,
      "hideRewardDialog": hideRewardDialog,
      "hideLikeButton": hideLikeButton,
      "hideFavorButton": hideFavorButton,
      "hideMore": hideMore,
      "hideCellularToast": hideCellularToast,
      "hideBack": hideBack,
      "unlockAdMode": unlockAdMode,
    });
  }

  /// 校验短剧参数是否正确
  /// [total] 短剧集数
  /// [freeSet] 免费集数
  /// [lockSet] 解锁集数
  static Future<bool> verifyDramaParams(
      {int total = 0, int freeSet = 0, int lockSet = 0}) async {
    return await _channel.invokeMethod('verifyDramaParams', {
      "total": total,
      "freeSet": freeSet,
      "lockSet": lockSet,
    });
  }

  /// 通过个性化推荐获取所有短剧
  /// [page] 页数，从1开始
  /// [count] 每页个数，不超过20
  static Future<List<Drama>> requestAllDramaByRecommend(
      {int page = 1, int count = 20}) async {
    var result = await _channel.invokeMethod('requestAllDramaByRecommend', {
      "page": page,
      "count": count,
    });
    return parseDramaList(result);
  }

  /// 批量获取所有短剧
  /// [page] 页数，从1开始
  /// [count] 每页个数
  /// [order] 按照短剧更新（上线时间）日期排序请求，0正序，1倒序
  static Future<List<Drama>> requestAllDrama(
      {int page = 1, int count = 20, int order = 0}) async {
    var result = await _channel.invokeMethod('requestAllDrama', {
      "page": page,
      "count": count,
      "order": order,
    });
    return parseDramaList(result);
  }

  /// 根据短剧id获取短剧信息
  /// [dramaIds] 短剧id列表
  static Future<List<Drama>> requestDrama(List<int> dramaIds) async {
    var result = await _channel.invokeMethod('requestDrama', {
      "dramaIds": dramaIds,
    });
    return parseDramaList(result);
  }

  /// 获取短剧解锁状态（用于解锁面板）
  /// [id] 短剧id
  /// [freeSet] 免费集数
  static Future<List<UnlockStatus>> getEpisodesStatus(
      {required int id, int freeSet = 0}) async {
    var result = await _channel.invokeMethod('getEpisodesStatus', {
      "id": id,
      "freeSet": freeSet,
    });
    return parseUnlockStatusList(result);
  }

  /// 按分类请求短剧
  /// [category] 分类，如"霸总"
  /// [page] 页数，从1开始
  /// [count] 每页个数,不超过20
  /// [order] 按照短剧更新（上线时间）日期排序请求，0正序，1倒序
  static Future<List<Drama>> requestDramaByCategory(
      {String category = "霸总",
      int page = 1,
      int count = 20,
      int order = 0}) async {
    var result = await _channel.invokeMethod('requestDramaByCategory', {
      "category": category,
      "page": page,
      "count": count,
      "order": order,
    });
    return parseDramaList(result);
  }

  /// 请求短剧分类列表
  static Future<List<String>> requestDramaCategoryList() async {
    var result = await _channel.invokeMethod('requestDramaCategoryList');
    List<String> list = [];
    if (result is List) {
      for (String item in result) {
        list.add(item);
      }
    }
    return list;
  }

  /// 搜索短剧
  /// [query] 搜索关键词
  /// [isFuzzy] 是否模糊搜索
  /// [page] 页数，从1开始
  /// [count] 每页个数，不超过20
  static Future<List<Drama>> searchDrama(
      {String query = "",
      bool isFuzzy = false,
      int page = 1,
      int count = 20}) async {
    var result = await _channel.invokeMethod('searchDrama', {
      "query": query,
      "isFuzzy": isFuzzy,
      "page": page,
      "count": count,
    });
    return parseDramaList(result);
  }

  /// 获取短剧历史记录
  /// [page] 页数，从1开始，小于1时全量获取
  /// [count] 每页个数，小于1时全量获取
  static Future<List<Drama>> getDramaHistory(
      {int page = 1, int count = 20}) async {
    var result = await _channel.invokeMethod('getDramaHistory', {
      "page": page,
      "count": count,
    });
    return parseDramaList(result);
  }

  /// 清除短剧历史记录
  static Future<bool> clearDramaHistory() async {
    return await _channel.invokeMethod('clearDramaHistory');
  }

  /// 收藏短剧
  /// [id] 短剧id
  /// [state] 更新状态，true 收藏，false 取消收藏
  static Future<bool> favorDrama(int id, bool state) async {
    return await _channel.invokeMethod('favorDrama', {
      "id": id,
      "state": state,
    });
  }

  /// 获取短剧收藏列表
  /// [page] 页数，从1开始，小于1时全量获取
  /// [count] 每页个数，小于1时全量获取
  static Future<List<Drama>> getFavorList(
      {int page = 1, int count = 20}) async {
    var result = await _channel.invokeMethod('getFavorList', {
      "page": page,
      "count": count,
    });
    return parseDramaList(result);
  }

  /// 设置全部短剧倍速，重启app后与上一次设置一致
  /// [speed] 速度 0～3
  static Future<bool> setGlobalSpeedPlay(double speed) async {
    return await _channel.invokeMethod('setGlobalSpeedPlay', {"speed": speed});
  }

  ///===========
  /// 短故事接口
  ///===========
  /// 打开阅读页面
  /// [novelStory] 短故事实体
  /// [rewardAdMode] 激励广告模式，0：SDK 激励，1：自定义激励
  /// [rewardCodeId] 激励广告广告位 ID
  /// [pageTurnMode] 翻页模式，0：平移，SDK 默认，1：仿真 2：覆盖 3：上下
  /// [defaultTextSize] 默认文字大小，默认 12
  /// [endPageCardStyle] 文末客片样式，0：纯文字样式 1：文字+图片样式
  /// [endPageRecSize] 文末推荐页个数，默认 3
  /// [controller] 控制器
  /// [unlockFlowListener] 解锁流程监听
  static Future<bool> openNovelReaderPage(
    NovelStory novelStory, {
    int rewardAdMode = 0,
    String rewardCodeId = "",
    int pageTurnMode = 0,
    int defaultTextSize = 12,
    int endPageCardStyle = 0,
    int endPageRecSize = 3,
    ProVideoController? controller,
    UnlockFlowListener? unlockFlowListener,
  }) async {
    // 设置控制器回调
    controller ??= ProVideoController();
    controller.setChannel(_novelChannel);
    controller.setUnlockFlowListener(unlockFlowListener);
    // 调用方法
    return await _channel.invokeMethod('openNovelReaderPage', {
      "novelStory": novelStory.toJson(),
      "rewardAdMode": rewardAdMode,
      "rewardCodeId": rewardCodeId,
      "pageTurnMode": pageTurnMode,
      "defaultTextSize": defaultTextSize,
      "endPageCardStyle": endPageCardStyle,
      "endPageRecSize": endPageRecSize,
    });
  }

  /// 获取短故事类目列表
  static Future<List<NovelCategory>> getStoryCategoryList() async {
    var result = await _channel.invokeMethod('getStoryCategoryList');
    return parseNovelCategoryList(result);
  }

  /// 搜索短故事
  /// [query] 搜索关键词
  /// [isFuzzy] 是否模糊搜索：true模糊搜索、false精准搜索
  /// [page] 页数，从1开始
  /// [count] 分页大小，建议20
  static Future<List<NovelStory>> searchStory(
      {String query = "",
      bool isFuzzy = false,
      int page = 1,
      int count = 20}) async {
    var result = await _channel.invokeMethod('searchStory', {
      "query": query,
      "isFuzzy": isFuzzy,
      "page": page,
      "count": count,
    });
    return parseNovelStoryList(result);
  }

  /// 根据类目请求短故事
  /// [id] 类目id，需要保证id有效(大于0)
  /// [order] 排序类型：0正序、1倒序
  /// [page] 页数，从1开始
  /// [count] 分页大小，建议20
  static Future<List<NovelStory>> requestStoryByCategory(
      {int id = 0, int order = 0, int page = 1, int count = 20}) async {
    var result = await _channel.invokeMethod('requestStoryByCategory', {
      "id": id,
      "order": order,
      "page": page,
      "count": count,
    });
    return parseNovelStoryList(result);
  }

  /// 根据短故事id请求
  /// [ids] 短故事id，支持多个
  /// [page] 页数，从1开始
  /// [count] 分页大小，建议20
  static Future<List<NovelStory>> requestStoryByIds(
      {List<int> ids = const [], int page = 1, int count = 20}) async {
    var result = await _channel.invokeMethod('requestStoryByIds', {
      "ids": ids,
      "page": page,
      "count": count,
    });
    return parseNovelStoryList(result);
  }

  /// 获取短故事精选列表
  /// [order] 排序类型：0正序、1倒序
  /// [page] 页数，从1开始
  /// [count] 分页大小，建议20
  static Future<List<NovelStory>> requestStoryFeed(
      {int order = 0, int page = 20, int count = 1}) async {
    var result = await _channel.invokeMethod('requestStoryFeed', {
      "order": order,
      "page": page,
      "count": count,
    });
    return parseNovelStoryList(result);
  }

  /// 获取短故事历史记录
  /// [page] 页数，从1开始
  /// [count] 分页大小，建议20
  static Future<List<NovelStory>> getStoryHistory(
      {int page = 1, int count = 20}) async {
    var result = await _channel.invokeMethod('getStoryHistory', {
      "page": page,
      "count": count,
    });
    return parseNovelStoryList(result);
  }

  /// 收藏短故事
  /// [id] 短故事id
  static Future<bool> storyFavorite(int id) async {
    return await _channel.invokeMethod('storyFavorite', {
      "id": id,
    });
  }

  /// 取消收藏短故事
  /// [id] 短故事id
  static Future<bool> storyFavoriteCancel(int id) async {
    return await _channel.invokeMethod('storyFavoriteCancel', {
      "id": id,
    });
  }

  /// 获取短故事收藏列表
  /// [page] 页数，从1开始
  /// [count] 分页大小，建议20
  static Future<List<NovelStory>> getStoryFavorite(
      {int page = 1, int count = 20}) async {
    var result = await _channel.invokeMethod('getStoryFavorite', {
      "page": page,
      "count": count,
    });
    return parseNovelStoryList(result);
  }

  ///===========
  /// 用户相关接口
  ///===========
  /// 获取签名信息[推荐使用服务端签名，不建议使用客户端签名]
  /// [key] 秘钥，必须
  /// [nonce] 16为随机字符串，必须，可以通过 getNonce 获取
  /// [time] 时间戳，单位秒，必须
  /// [ouid] 值为开发者用户id
  static Future<String> getSignString(
      String key, String nonce, int time, String ouid) async {
    return await _channel.invokeMethod('getSignString', {
      "key": key,
      "nonce": nonce,
      "time": time,
      "params": {
        "ouid": ouid,
      }
    });
  }

  /// 获取随机字符串(默认 16 位)
  static String getNonce([int length = 16]) {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    Random random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)])
        .join('');
  }

  /// 登录
  /// [params] 登录参数
  static Future<bool> login(String params) async {
    return await _channel.invokeMethod('login', {
      "params": params,
    });
  }

  /// 是否已登录
  static Future<bool> isLogin() async {
    return await _channel.invokeMethod('isLogin');
  }

  /// 登出
  static Future<bool> logout() async {
    return await _channel.invokeMethod('logout');
  }
}
