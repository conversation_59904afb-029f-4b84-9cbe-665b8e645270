/// 短剧详情实体
class Drama {
  int id;
  int? groupId;
  String title;
  String coverImage;
  int status;
  int total;
  int index;
  String type;
  int? typeId;
  String desc;
  int? createTime;
  int? actionTime;
  int? freeSet;
  int? lockSet;
  String? icpNumber;
  bool? isFavor;
  int? favoriteTime;
  int? favoriteCount;
  int? levelLabel;
  bool? isPotential;
  int? duration;
  List<String> tags = const [];

  Drama({
    required this.id,
    this.groupId,
    required this.title,
    required this.coverImage,
    required this.status,
    required this.total,
    required this.index,
    required this.type,
    this.typeId,
    required this.desc,
    this.createTime,
    this.actionTime,
    this.freeSet,
    this.lockSet,
    this.icpNumber,
    this.isFavor,
    this.favoriteTime,
    this.favoriteCount,
    this.levelLabel,
    this.isPotential,
    this.duration,
    this.tags = const [],
  });

  factory Drama.fromJson(Map<dynamic, dynamic> json) {
    return Drama(
      id: json['id'],
      groupId: json['groupId'] ?? 0,
      title: json['title'] ?? "" ,
      coverImage: json['coverImage'] ?? "",
      status: json['status'] ?? 0,
      total: json['total'] ?? 1,
      index: json['index'] ?? 1,
      type: json['type'] ?? "",
      typeId: json['typeId'] ?? 0,
      desc: json['desc'] ?? '',
      createTime: json['createTime'] ?? 0,
      actionTime: json['actionTime'] ?? 0,
      freeSet: json['freeSet'] ?? 2,
      lockSet: json['lockSet'] ?? 1,
      icpNumber: json['icpNumber'] ?? "",
      isFavor: json['isFavor'] ?? false,
      favoriteTime: json['favoriteTime'] ?? 0,
      favoriteCount: json['favoriteCount'] ?? 0,
      levelLabel: json['levelLabel'] ?? 0,
      isPotential: json['isPotential'] ?? false,
      duration: json['duration'] ?? 0,
      // tags: json["tags"] ?? []
    );
  }
}

/// 解锁详情
class UnlockStatus {
  int index;
  bool isLocked;

  UnlockStatus({
    required this.index,
    required this.isLocked,
  });

  factory UnlockStatus.fromJson(Map<dynamic, dynamic> json) {
    return UnlockStatus(
      index: json['index'],
      isLocked: json['isLocked'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['index'] = index;
    data['isLocked'] = isLocked;
    return data;
  }
}

/// 解析短剧列表
/// [result] 短剧列表
Future<List<Drama>> parseDramaList(dynamic result) async {
  List<Drama> list = [];
  if (result is List) {
    for (var item in result) {
      list.add(Drama.fromJson(item));
    }
  }
  return list;
}

/// 解析解锁详情
/// [result] 解锁列表
List<UnlockStatus> parseUnlockStatusList(dynamic result) {
  List<UnlockStatus> list = [];
  if (result is List) {
    for (var item in result) {
      list.add(UnlockStatus.fromJson(item));
    }
  }
  return list;
}

/// 错误信息解析
class ErrMsg {
  int? errCode;
  String? errMsg;

  ErrMsg({
    this.errCode,
    this.errMsg,
  });

  factory ErrMsg.fromJson(Map<dynamic, dynamic> json) {
    return ErrMsg(
      errCode: json['errCode'],
      errMsg: json['errMsg'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['errCode'] = errCode;
    data['errMsg'] = errMsg;
    return data;
  }
}
