/// 短故事实体类
class NovelStory {
  // 短故事id
  final int id;
  // 故事标题
  String title;
  // 简介
  String desc;
  // 第一章小说前300字
  String? content;
  // 作者
  String author;
  // 封面图片
  String coverImage;
  // 封面图类型：0封面图、1默认封面图、2无图
  int imageType;
  // 分类id
  int categoryId;
  // 分类名称
  String categoryName;
  // 总共章数
  int total;
  // 创建时间
  int createTime;
  // 当前阅读章节
  int index;
  // 本章阅读进度
  double progress;
  // 用户阅读次数，以用户点击为准
  int statsCount;
  // 是否收藏
  bool isFavorite;
  // 收藏时间
  String favoriteTime;
  // 操作时间
  int actionTime;

  NovelStory({
    required this.id,
    required this.title,
    required this.desc,
    this.content,
    required this.author,
    required this.coverImage,
    required this.imageType,
    required this.categoryId,
    required this.categoryName,
    required this.total,
    required this.createTime,
    required this.index,
    required this.progress,
    required this.statsCount,
    required this.isFavorite,
    required this.favoriteTime,
    required this.actionTime,
  });

  factory NovelStory.fromJson(Map<dynamic, dynamic> json) {
    return NovelStory(
      id: json['id'],
      title: json['title'],
      desc: json['desc'],
      content: json['content'],
      author: json['author'],
      coverImage: json['coverImage'],
      imageType: json['imageType'],
      categoryId: json['categoryId'],
      categoryName: json['categoryName'],
      total: json['total'],
      createTime: json['createTime'],
      index: json['index'],
      progress: json['progress'].toDouble(),
      statsCount: json['statsCount'],
      isFavorite: json['isFavorite'],
      favoriteTime: json['favoriteTime'],
      actionTime: json['actionTime'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['desc'] = desc;
    data['content'] = content;
    data['author'] = author;
    data['coverImage'] = coverImage;
    data['imageType'] = imageType;
    data['categoryId'] = categoryId;
    data['categoryName'] = categoryName;
    data['total'] = total;
    data['createTime'] = createTime;
    data['index'] = index;
    data['progress'] = progress;
    data['statsCount'] = statsCount;
    data['isFavorite'] = isFavorite;
    data['favoriteTime'] = favoriteTime;
    data['actionTime'] = actionTime;
    return data;
  }
}

/// 解析短故事列表
/// [result] 短故事列表
Future<List<NovelStory>> parseNovelStoryList(dynamic result) async {
  List<NovelStory> list = [];
  if (result is List) {
    for (var item in result) {
      list.add(NovelStory.fromJson(item));
    }
  }
  return list;
}

/// 短故事类目
class NovelCategory {
  // 类目id
  int id;
  // 类目名称
  String name;
  // 类目等级：一级类目/二级类目
  int level;
  // 子类目
  List<NovelCategory> children;

  NovelCategory({
    required this.id,
    required this.name,
    required this.level,
    required this.children,
  });

  factory NovelCategory.fromJson(Map<dynamic, dynamic> json) {
    return NovelCategory(
      id: json['id'],
      name: json['name'],
      level: json['level'],
      children: json['children'] != null
          ? List<NovelCategory>.from(
              json['children'].map((item) => NovelCategory.fromJson(item)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['level'] = level;
    data['children'] = children.map((item) => item.toJson()).toList();
    return data;
  }
}

/// 解析短故事类目列表
/// [result] 短故事类目列表
Future<List<NovelCategory>> parseNovelCategoryList(dynamic result) async {
  List<NovelCategory> list = [];
  if (result is List) {
    for (var item in result) {
      list.add(NovelCategory.fromJson(item));
    }
  }
  return list;
}
