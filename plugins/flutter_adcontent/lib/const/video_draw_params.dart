class VideoDrawParams {
  // 进度条样式：浅色
  static const int progressBarStyleLight = 1;
  // 进度条样式：深色
  static const int progressBarStyleDark = 2;
  // 默认举报弹窗距离顶部的距离
  static const double defaultReportTopPadding = 64.0;
  // 刷新
  static const int refresh = 1;
  // 追加
  static const int append = 2;
  // 视频
  static const int drawContentTypeOnlyVideo = 1;
  // 直播
  static const int drawContentTypeOnlyLive = 2;
  // 视频+直播
  static const int drawContentTypeVideoLive = 3;
  // 推荐
  static const int drawChannelTypeRecommend = 1;
  // 关注
  static const int drawChannelTypeFollow = 2;
  // 小视频：推荐+关注
  static const int drawChannelTypeRecommendFollow = 3;
  // 剧场
  static const int drawChannelTypeTheater = 2;
  // 短剧：推荐+剧场
  static const int drawChannelTypeRecommendTheater = 3;
  // 答题模式开启
  static const int quizModeOn = 1;
  // 全屏沉浸式
  static const int drawStyleFullscreen = 2;
  // 宫格式
  static const int drawStyleGrid = 3;
  // 双列式 Feed 流
  static const int drawStyleDoubleFeed = 4;

  // 用户主页
  static const int pageTypeUserHomePage = 0;
  // 视频列表 USER_FAVORITE_VIDEO_PAGE
  static const int pageTypeUserFavoriteVideoPage = 1;
  // 关注列表 USER_FOCUS_PAGE
  static const int pageTypeUserFocusPage = 2;
}
