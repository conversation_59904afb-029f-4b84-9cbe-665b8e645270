import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../ctrls/video_controller.dart';

/// 短剧组件
class DramaWidget extends StatefulWidget {
  // 构造方法
  /// [controller] 视频控制器
  /// [unlockFlowListener] 解锁流程监听
  /// [videoPlayListener] 视频播放监听器
  /// [width] View 的宽度
  /// [height] View 的高度
  /// [id] 短剧 ID
  /// [index] 集数
  /// [groupId] 分组 ID
  /// [detailFree] 免费集数
  /// [unlockCount] 解锁集数
  /// [unlockAdMode] 解锁广告模式，0：SDK 解锁，1：自定义解锁
  /// [hideTopInfo] 是否隐藏顶部信息
  /// [setTopOffset] 顶部信息偏移
  /// [hideBottomInfo] 是否隐藏底部信息
  /// [setBottomOffset] 底部信息偏移
  /// [hideLikeButton] 是否隐藏点赞按钮
  /// [hideFavorButton] 是否隐藏收藏按钮
  /// [hideMore] 是否隐藏更多
  /// [hideCellularToast] 是否隐藏流量提示
  DramaWidget({
    Key? key,
    this.controller,
    this.unlockFlowListener,
    this.videoPlayListener,
    this.theaterListener,
    this.selfDetailListener,
    this.width = 375,
    this.height = 667,
    this.id = 0,
    this.index = 1,
    this.groupId = "0",
    this.detailFree = 5,
    this.unlockCount = 1,
    this.unlockAdMode = 0,
    this.hideTopInfo = false,
    this.setTopOffset = -1,
    this.hideBottomInfo = false,
    this.setBottomOffset = 38,
    this.hideLikeButton = false,
    this.hideFavorButton = false,
    this.hideMore = true,
    this.hideCellularToast = false,
    this.strCoverUrl = "",
    this.strDesc = "",
    this.tags = const [],
    this.total = 0,
    this.favStatusListener,
    this.bCollectDrama = false,
    this.hideLongClickSpeed = false,
  }) : super(key: key);
  // View 的宽度
  final double width;
  // View 的高度
  final double height;
  // 短剧 ID
  final int id;
  // 集数
  final int index;
  // 分组 ID
  final String groupId;
  // 免费集数
  final int detailFree;
  // 解锁集数
  final int unlockCount;
  // 解锁模式
  final int unlockAdMode;
  // 是否隐藏顶部信息
  final bool hideTopInfo;
  // 顶部信息偏移
  final int setTopOffset;
  // 是否隐藏底部信息
  final bool hideBottomInfo;
  // 底部信息偏移
  final int setBottomOffset;
  // 是否隐藏点赞按钮
  final bool hideLikeButton;
  // 是否隐藏收藏按钮
  final bool hideFavorButton;
  // 是否隐藏更多
  final bool hideMore;
  // 是否隐藏流量提示
  final bool hideCellularToast;
  // 是否隐藏长按倍速
  final bool hideLongClickSpeed;
  // 视频控制器
  final ProVideoController? controller;
  // 解锁流程监听
  final UnlockFlowListener? unlockFlowListener;
  // 视频播放监听器
  final VideoPlayListener? videoPlayListener;
  ///
  TheaterListener? theaterListener;

  SelfDetailListener? selfDetailListener;

  FavDramaListener? favStatusListener;

  final String? strCoverUrl;
  final String? strDesc;
  final List<String>? tags;
  final int? total;
  bool bCollectDrama = false;

  @override
  _DramaWidgetState createState() => _DramaWidgetState();
}

class _DramaWidgetState extends State<DramaWidget>
    with AutomaticKeepAliveClientMixin {
  // View 类型
  final String viewType = 'flutter_adcontent_view_drama';
  // 创建参数
  late Map<String, dynamic> creationParams;
  // 控制器
  late ProVideoController controller;

  @override
  void initState() {
    creationParams = <String, dynamic>{
      "width": widget.width,
      "height": widget.height,
      "id": widget.id,
      "index": widget.index,
      "groupId": widget.groupId,
      "detailFree": widget.detailFree,
      "unlockCount": widget.unlockCount,
      "hideTopInfo": widget.hideTopInfo,
      "setTopOffset": widget.setTopOffset,
      "hideBottomInfo": widget.hideBottomInfo,
      "setBottomOffset": widget.setBottomOffset,
      "hideLikeButton": widget.hideLikeButton,
      "hideFavorButton": widget.hideFavorButton,
      "hideRewardDialog": true,
      "hideMore": widget.hideMore,
      "hideCellularToast": widget.hideCellularToast,
      "hideLongClickSpeed": widget.hideLongClickSpeed,
      "hideBack": true,
      "unlockAdMode": widget.unlockAdMode,
      "coverImage": widget.strCoverUrl,
      "desc": widget.strDesc,
      "tags": widget.tags,
      "total": widget.total,
      "bFav" : widget.bCollectDrama
    };
    controller = widget.controller ?? ProVideoController();
    controller.setUnlockFlowListener(widget.unlockFlowListener);
    controller.setVideoPlayListener(widget.videoPlayListener);
    controller.setTheaterListener(widget.theaterListener);
    controller.setSelfDetailListen(widget.selfDetailListener);
    controller.setFavDramaListen(widget.favStatusListener);

    super.initState();
  }

  /// 销毁方法
  @override
  void dispose() {
    // controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (Platform.isIOS) {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: UiKitView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    } else {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: AndroidView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    }
  }

  @override
  bool get wantKeepAlive => true;

  /// 初始化方法通道
  void initMethodChannel(int id) {
    MethodChannel _channel = MethodChannel('$viewType/$id');
    controller.setChannel(_channel);
  }
}
