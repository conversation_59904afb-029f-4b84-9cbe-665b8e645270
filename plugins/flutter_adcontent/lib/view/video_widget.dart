import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

import '../flutter_adcontent.dart';

/// 小视频组件
class VideoWidget extends StatefulWidget {
  /// 构造函数
  /// [width] View 的宽度
  /// [height] View 的高度
  /// [titleTopMargin] 标题距顶部的距离
  /// [bottomOffset] 内容距底部的距离
  /// [controller] 视频控制器
  /// [channelType] 标题频道类型
  /// [contentType] 展示内容类别
  /// [style] 展示样式（全屏、宫格、双 Feed 流）
  /// [hideChannelName] 隐藏频道名称
  /// [hideFollow] 隐藏关注按钮
  /// [showGuide] 显示引导
  /// [enableRefresh] 开启刷新
  /// [autoPlay] 自动播放
  const VideoWidget({
    Key? key,
    this.controller,
    this.videoPlayListener,
    this.width = 375,
    this.height = 812,
    this.titleTopMargin = 32,
    this.bottomOffset = 0,
    this.channelType = VideoDrawParams.drawChannelTypeRecommendFollow,
    this.contentType = VideoDrawParams.drawContentTypeVideoLive,
    this.style = VideoDrawParams.drawStyleFullscreen,
    this.hideChannelName = false,
    this.hideFollow = false,
    this.showGuide = true,
    this.enableRefresh = true,
    this.autoPlay = false,
  }) : super(key: key);
  // 视频控制器
  final ProVideoController? controller;
  // 视频播放监听器
  final VideoPlayListener? videoPlayListener;
  // View 的宽度
  final double width;
  // View 的高度
  final double height;
  // 标题频道类型
  final int channelType;
  // 展示内容类别
  final int contentType;
  // 展示样式（全屏、宫格、双 Feed 流）
  final int style;
  // 标题距顶部的距离
  final int titleTopMargin;
  // 内容距底部的距离
  final int bottomOffset;
  // 隐藏频道名称
  final bool hideChannelName;
  // 隐藏关注按钮
  final bool hideFollow;
  // 显示引导
  final bool showGuide;
  // 开启刷新
  final bool enableRefresh;
  // 自动播放
  final bool autoPlay;

  @override
  _VideoWidgetState createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget>
    with AutomaticKeepAliveClientMixin {
  // View 类型
  final String viewType = 'flutter_adcontent_view_video';
  // 创建参数
  late Map<String, dynamic> creationParams;
  // 控制器
  late ProVideoController controller;

  @override
  void initState() {
    creationParams = <String, dynamic>{
      "width": widget.width,
      "height": widget.height,
      "channelType": widget.channelType,
      "contentType": widget.contentType,
      "style": widget.style,
      "titleTopMargin": widget.titleTopMargin,
      "bottomOffset": widget.bottomOffset,
      "hideChannelName": widget.hideChannelName,
      "hideClose": true,
      "hideFollow": widget.hideFollow,
      "showGuide": widget.showGuide,
      "enableRefresh": widget.enableRefresh,
      "autoPlay": widget.autoPlay,
    };
    controller = widget.controller ?? ProVideoController();
    controller.setVideoPlayListener(widget.videoPlayListener);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (Platform.isIOS) {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: UiKitView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    } else {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: AndroidView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    }
  }

  @override
  bool get wantKeepAlive => true;

  /// 初始化方法通道
  void initMethodChannel(int id) {
    MethodChannel _channel = MethodChannel('$viewType/$id');
    controller.setChannel(_channel);
  }
}
