import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../ctrls/video_controller.dart';

/// 短故事首页聚合组件
class NovelStoryWidget extends StatefulWidget {
  // 构造方法
  /// [controller] 视频控制器
  /// [novelStoryHomeListener] 短故事首页监听器
  /// [width] View 的宽度
  /// [height] View 的高度
  /// [setTopOffset] 顶部信息偏移
  /// [recPageSize] 推荐榜请求分页大小，默认9
  /// [canPullRefresh] 是否支持下拉刷新
  /// [rewardAdMode] 激励广告模式，0：SDK 激励，1：自定义激励
  /// [pageTurnMode] 翻页模式，0：平移，SDK 默认，1：仿真 2：覆盖 3：上下
  /// [defaultTextSize] 默认文字大小，默认 12
  /// [endPageCardStyle] 文末客片样式，0：纯文字样式 1：文字+图片样式
  /// [endPageRecSize] 文末推荐页个数，默认 3
  const NovelStoryWidget({
    Key? key,
    this.controller,
    this.novelStoryListener,
    this.novelStoryHomeListener,
    this.unlockFlowListener,
    this.width = 375,
    this.height = 667,
    this.setTopOffset = -1,
    this.recPageSize = 9,
    this.canPullRefresh = false,
    this.rewardAdMode = 0,
    this.rewardCodeId = "",
    this.pageTurnMode = 0,
    this.defaultTextSize = 12,
    this.endPageCardStyle = 0,
    this.endPageRecSize = 3,
  }) : super(key: key);
  // 视频控制器
  final ProVideoController? controller;
  // 短故事监听器
  final NovelStoryListener? novelStoryListener;
  // 短故事聚合页监听器
  final NovelStoryHomeListener? novelStoryHomeListener;
  // 解锁流程监听
  final UnlockFlowListener? unlockFlowListener;
  // View 的宽度
  final double width;
  // View 的高度
  final double height;
  // 顶部信息偏移
  final int setTopOffset;
  // 是否支持下拉刷新
  final bool canPullRefresh;
  // 推荐榜请求分页大小，默认9
  final int recPageSize;
  // 激励广告模式
  final int rewardAdMode;
  // 激励视频广告位 id
  final String rewardCodeId;
  // 翻页模式
  final int pageTurnMode;
  // 默认文字大小
  final int defaultTextSize;
  // 文末客片样式
  final int endPageCardStyle;
  // 文末推荐页个数
  final int endPageRecSize;

  @override
  _NovelStoryWidgetState createState() => _NovelStoryWidgetState();
}

class _NovelStoryWidgetState extends State<NovelStoryWidget>
    with AutomaticKeepAliveClientMixin {
  // View 类型
  final String viewType = 'flutter_adcontent_view_novel_story';
  // 创建参数
  late Map<String, dynamic> creationParams;
  // 控制器
  late ProVideoController controller;

  @override
  void initState() {
    creationParams = <String, dynamic>{
      "width": widget.width,
      "height": widget.height,
      "setTopOffset": widget.setTopOffset,
      "canPullRefresh": widget.canPullRefresh,
      "recPageSize": widget.recPageSize,
      "rewardAdMode": widget.rewardAdMode,
      "rewardCodeId": widget.rewardCodeId,
      "pageTurnMode": widget.pageTurnMode,
      "defaultTextSize": widget.defaultTextSize,
      "endPageCardStyle": widget.endPageCardStyle,
      "endPageRecSize": widget.endPageRecSize,
    };
    controller = widget.controller ?? ProVideoController();
    controller.setNovelStoryListener(widget.novelStoryListener);
    controller.setNovelStoryHomeListener(widget.novelStoryHomeListener);
    controller.setUnlockFlowListener(widget.unlockFlowListener);
    super.initState();
  }

  /// 销毁方法
  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (Platform.isIOS) {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: UiKitView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    } else {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: AndroidView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    }
  }

  @override
  bool get wantKeepAlive => true;

  /// 初始化方法通道
  void initMethodChannel(int id) {
    MethodChannel _channel = MethodChannel('$viewType/$id');
    controller.setChannel(_channel);
  }
}
