import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../const/video_draw_params.dart';
import '../ctrls/video_controller.dart';

/// 剧场组件
class TheaterWidget extends StatefulWidget {
  // 构造方法
  /// [controller] 视频控制器
  /// [channelType] 顶部频道类型[VideoDrawParams]
  /// [theaterListener] 短剧剧场监听器
  /// [videoPlayListener] 视频播放监听器
  /// [width] View 的宽度
  /// [height] View 的高度
  /// [detailFree] 免费集数
  /// [unlockCount] 解锁集数
  /// [unlockAdMode] 解锁广告模式，0：SDK 解锁，1：自定义解锁
  /// [hideTopInfo] 是否隐藏顶部信息
  /// [setTopOffset] 顶部信息偏移
  /// [hideBottomInfo] 是否隐藏底部信息[滑滑流]
  /// [setBottomOffset] 底部信息偏移[滑滑流]
  /// [hideLikeButton] 是否隐藏点赞按钮[滑滑流]
  /// [hideFavorButton] 是否隐藏收藏按钮[滑滑流]
  /// [hideEnter] 是否隐藏进入（下一集）按钮[滑滑流]
  /// [showChangeBtn] 是否显示换一换按钮[剧场]
  const TheaterWidget({
    Key? key,
    this.controller,
    this.theaterListener,
    this.selfDetailListener,
    this.videoPlayListener,
    this.channelType = VideoDrawParams.drawChannelTypeRecommend,
    this.width = 375,
    this.height = 667,
    this.detailFree = 5,
    this.unlockCount = 1,
    this.unlockAdMode = 0,
    this.hideTopInfo = false,
    this.setTopOffset = -1,
    this.hideBottomInfo = false,
    this.setBottomOffset = -1,
    this.hideEnter = false,
    this.hideLikeButton = false,
    this.hideFavorButton = false,
    this.showChangeBtn = true,
    this.bHideLongPressSpeed = true,
  }) : super(key: key);
  // 视频控制器
  final ProVideoController? controller;
  // 短剧剧场监听器
  final TheaterListener? theaterListener;
  final SelfDetailListener? selfDetailListener;
    // 视频播放监听器
  final VideoPlayListener? videoPlayListener;
  // 标题频道类型
  final int channelType;
  // View 的宽度
  final double width;
  // View 的高度
  final double height;
  // 免费集数
  final int detailFree;
  // 解锁集数
  final int unlockCount;
  // 解锁模式
  final int unlockAdMode;
  // 是否隐藏顶部信息
  final bool hideTopInfo;
  // 顶部信息偏移
  final int setTopOffset;
  // 是否隐藏底部信息
  final bool hideBottomInfo;
  // 底部信息偏移
  final int setBottomOffset;
  // 是否隐藏进入（下一集）按钮
  final bool hideEnter;
  // 是否隐藏点赞按钮
  final bool hideLikeButton;
  // 是否隐藏收藏按钮
  final bool hideFavorButton;
  // 是否显示换一换按钮（剧场）
  final bool showChangeBtn;
  ///
  final bool bHideLongPressSpeed;

  @override
  _TheaterWidgetState createState() => _TheaterWidgetState();
}

class _TheaterWidgetState extends State<TheaterWidget>
    with AutomaticKeepAliveClientMixin {
  // View 类型
  final String viewType = 'flutter_adcontent_view_theater';
  // 创建参数
  late Map<String, dynamic> creationParams;
  // 控制器
  late ProVideoController controller;

  @override
  void initState() {
    creationParams = <String, dynamic>{
      "width": widget.width,
      "height": widget.height,
      "channelType": widget.channelType,
      "detailFree": widget.detailFree,
      "unlockCount": widget.unlockCount,
      "unlockAdMode": widget.unlockAdMode,
      "hideRewardDialog": true,
      "hideBack": true,
      "hideTopInfo": widget.hideTopInfo,
      "setTopOffset": widget.setTopOffset,
      "hideBottomInfo": widget.hideBottomInfo,
      "setBottomOffset": widget.setBottomOffset,
      "hideEnter": widget.hideEnter,
      "hideLikeButton": widget.hideLikeButton,
      "hideFavorButton": widget.hideFavorButton,
      "showChangeBtn": widget.showChangeBtn,
      "hideLongClickSpeed" : widget.bHideLongPressSpeed
    };
    controller = widget.controller ?? ProVideoController();
    controller.setTheaterListener(widget.theaterListener);
    controller.setSelfDetailListen(widget.selfDetailListener);
    controller.setVideoPlayListener(widget.videoPlayListener);
    super.initState();
  }

  /// 销毁方法
  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (Platform.isIOS) {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: UiKitView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    } else {
      return SizedBox.fromSize(
        size: Size(widget.width.toDouble(), widget.height.toDouble()),
        child: AndroidView(
          viewType: viewType,
          creationParams: creationParams,
          creationParamsCodec: const StandardMessageCodec(),
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
          },
          onPlatformViewCreated: (id) {
            initMethodChannel(id);
          },
        ),
      );
    }
  }

  @override
  bool get wantKeepAlive => true;

  /// 初始化方法通道
  void initMethodChannel(int id) {
    MethodChannel _channel = MethodChannel('$viewType/$id');
    controller.setChannel(_channel);
  }
}
