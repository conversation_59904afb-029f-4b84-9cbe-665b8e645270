import 'dart:io';

import 'package:flutter/services.dart';

import '../entity/drama.dart';
import '../entity/novel_story.dart';

/// 视频控制器
class ProVideoController {
  // 视频组件通道方法
  MethodChannel? _proVideoChannel;
  // 解锁流程监听器
  UnlockFlowListener? _unlockFlowListener;
  // 视频播放监听器
  VideoPlayListener? _videoPlayListener;
  // 短剧剧场监听器
  TheaterListener? _theaterListener;
  // 短故事聚合页监听器
  NovelStoryHomeListener? _novelStoryHomeListener;

  ///自定义播放详情页监听
  SelfDetailListener? _selfDetailListener;

  FavDramaListener? _dramaFavListen;

    // 短故事监听器
  NovelStoryListener? _novelStoryListener;

  /// 设置通道方法
  /// [channel] 通道方法[MethodChannel]
  void setChannel(MethodChannel channel) {
    _proVideoChannel = channel;
    _proVideoChannel?.setMethodCallHandler(onMethodCallHandler);
  }

  /// 设置解锁监听
  /// [listener] 解锁流程监听[UnlockFlowListener]
  void setUnlockFlowListener(UnlockFlowListener? listener) {
    _unlockFlowListener = listener;
  }

  /// 设置视频播放监听
  /// [listener] 视频播放监听[VideoPlayListener]
  void setVideoPlayListener(VideoPlayListener? listener) {
    _videoPlayListener = listener;
  }

  /// 设置短剧剧场监听
  /// [listener] 短剧剧场监听[TheaterListener]
  void setTheaterListener(TheaterListener? listener) {
    _theaterListener = listener;
  }

  ///自定义详情页面的监听
  void setSelfDetailListen(SelfDetailListener? listener) {
    _selfDetailListener = listener;
  }

  ///自定义详情页面的监听
  void setFavDramaListen(FavDramaListener? listener) {
    _dramaFavListen = listener;
  }

  /// 设置短故事聚合页监听
  /// [listener] 短故事聚合页监听[NovelStoryHomeListener]
  void setNovelStoryHomeListener(NovelStoryHomeListener? listener) {
    _novelStoryHomeListener = listener;
  }

  /// 设置短故事监听
  /// [listener] 短故事监听[NovelStoryListener]
  void setNovelStoryListener(NovelStoryListener? listener) {
    _novelStoryListener = listener;
  }

  /// 方法调用处理
  Future<void> onMethodCallHandler(MethodCall call) async {
    String method = call.method;
    print("ProVideoController onMethodCallHandler method:" + method);
    if (method == 'unlockFlowStart') {
      // 开始解锁

      _unlockFlowListener?.unlockFlowStart(call.arguments);
    } else if (method == 'unlockFlowEnd') {
      // 结束解锁
      ErrMsg err = ErrMsg.fromJson(call.arguments);
      _unlockFlowListener?.unlockFlowEnd(err.errCode, err.errMsg);
    } else if (method == 'showCustomAd') {
      // 结束解锁
      _unlockFlowListener?.showCustomAd?.call();
    } else if (method == 'onVideoClose') {
      // 视频关闭
      _videoPlayListener?.onVideoClose?.call();
    } else if (method == 'onSeekTo') {
      // 拖动进度
      num duration = call.arguments;
      _videoPlayListener?.onSeekTo?.call(duration);
    } else if (method == 'onDurationChange') {
      // 进度变化
      num duration = call.arguments;
      _videoPlayListener?.onDurationChange?.call(duration);
    } else if (method == 'onError') {
      // 解析错误
      ErrMsg err = ErrMsg.fromJson(call.arguments);
      _videoPlayListener?.onError?.call(err.errCode, err.errMsg);
    } else if (method == 'onNovelHomeItemClick') {
      // 短故事点击
      NovelStory novelStory = NovelStory.fromJson(call.arguments);
      _novelStoryHomeListener?.onItemClick?.call(novelStory);
    } else if (method == 'onOpenDetail') {
      // 打开详情
      try {
        Map<dynamic, dynamic> mapParam = call.arguments ?? {};
        Drama drama = Drama.fromJson(mapParam);
        _theaterListener?.onOpenDetail?.call(drama);
      } catch (e) {
        int nDebug = 0;
      }
    } else if (method == "onOpenSelfDetail") {
      try {
        Map<dynamic, dynamic> mapParam = call.arguments ?? {};
        Drama drama = Drama.fromJson(mapParam);
        _selfDetailListener?.onOpenSelfDetail?.call(drama);
      } catch (e) {
        int nDebug = 0; // TODO
      }
    } else if (method == "dramaFav") {
      Map<dynamic, dynamic> mapParam = call.arguments ?? {};
      late bool bFav;
      late int nViewId;

      if (Platform.isIOS) {
        bFav = (mapParam["isFav"] ?? "0") == "1";
        nViewId = 0;
      } else {
        bFav = mapParam["isFav"] ?? false;
        nViewId = mapParam["viewId"] ?? 0;
      }

      try {
        _dramaFavListen?.favSet?.call(bFav, nViewId);
      } catch (e) {
        int nDebug = 0;
      }
    } else {
      if (method.contains('onDJX')) {
        // 解析视频
        Drama drama = Drama.fromJson(call.arguments);
        if (method == 'onDJXPageChange') {
          // 视频切换
          _videoPlayListener?.onDJXPageChange?.call(drama);
        } else if (method == 'onDJXVideoPlay') {
          // 视频播放开始
          _videoPlayListener?.onDJXVideoPlay?.call(drama);
        } else if (method == 'onDJXVideoPause') {
          // 视频暂停
          _videoPlayListener?.onDJXVideoPause?.call(drama);
        } else if (method == 'onDJXVideoContinue') {
          // 视频继续
          _videoPlayListener?.onDJXVideoContinue?.call(drama);
        } else if (method == 'onDJXVideoCompletion') {
          // 视频播放完成
          _videoPlayListener?.onDJXVideoCompletion?.call(drama);
        } else if (method == 'onDJXVideoOver') {
          // 视频结束
          _videoPlayListener?.onDJXVideoOver?.call(drama);
        }
      } else if (method.contains('Nov')) {
        // 短故事
        NovelStory novelStory = NovelStory.fromJson(call.arguments);
        if (method == 'NovOnEnter') {
          // 进入短故事
          _novelStoryListener?.onEnter?.call(novelStory);
        } else if (method == 'NovOnExit') {
          // 退出
          _novelStoryListener?.onExit?.call(novelStory);
        } else if (method == 'NovOnPageSelected') {
          // 页面切换
          _novelStoryListener?.onPageSelected?.call(novelStory);
        } else if (method == 'NovOnBookEnd') {
          // 书籍结束
          _novelStoryListener?.onBookEnd?.call(novelStory);
        }
      } else {
        // 小视频
        if (method == 'onDPPageChange') {
          // 视频切换
          _videoPlayListener?.onDPPageChange?.call(call.arguments);
        } else if (method == 'onDPVideoPlay') {
          // 视频播放开始
          _videoPlayListener?.onDPVideoPlay?.call();
        } else if (method == 'onDPVideoPause') {
          // 视频暂停
          _videoPlayListener?.onDPVideoPause?.call();
        } else if (method == 'onDPVideoContinue') {
          // 视频继续
          _videoPlayListener?.onDPVideoContinue?.call();
        } else if (method == 'onDPVideoCompletion') {
          // 视频播放完成
          _videoPlayListener?.onDPVideoCompletion?.call();
        } else if (method == 'onDPVideoOver') {
          // 视频结束
          _videoPlayListener?.onDPVideoOver?.call();
        }
      }
    }
  }

  /// 暂停
  Future<void> pause() async {
    _proVideoChannel?.invokeMethod('pause');
  }

  /// 暂停
  Future<void> iOSUIApplicationWillResignActiveNotification() async {
    if (Platform.isIOS) {
      _proVideoChannel?.invokeMethod('UIApplicationWillResignActiveNotification');
    }
  }

  /// 暂停
  Future<void> iOSUIApplicationDidBecomeActiveNotification() async {
    if (Platform.isIOS) {
      _proVideoChannel?.invokeMethod('UIApplicationDidBecomeActiveNotification');
    }
  }

  /// 继续
  Future<void> resume() async {
    _proVideoChannel?.invokeMethod('resume');
  }

  /// 销毁
  Future<void> dispose() async {
    _proVideoChannel?.invokeMethod('dispose');
  }

  ///设置收藏的结果
  Future<void> setDramaFavStatus(bool bFav, int nViewId) async {
    _proVideoChannel?.invokeMethod("dramaFavNative", {"isFav": bFav, "viewId": nViewId});
  }
    /// 设置倍速
  /// [speed] 速度
  /// [scope] 0：当前集生效 1：当前剧生效
  Future<void> setSpeedPlay({double speed = 1.0, int scope = 0}) async {
    await _proVideoChannel
        ?.invokeMethod('setSpeedPlay', {"speed": speed, "scope": scope});
  }

  /// 解锁剧集【短剧】
  /// [id] 短剧ID
  /// [lockSet] 锁集数
  /// [cancel] 是否取消
  Future<void> unLock(int id, int lockSet, bool cancel) async {
    _proVideoChannel?.invokeMethod("unlock", {
      "id": id,
      "lockSet": lockSet,
      "cancel": cancel,
    });
  }

  /// 自定义广告解锁剧集，设置广告价格回调（1）【短剧】
  /// ** 仅在自定义广告解锁时有效**
  /// [cpm] 广告价格
  Future<void> setCustomAdOnShow(String cpm) async {
    _proVideoChannel?.invokeMethod("setCustomAdOnShow", cpm);
  }

  /// 自定义广告解锁剧集，设置激励结果（2）【短剧】
  /// ** 仅在自定义广告解锁时有效**
  /// [verify] 激励结果, true:成功，false:失败
  /// [extraData] 额外数据
  Future<void> setCustomAdOnReward(bool verify, {Map<String, dynamic>? extraData}) async {
    _proVideoChannel?.invokeMethod("setCustomAdOnReward", {
      "verify": verify,
      "extraData": extraData,
    });
  }

  /// 设置当前播放集数【短剧】
  /// [index] 集数
  Future<void> setCurrentIndex(int index) async {
    _proVideoChannel?.invokeMethod("setCurrentIndex", index);
  }

  /// 打开选集面板【短剧】
  Future<void> openDramaGallery() async {
    _proVideoChannel?.invokeMethod("openDramaGallery");
  }

  /// 打开更多弹窗【短剧】
  Future<void> openMoreDialog() async {
    _proVideoChannel?.invokeMethod("openMoreDialog");
  }
}

// 开始解锁
typedef UnlockFlowStartCallback = void Function(dynamic data);
// 结束解锁
typedef UnlockFlowEndCallback = void Function(int? errCode, String? errMsg);

// 显示自定义广告
typedef ShowCustomAd = void Function();

/// 解锁流程监听
class UnlockFlowListener {
  final UnlockFlowStartCallback unlockFlowStart;
  final UnlockFlowEndCallback unlockFlowEnd;
  final ShowCustomAd? showCustomAd;

  UnlockFlowListener({
    required this.unlockFlowStart,
    required this.unlockFlowEnd,
    this.showCustomAd,
  });
}

/// 视频播放监听
typedef VideoProPlayCallback = void Function(Drama drama);

///点击收藏的回调
typedef FavStatusCallback = void Function(bool bFav, int nViewId);

/// 视频时长监听
typedef VideoProDurationCallback = void Function(num duration);

/// 视频切换监听
typedef VideoProPageCallback = void Function(num index);

/// 视频切换监听
typedef VideoProErrorCallback = void Function(int? errCode, String? errMsg);

/// 短故事监听
typedef NovelStoryCallback = void Function(NovelStory novelStory);

///收藏监听
typedef DramaFavCallback = void Function(bool bFav, int sourceId);

class VideoPlayListener {
  // 视频切换
  final VideoProPlayCallback? onDJXPageChange;
  final VideoProPageCallback? onDPPageChange;

  // 视频播放开始
  final VideoProPlayCallback? onDJXVideoPlay;
  final VoidCallback? onDPVideoPlay;
  // 视频暂停
  final VideoProPlayCallback? onDJXVideoPause;
  final VoidCallback? onDPVideoPause;

  // 视频继续
  final VideoProPlayCallback? onDJXVideoContinue;
  final VoidCallback? onDPVideoContinue;

  // 视频播放完成
  final VideoProPlayCallback? onDJXVideoCompletion;
  final VoidCallback? onDPVideoCompletion;

  // 视频结束
  final VideoProPlayCallback? onDJXVideoOver;
  final VoidCallback? onDPVideoOver;
  // 视频关闭
  final VoidCallback? onVideoClose;
  // 拖动进度
  final VideoProDurationCallback? onSeekTo;
  // 播放进度
  final VideoProDurationCallback? onDurationChange;

  // 短故事
  // 进入短故事
  final NovelStoryCallback? onEnter;
  // 退出短故事
  final NovelStoryCallback? onExit;
  // 页面切换
  final NovelStoryCallback? onPageSelected;
  // 书籍结束
  final NovelStoryCallback? onBookEnd;

  // 播放错误
  final VideoProErrorCallback? onError;
  VideoPlayListener({
    this.onDJXPageChange,
    this.onDPPageChange,
    this.onDJXVideoPlay,
    this.onDPVideoPlay,
    this.onDJXVideoPause,
    this.onDPVideoPause,
    this.onDJXVideoContinue,
    this.onDPVideoContinue,
    this.onDJXVideoCompletion,
    this.onDPVideoCompletion,
    this.onDJXVideoOver,
    this.onDPVideoOver,
    this.onVideoClose,
    this.onSeekTo,
    this.onDurationChange,
    this.onError,
    this.onEnter,
    this.onExit,
    this.onPageSelected,
    this.onBookEnd,
  });
}

/// 短剧剧场监听
class TheaterListener {
  // 打开短剧详情
  final VideoProPlayCallback? onOpenDetail;

  TheaterListener({
    this.onOpenDetail,
  });
}

/// 短剧剧场详情页监听
class SelfDetailListener {
  // 打开短剧详情
  final VideoProPlayCallback? onOpenSelfDetail;

  SelfDetailListener({
    this.onOpenSelfDetail,
  });
}

/// 收藏监听
class FavDramaListener {
  /// 收藏
  final FavStatusCallback? favSet;
  FavDramaListener({
    this.favSet,
  });
}

/// 收藏或取消收藏监听
class DramaFavListener {
  // 打开短剧详情
  final DramaFavCallback? dramaFavCallback;

  DramaFavListener({
    this.dramaFavCallback,
  });
}

/// 短故事聚合页监听
class NovelStoryHomeListener {
  // 短故事点击
  final NovelStoryCallback? onItemClick;

  NovelStoryHomeListener({
    this.onItemClick,
  });
}

// 短故事
class NovelStoryListener {
  // 进入短故事
  final NovelStoryCallback? onEnter;
  // 退出短故事
  final NovelStoryCallback? onExit;
  // 页面切换
  final NovelStoryCallback? onPageSelected;
  // 书籍结束
  final NovelStoryCallback? onBookEnd;
  NovelStoryListener({
    this.onEnter,
    this.onExit,
    this.onPageSelected,
    this.onBookEnd,
  });
}
