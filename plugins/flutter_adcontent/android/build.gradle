group 'com.zero.flutter_adcontent'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url "https://artifact.bytedance.com/repository/Volcengine/"
        }
        maven {
            url "https://artifact.bytedance.com/repository/pangle/"
        }
        maven {
            url "https://artifact.bytedance.com/repository/AwemeOpenSDK"
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url "https://artifact.bytedance.com/repository/Volcengine/"
        }
        maven {
            url "https://artifact.bytedance.com/repository/pangle/"
        }
        maven {
            url "https://artifact.bytedance.com/repository/AwemeOpenSDK"
        }
    }
}

apply plugin: 'com.android.library'

android {
    if (project.android.hasProperty("namespace")) {
        namespace = "com.zero.flutter_adcontent"
    }
    compileSdkVersion 31

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 24
    }
}

configurations.all {
//    exclude group: "com.bytedance.boringssl.so", module: 'boringssl-so'
    //若接入流媒体sdk（小视频、直播）需要force constraint-layout版本
    resolutionStrategy {
        force 'com.android.support.constraint:constraint-layout:1.1.2'
    }
}

dependencies {

    // 小视频 sdk https://artifact.bytedance.com/repository/pangle/com/pangle/cn/pangrowth-sdk/maven-metadata.xml
    implementation ('com.pangle.cn:pangrowth-sdk:4.4.1.0'){
        exclude group: 'com.pangle.cn', module: 'partner-live-sdk'
        exclude group: 'com.pangle.cn', module: 'pangrowth-novel-sdk'
        exclude group: 'com.pangle.cn', module: 'pangrowth-game-sdk'
        exclude group: 'com.pangle.cn', module: 'pangrowth-luckycat-sdk'
        exclude group: 'com.pangle.cn', module: 'pangrowth-reward-sdk'
        exclude group: 'com.pangle.cn', module: 'partner-luckycat-api-sdk'
        exclude group: 'com.pangle.cn', module: 'pangrowth-luckycat-api'
    }
    //内容输出基础 sdk https://artifact.bytedance.com/repository/pangle/com/pangle/cn/pangrowth-base/maven-metadata.xml
    implementation 'com.pangle.cn:pangrowth-base:2.8.0.1'
    // 短剧 sdk https://artifact.bytedance.com/repository/pangle/com/pangle/cn/pangrowth-djx-sdk/maven-metadata.xml
    implementation 'com.pangle.cn:pangrowth-djx-sdk-lite:2.8.0.1'
    // 短故事 sdk https://artifact.bytedance.com/repository/pangle/com/pangle/cn/pangrowth-nov-sdk/maven-metadata.xml
    implementation 'com.pangle.cn:pangrowth-nov-sdk:2.8.0.1'
    // 广告插件（用于广告事件的统一）
    implementation project(':flutter_gromore_ads')

    // 其他 SDK 依赖
    implementation('com.volcengine:apm_insight_crash:1.4.4')
    implementation('com.squareup.picasso:picasso:2.71828')
    implementation('com.android.support:appcompat-v7:28.0.0')
    implementation('com.android.support:support-v4:28.0.0')
    implementation('com.android.support:recyclerview-v7:28.0.0')
    implementation('com.android.support.constraint:constraint-layout:1.1.2')
    implementation('com.android.support:design:28.0.0')
    implementation('com.android.support:support-annotations:28.0.0')
    implementation('com.volcengine:apm_insight_sdk:1.1.5')
    implementation('com.kyleduo.switchbutton:library:2.1.0')
    implementation('com.google.code.gson:gson:2.8.6')
    implementation('com.android.support:multidex:1.0.3')
    implementation('com.squareup.okhttp3:okhttp:3.10.0')

    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
}
