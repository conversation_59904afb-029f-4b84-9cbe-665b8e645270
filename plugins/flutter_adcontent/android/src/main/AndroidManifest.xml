<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zero.flutter_adcontent">

    <application>
        <!-- 短剧-->
        <activity
            android:name=".page.TheaterActivity"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".page.DetailActivity"
            android:theme="@style/AppTheme"
            android:configChanges="keyboard|orientation|screenSize"
            android:exported="false" />
        <activity
            android:name=".page.DrawActivity"
            android:theme="@style/AppTheme"
            android:configChanges="keyboard|orientation|screenSize"
            android:exported="false" />
        <!--短故事阅读页面-->
        <activity
            android:name="com.bytedance.sdk.nov.core.reader.NovReaderActivity"
            android:theme="@style/Theme.AppCompat.NoActionBar" />
        <activity
            android:name="com.bytedance.sdk.nov.core.reader.end.NovEndPageActivity"
            android:theme="@style/Theme.AppCompat.NoActionBar" />
    </application>

</manifest>