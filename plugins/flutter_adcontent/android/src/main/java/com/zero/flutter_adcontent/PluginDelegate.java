package com.zero.flutter_adcontent;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.sdk.djx.DJXSdk;
import com.bytedance.sdk.djx.DJXSdkConfig;
import com.bytedance.sdk.djx.IDJXPrivacyController;
import com.bytedance.sdk.djx.IDJXService;
import com.bytedance.sdk.djx.interfaces.listener.IDJXAdListener;
import com.bytedance.sdk.djx.model.DJXDrama;
import com.bytedance.sdk.djx.model.DJXEpisodeStatus;
import com.bytedance.sdk.djx.model.DJXError;
import com.bytedance.sdk.djx.model.DJXLock;
import com.bytedance.sdk.djx.model.DJXOthers;
import com.bytedance.sdk.djx.model.DJXUser;
import com.bytedance.sdk.dp.DPSdk;
import com.bytedance.sdk.dp.DPSdkConfig;
import com.bytedance.sdk.dp.IDPPrivacyController;
import com.bytedance.sdk.nov.api.INovCallback;
import com.bytedance.sdk.nov.api.NovSdk;
import com.bytedance.sdk.nov.api.NovSdkConfig;
import com.bytedance.sdk.nov.api.model.NovCategory;
import com.bytedance.sdk.nov.api.model.NovStory;
import com.bytedance.sdk.nov.api.params.NovReaderConfig;
import com.bytedance.sdk.nov.api.params.NovWidgetReaderParams;
import com.zero.flutter_adcontent.page.ConfigUtils;
import com.zero.flutter_adcontent.page.DetailActivity;
import com.zero.flutter_adcontent.page.DrawActivity;
import com.zero.flutter_adcontent.page.NativeViewFactory;
import com.zero.flutter_adcontent.page.TheaterActivity;
import com.zero.flutter_adcontent.utils.FADSDramaUtils;
import com.zero.flutter_adcontent.utils.FADSNovelStoryUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import io.flutter.BuildConfig;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

/// 插件代理
public class PluginDelegate implements MethodChannel.MethodCallHandler, EventChannel.StreamHandler {
    private final String TAG = "FlutterAdcontentPlugin";
    // Flutter 插件绑定对象
    public FlutterPlugin.FlutterPluginBinding bind;
    // 当前 Activity
    public Activity activity;
    // 当前 App Context
    public Context appContext;
    // 返回通道
    private MethodChannel.Result result;
    // 事件通道
    private EventChannel.EventSink eventSink;
    // 插件代理对象
    private static PluginDelegate _instance;

    public static PluginDelegate getInstance() {
        return _instance;
    }

    // 短剧详情 View
    public static final String KEY_VIEW_DRAMA = "flutter_adcontent_view_drama";
    // 短剧剧场 View
    public static final String KEY_VIEW_THEATER = "flutter_adcontent_view_theater";
    // 小视频 View
    public static final String KEY_VIEW_VIDEO = "flutter_adcontent_view_video";
    // 短故事聚合页 View
    public static final String KEY_VIEW_NOVEL_STORY = "flutter_adcontent_view_novel_story";
    // 短故事阅读页事件通道
    public static final String KEY_CHANNEL_NOVEL_STORY = "flutter_adcontent_channel_novel_story";
    // 短故事通道
    public MethodChannel novelChannel;
    // 用户中心 View
    public static final String KEY_VIEW_USER = "flutter_adcontent_view_user";
    // 初始化情况
    private boolean isInit = false;
    // 是否只需要备案过的
    private boolean  isOnlyICPNumber = false;
    // 青少年模式
    private boolean isTeenagerMode = false;
    // 短剧初始化
    private Boolean isInitSkit;
    // 小视频初始化
    private Boolean isInitVideo;
    // 短故事初始化
    private Boolean isInitNovel;

    /**
     * 插件代理构造函数构造函数
     *
     * @param activity      Activity
     * @param pluginBinding FlutterPluginBinding
     */
    public PluginDelegate(Activity activity, FlutterPlugin.FlutterPluginBinding pluginBinding) {
        this.activity = activity;
        this.appContext = activity.getApplicationContext();
        this.bind = pluginBinding;
        _instance = this;
    }

    /**
     * 方法通道调用
     *
     * @param call   方法调用对象
     * @param result 回调结果对象
     */
    @Override
    public void onMethodCall(MethodCall call, @NonNull MethodChannel.Result result) {
        String method = call.method;
        Log.d(TAG, "MethodChannel onMethodCall method:" + method + " arguments:" + call.arguments);
        if ("init".equals(method)) {
            init(call, result);
        } else if ("showTheaterPage".equals(method)) {
            showTheaterPage(call, result);
        } else if ("showDrawPage".equals(method)) {
            showDrawPage(call, result);
        } else if ("showDetailPage".equals(method)) {
            showDetailPage(call, result);
        } else if ("verifyDramaParams".equals(method)) {
            verifyDramaParams(call, result);
        } else if ("requestAllDramaByRecommend".equals(method)) {
            requestAllDramaByRecommend(call, result);
        } else if ("requestAllDrama".equals(method)) {
            requestAllDrama(call, result);
        } else if ("requestDrama".equals(method)) {
            requestDrama(call, result);
        } else if ("getEpisodesStatus".equals(method)) {
            getEpisodesStatus(call, result);
        } else if ("requestDramaByCategory".equals(method)) {
            requestDramaByCategory(call, result);
        } else if ("requestDramaCategoryList".equals(method)) {
            requestDramaCategoryList(result);
        } else if ("searchDrama".equals(method)) {
            searchDrama(call, result);
        } else if ("getDramaHistory".equals(method)) {
            getDramaHistory(call, result);
        } else if ("clearDramaHistory".equals(method)) {
            clearDramaHistory(call, result);
        } else if ("favorDrama".equals(method)) {
            favorDrama(call, result);
        } else if ("getFavorList".equals(method)) {
            getFavorList(call, result);
        } else if ("openNovelReaderPage".equals(method)) {
            openNovelReaderPage(call, result);
        } else if ("getStoryCategoryList".equals(method)) {
            getStoryCategoryList(result);
        } else if ("searchStory".equals(method)) {
            searchStory(call, result);
        } else if ("setGlobalSpeedPlay".equals(method)) {
            setGlobalSpeedPlay(call, result);
        } else if ("getSignString".equals(method)) {
            getSignString(call, result);
        } else if ("login".equals(method)) {
            login(call, result);
        } else if ("isLogin".equals(method)) {
            isLogin(call, result);
        } else if ("logout".equals(method)) {
            logout(call, result);
        } else if ("requestStoryByCategory".equals(method)) {
            requestStoryByCategory(call, result);
        } else if ("requestStoryByIds".equals(method)) {
            requestStoryByIds(call, result);
        } else if ("requestStoryFeed".equals(method)) {
            requestStoryFeed(call, result);
        } else if ("getStoryHistory".equals(method)) {
            getStoryHistory(call, result);
        } else if ("storyFavorite".equals(method)) {
            storyFavorite(call, result);
        } else if ("storyFavoriteCancel".equals(method)) {
            storyFavoriteCancel(call, result);
        } else if ("getStoryFavorite".equals(method)) {
            getStoryFavorite(call, result);
        } else {
            result.notImplemented();
        }
    }

    /**
     * 建立事件通道监听
     *
     * @param arguments 参数
     * @param events    事件回调对象
     */
    @Override
    public void onListen(Object arguments, EventChannel.EventSink events) {
        Log.d(TAG, "EventChannel onListen arguments:" + arguments);
        eventSink = events;
    }

    /**
     * 取消事件通道监听
     *
     * @param arguments 参数
     */
    @Override
    public void onCancel(Object arguments) {
        Log.d(TAG, "EventChannel onCancel");
        eventSink = null;
    }

    /**
     * 添加事件
     *
     * @param event 事件
     */
    public void addEvent(Object event) {
        if (eventSink != null) {
            Log.d(TAG, "EventChannel addEvent event:" + event.toString());
            eventSink.success(event);
        }
    }

    /**
     * 注册短剧视图
     */
    public void regDramaView() {
        bind.getPlatformViewRegistry().registerViewFactory(KEY_VIEW_DRAMA, new NativeViewFactory(KEY_VIEW_DRAMA, this));
    }

    /**
     * 注册剧场视图
     */
    public void regTheaterView() {
        bind.getPlatformViewRegistry().registerViewFactory(KEY_VIEW_THEATER, new NativeViewFactory(KEY_VIEW_THEATER, this));
    }

    /**
     * 注册小视频视图
     */
    public void regVideoView() {
        bind.getPlatformViewRegistry().registerViewFactory(KEY_VIEW_VIDEO, new NativeViewFactory(KEY_VIEW_VIDEO, this));
    }

    /**
     * 注册短故事视图
     */
    public void regNovelView() {
        bind.getPlatformViewRegistry().registerViewFactory(KEY_VIEW_NOVEL_STORY, new NativeViewFactory(KEY_VIEW_NOVEL_STORY, this));
    }

    /**
     * 初始化短剧小视频
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void init(MethodCall call, final MethodChannel.Result result) {
        Boolean initSkit = call.argument("initSkit");
        Boolean initVideo = call.argument("initVideo");
        Boolean initNovel = call.argument("initNovel");
        this.isOnlyICPNumber = call.argument("isOnlyICPNumber");
        this.isTeenagerMode = call.argument("isTeenagerMode");
        final String skitSettingFile = call.argument("settingFile");
        final AtomicInteger initCount = new AtomicInteger(3); // 初始化计数器为2
        final AtomicBoolean overallSuccess = new AtomicBoolean(true); // 用来记录整体是否成功
        // 初始化短剧
        if (initSkit != null && initSkit) {
            initSkitSdk(skitSettingFile, new DJXSdk.StartListener() {
                @Override
                public void onStartComplete(boolean success, String s, @Nullable DJXError djxError) {
                    if (success) {
                        Log.d(TAG, "🎬短剧==> 初始化成功");
                    } else {
                        Log.d(TAG, "🎬短剧==> 初始化失败" + s + " err:" + djxError.toString());
                    }
                    handleInitResult(success, overallSuccess, initCount, result);
                }
            });
        } else {
            initCount.decrementAndGet(); // 不初始化短剧，递增计数器以避免减少
        }
        // 初始化小视频
        if (initVideo != null && initVideo) {
            initVideoSdk(skitSettingFile, new DPSdk.StartListener() {
                @Override
                public void onStartComplete(boolean success, String errMsg) {
                    if (success) {
                        Log.d(TAG, "📱小视频==> 初始化成功");
                    } else {
                        Log.d(TAG, "📱小视频==> 初始化失败:" + errMsg);
                    }
                    handleInitResult(success, overallSuccess, initCount, result);
                }
            });
        } else {
            initCount.decrementAndGet(); // 不初始化小视频，递增计数器以避免减少
        }

        // 初始化短故事
        if (initNovel != null && initNovel) {
            initNovelSdk(skitSettingFile, new NovSdk.StartListener() {
                @Override
                public void onStartComplete(boolean success, @Nullable String s, @Nullable DJXError djxError) {
                    if (success) {
                        Log.d(TAG, "📚短故事==> 初始化成功");
                        initNovelChannel();
                    } else {
                        Log.d(TAG, "📚短故事==> 初始化失败" + s + " err:" + djxError.toString());
                    }
                    handleInitResult(success, overallSuccess, initCount, result);
                }
            });
        } else {
            initCount.decrementAndGet(); // 不初始化短故事，递增计数器以避免减少
        }
    }

    /**
     * 处理初始化结果
     *
     * @param success        是否成功
     * @param overallSuccess 是否总体成功
     * @param initCount      初始化计数
     * @param result         结果
     */
    private void handleInitResult(boolean success,
                                  AtomicBoolean overallSuccess, AtomicInteger initCount,
                                  MethodChannel.Result result) {
        if (!success) {
            overallSuccess.set(false); // 记录失败
        }
        if (initCount.decrementAndGet() == 0) { // 检查是否所有初始化都已完成
            if (overallSuccess.get()) {
                Log.d(TAG, "🍎所有初始化成功");
                result.success(true);
            } else {
                Log.e(TAG, "🍐至少一个初始化失败");
                result.success(false);
            }
        }
    }

    /**
     * 初始化短剧 SDK
     *
     * @param skitSettingFile 短剧配置文件
     * @param startListener   开始监听
     */
    private void initSkitSdk(String skitSettingFile, DJXSdk.StartListener startListener) {
        Log.d(TAG, "开始初始化短剧SDK:" + skitSettingFile);
        // 初始化短剧
        DJXSdkConfig config = new DJXSdkConfig.Builder()
                .debug(BuildConfig.RELEASE)
                .privacyController(new IDJXPrivacyController() {
                    @Override
                    public boolean isOnlyICPNumber() {
                        return isOnlyICPNumber;
                    }
                    @Override
                    public boolean isTeenagerMode() {
                        return isTeenagerMode;
                    }
                })
                .build();
        DJXSdk.init(appContext, skitSettingFile + ".json", config);
        DJXSdk.start(startListener);
    }

    /**
     * 初始化小视频 SDK
     *
     * @param videoSettingFile 短剧配置文件
     * @param startListener    开始监听
     */
    private void initVideoSdk(String videoSettingFile, DPSdk.StartListener startListener) {
        Log.d(TAG, "初始化小视频 SDK:" + videoSettingFile);
        DPSdkConfig config = new DPSdkConfig.Builder()
                .debug(BuildConfig.RELEASE)
                .build();
        DPSdk.init(appContext, videoSettingFile + ".json", config);
        DPSdk.start(startListener);
    }

    /**
     * 初始化短故事 SDK
     *
     * @param videoSettingFile 短剧配置文件
     * @param startListener    开始监听
     */
    private void initNovelSdk(String videoSettingFile, NovSdk.StartListener startListener) {
        Log.d(TAG, "初始化短故事 SDK:" + videoSettingFile);
        NovSdkConfig config = new NovSdkConfig.Builder()
                .debug(BuildConfig.RELEASE)
                .privacyController(new IDJXPrivacyController() {
                    @Override
                    public boolean isOnlyICPNumber() {
                        return isOnlyICPNumber;
                    }
                    @Override
                    public boolean isTeenagerMode() {
                        return isTeenagerMode;
                    }
                })
                .build();
        NovSdk.init(appContext, videoSettingFile + ".json", config);
        NovSdk.start(startListener);
    }

    /**
     * 初始化短剧通讯通道
     */
    private void initNovelChannel(){
        novelChannel = new MethodChannel(this.bind.getBinaryMessenger(), KEY_CHANNEL_NOVEL_STORY);
        novelChannel.setMethodCallHandler(ConfigUtils::novelMethodCall);
    }

    /**
     * 显示剧场页面
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void showTheaterPage(MethodCall call, MethodChannel.Result result) {
        ConfigUtils.detailCallThreat = call;
        Intent intent = new Intent(activity, TheaterActivity.class);
        intent.putExtra("showBackBtn", (Boolean) call.argument("showBackBtn"));
        intent.putExtra("showPageTitle", (Boolean) call.argument("showPageTitle"));
        intent.putExtra("showChangeBtn", (Boolean) call.argument("showChangeBtn"));
        activity.startActivity(intent);
        result.success(true);
    }

    /**
     * 显示短剧滑滑流
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void showDrawPage(MethodCall call, MethodChannel.Result result) {
        ConfigUtils.detailCall = call;
        Intent intent = new Intent(activity, DrawActivity.class);
        intent.putExtra("hideInfo", (Boolean) call.argument("hideInfo"));
        intent.putExtra("hideEnter", (Boolean) call.argument("hideEnter"));
        intent.putExtra("topDramaId", (Integer) call.argument("topDramaId"));
        activity.startActivity(intent);
        result.success(true);
    }

    /**
     * 显示短剧详情页面
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void showDetailPage(MethodCall call, MethodChannel.Result result) {
        ConfigUtils.detailCall = call;
        Intent intent = new Intent(activity, DetailActivity.class);
        intent.putExtra("id", (Integer) call.argument("id"));
        intent.putExtra("index", (Integer) call.argument("index"));
        intent.putExtra("groupId", (String) call.argument("groupId"));
        activity.startActivity(intent);
        result.success(true);
    }

    /**
     * 获取短剧列表回调
     *
     * @param result Result
     * @return IDJXService.IDJXCallback
     */
    private IDJXService.IDJXCallback<List<? extends DJXDrama>> getListIDJXCallback(MethodChannel.Result result) {
        return new IDJXService.IDJXCallback<List<? extends DJXDrama>>() {
            @Override
            public void onSuccess(List<? extends DJXDrama> djxDramas, @Nullable DJXOthers djxOthers) {
                Log.d(TAG, "getListIDJXCallback  onSuccess:" + djxDramas.toString());
                result.success(FADSDramaUtils.toList(djxDramas));
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "getListIDJXCallback  onError:" + djxError.toString());
                result.success(null);
            }
        };
    }

    /**
     * 验证短剧参数
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void verifyDramaParams(MethodCall call, MethodChannel.Result result) {
        int total = call.argument("total");
        int freeSet = call.argument("freeSet");
        int lockSet = call.argument("lockSet");
        DJXSdk.service().verifyDramaParams(total, freeSet, lockSet, new IDJXService.IDJXCallback<DJXLock>() {
            @Override
            public void onSuccess(DJXLock djxLock, @Nullable DJXOthers djxOthers) {
                result.success(true);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                result.success(false);
                Log.e(TAG, "verifyDramaParams onError :" + djxError.toString());
            }
        });
    }

    /**
     * 获取短剧解锁状态
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void getEpisodesStatus(MethodCall call, MethodChannel.Result result) {
        int id = call.argument("id");
        int freeSet = call.argument("freeSet");
        DJXSdk.service().getEpisodesStatus(id, freeSet, new IDJXService.IDJXCallback<List<DJXEpisodeStatus>>() {
            @Override
            public void onSuccess(List<DJXEpisodeStatus> djxEpisodeStatuses, @Nullable DJXOthers djxOthers) {
                result.success(FADSDramaUtils.toEpisodeStatusList(djxEpisodeStatuses));
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                result.success(null);
                Log.e(TAG, "getEpisodesStatus onError :" + djxError.toString());
            }
        });
    }

    /**
     * 通过个性化推荐获取所有短剧
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestAllDramaByRecommend(MethodCall call, MethodChannel.Result result) {
        int page = call.argument("page");
        int count = call.argument("count");
        DJXSdk.service().requestAllDramaByRecommend(page, count, getListIDJXCallback(result));
    }

    /**
     * 批量获取所有短剧
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestAllDrama(MethodCall call, MethodChannel.Result result) {
        int page = call.argument("page");
        int count = call.argument("count");
        int order = call.argument("order");
        DJXSdk.service().requestAllDrama(page, count, order==0, getListIDJXCallback(result));
    }

    /**
     * 根据短剧id获取短剧信息
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestDrama(MethodCall call, MethodChannel.Result result) {
        List<Integer> dramaIdsInt = call.argument("dramaIds");
        if (dramaIdsInt == null) {
            result.error("dramaIds is null", "dramaIds is null", null);
            return;
        }
        List<Long> dramaIds = new ArrayList<>();
        for (Integer dramaId : dramaIdsInt) {
            dramaIds.add(dramaId.longValue());
        }
        DJXSdk.service().requestDrama(dramaIds, getListIDJXCallback(result));
    }


    /**
     * 按分类请求短剧
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestDramaByCategory(MethodCall call, MethodChannel.Result result) {
        String category = call.argument("category");
        int page = call.argument("page");
        int count = call.argument("count");
        int order = call.argument("order");
        DJXSdk.service().requestDramaByCategory(category, page, count, order, getListIDJXCallback(result));
    }


    /**
     * 请求短剧分类列表
     *
     * @param result Result
     */
    public void requestDramaCategoryList(MethodChannel.Result result) {
        DJXSdk.service().requestDramaCategoryList(new IDJXService.IDJXCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> list, @Nullable DJXOthers djxOthers) {
                result.success(list);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "requestDramaCategoryList  onError code:" + djxError.toString());
                result.success(null);
            }
        });
    }


    /**
     * 搜索短剧
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void searchDrama(MethodCall call, MethodChannel.Result result) {
        String query = call.argument("query");
        boolean isFuzzy = call.argument("isFuzzy");
        int page = call.argument("page");
        int count = call.argument("count");
        DJXSdk.service().searchDrama(query, isFuzzy, page, count, getListIDJXCallback(result));
    }


    /**
     * 获取短剧历史记录
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void getDramaHistory(MethodCall call, MethodChannel.Result result) {
        int page = call.argument("page");
        int count = call.argument("count");
        DJXSdk.service().getDramaHistory(page, count, getListIDJXCallback(result));
    }


    /**
     * 清理短剧历史记录
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void clearDramaHistory(MethodCall call, MethodChannel.Result result) {
        DJXSdk.service().clearDramaHistory(new IDJXService.IDJXCallback<List<? extends DJXDrama>>() {
            @Override
            public void onSuccess(List<? extends DJXDrama> djxDramas, @Nullable DJXOthers djxOthers) {
                result.success(true);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "clearDramaHistory  onError code:" + djxError.toString());
                result.success(false);
            }
        });
    }

    /**
     * 收藏短剧
     */
    public void favorDrama(MethodCall call, MethodChannel.Result result) {
        int id = call.argument("id");
        boolean state = call.argument("state");
        DJXSdk.service().favorDrama(id, state, new IDJXService.IDJXCallback<Object>() {
            @Override
            public void onSuccess(Object djxDrama, @Nullable DJXOthers djxOthers) {
                result.success(true);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "favorDrama  onError code:" + djxError.toString());
                result.success(false);
            }
        });
    }

    /**
     * 获取短剧收藏列表
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void getFavorList(MethodCall call, MethodChannel.Result result) {
        int page = call.argument("page");
        int count = call.argument("count");
        DJXSdk.service().getFavorList(page, count, getListIDJXCallback(result));
    }

    /**
     * 设置短剧全局倍速
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void setGlobalSpeedPlay(MethodCall call, MethodChannel.Result result) {
        double speed = call.argument("speed");
        DJXSdk.service().setGlobalSpeedPlay((float) speed);
        result.success(true);
    }

    // ==========
    // 短故事接口
    // ==========

    /**
     * 打开短故事阅读页面
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void openNovelReaderPage(MethodCall call, MethodChannel.Result result) {
        ConfigUtils.novelCall = call;
        NovStory novStory = FADSNovelStoryUtils.fromMap(call.argument("novelStory"));
        // 阅读页配置
        NovReaderConfig config = ConfigUtils.getNovReaderConfig();
        if (config == null) {
            Log.e(TAG, "openNovelReaderPage  config is null");
            result.success(false);
            return;
        }
        ConfigUtils.novelChannel=novelChannel;
        // 阅读页组件参数
        NovWidgetReaderParams params = new NovWidgetReaderParams(novStory, config);
        NovSdk.factory().openReader(params);
        result.success(true);
    }


    /**
     * 获取短故事类目列表
     *
     * @param result Result
     */
    public void getStoryCategoryList(MethodChannel.Result result) {
        NovSdk.service().requestCategoryList(new INovCallback<List<NovCategory>>() {
            @Override
            public void onSuccess(List<NovCategory> novCategories, @Nullable DJXOthers djxOthers) {
                result.success(FADSNovelStoryUtils.toCategoryList(novCategories));
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "getStoryCategoryList  onError code:" + djxError.toString());
                result.success(null);
            }
        });
    }

    /**
     * 搜索短故事
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void searchStory(MethodCall call, MethodChannel.Result result) {
        String query = call.argument("query");
        boolean isFuzzy = call.argument("isFuzzy");
        int page = call.argument("page");
        int count = call.argument("count");
        NovSdk.service().searchStory(query, isFuzzy, page, count, getStoryListCallback(result));
    }


    /**
     * 根据类目请求短故事
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestStoryByCategory(MethodCall call, MethodChannel.Result result) {
        int id = call.argument("id");
        int order = call.argument("order");
        int page = call.argument("page");
        int count = call.argument("count");
        NovSdk.service().requestStoryByCategory(id, order, page, count, getStoryListCallback(result));
    }

    /**
     * 根据短故事id请求
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestStoryByIds(MethodCall call, MethodChannel.Result result) {
        List<Integer> ids = call.argument("ids");
        int page = call.argument("page");
        int count = call.argument("count");
        NovSdk.service().requestStoryByIds(ids, page, count, getStoryListCallback(result));
    }

    /**
     * 获取短故事精选列表
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void requestStoryFeed(MethodCall call, MethodChannel.Result result) {
        int order = call.argument("order");
        int page = call.argument("page");
        int count = call.argument("count");
        NovSdk.service().requestStoryFeed(order, count, page, getStoryListCallback(result));
    }

    /**
     * 获取短故事历史记录
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void getStoryHistory(MethodCall call, MethodChannel.Result result) {
        int page = call.argument("page");
        int count = call.argument("count");
        NovSdk.service().getStoryHistory(page, count, getStoryListCallback(result));
    }

    /**
     * 收藏短故事
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void storyFavorite(MethodCall call, MethodChannel.Result result) {
        int id = call.argument("id");
        NovSdk.service().storyFavorite(id, new INovCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean, @Nullable DJXOthers djxOthers) {
                result.success(true);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "storyFavorite onError:" + djxError.toString());
                result.success(false);
            }
        });
    }

    /**
     * 取消收藏短故事
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void storyFavoriteCancel(MethodCall call, MethodChannel.Result result) {
        int id = call.argument("id");
        NovSdk.service().storyFavoriteCancel(id, new INovCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean, @Nullable DJXOthers djxOthers) {
                result.success(true);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "storyFavoriteCancel onError:" + djxError.toString());
                result.success(false);
            }
        });
    }

    /**
     * 获取短故事收藏列表
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void getStoryFavorite(MethodCall call, MethodChannel.Result result) {
        int page = call.argument("page");
        int count = call.argument("count");
        NovSdk.service().getStoryFavorite(page, count, getStoryListCallback(result));
    }


    /**
     * 通用短故事回调
     */
    private INovCallback<List<NovStory>> getStoryListCallback(MethodChannel.Result result) {
        return new INovCallback<List<NovStory>>() {
            @Override
            public void onSuccess(List<NovStory> novStories, @Nullable DJXOthers djxOthers) {
                result.success(FADSNovelStoryUtils.toList(novStories));
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                result.success(null);
            }
        };
    }

    // ==========
    // 登录相关接口
    // ==========


    /**
     * 获取签名字符串
     * [key] 秘钥，必须
     * /// [nonce] 16为随机字符串，必须
     * /// [time] 时间戳，单位秒，必须
     * /// [ouid] 值为开发者用户id
     */
    public void getSignString(MethodCall call, MethodChannel.Result result) {
        String key = call.argument("key");
        String nonce = call.argument("nonce");
        int time = call.argument("time");
        HashMap<String, String> params = call.argument("params");
        String signString = DJXSdk.service().getSignString(key, nonce, time, params);
        result.success(signString);
    }

    /**
     * 登录
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void login(MethodCall call, MethodChannel.Result result) {
        String params = call.argument("params");

        DJXSdk.service().login(params, new IDJXService.IDJXCallback<DJXUser>() {
            @Override
            public void onSuccess(DJXUser djxUser, @Nullable DJXOthers djxOthers) {
                result.success(true);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "login  onError code:" + djxError.toString());
                result.success(false);
            }
        });
    }

    /**
     * 是否已登录
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void isLogin(MethodCall call, MethodChannel.Result result) {
        boolean isLogin = DJXSdk.service().isLogin();
        result.success(isLogin);
    }

    /**
     * 登出
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void logout(MethodCall call, MethodChannel.Result result) {
        DJXSdk.service().logout(new IDJXService.IDJXCallback<DJXUser>() {
            @Override
            public void onSuccess(DJXUser djxUser, @Nullable DJXOthers djxOthers) {
                result.success(true);
            }

            @Override
            public void onError(@NonNull DJXError djxError) {
                Log.e(TAG, "logout  onError code:" + djxError.toString());
                result.success(false);
            }
        });
    }

}
