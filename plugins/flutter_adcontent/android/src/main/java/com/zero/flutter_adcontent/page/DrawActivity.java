package com.zero.flutter_adcontent.page;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.bytedance.sdk.djx.DJXSdk;
import com.bytedance.sdk.djx.IDJXDramaDetailDelegate;
import com.bytedance.sdk.djx.IDJXWidget;
import com.bytedance.sdk.djx.interfaces.listener.IDJXDrawListener;
import com.bytedance.sdk.djx.model.DJXDrama;
import com.bytedance.sdk.djx.model.DJXDramaDetailConfig;
import com.bytedance.sdk.djx.params.DJXWidgetDrawParams;
import com.zero.flutter_adcontent.R;

import java.util.List;
import java.util.Map;

public class DrawActivity extends AppCompatActivity {
    private IDJXWidget mWidget;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_teater);
        initView();
        initData();
    }

    protected void initView() {

    }

    protected void initData() {
        // 解析参数
        Intent intent = getIntent();
        boolean hideInfo = intent.getBooleanExtra("hideInfo", true);
        boolean hide_enter = intent.getBooleanExtra("hideEnter", true);
        long topDramaId = intent.getIntExtra("topDramaId", -1);
        // 创建剧场配置
        DJXDramaDetailConfig dramaDetailConfig = ConfigUtils.createDramaDetailConfig();
        DJXWidgetDrawParams widgetDrawParams = DJXWidgetDrawParams.obtain();
        widgetDrawParams.detailConfig(dramaDetailConfig);
        widgetDrawParams.drawChannelType(DJXWidgetDrawParams.DRAW_CHANNEL_TYPE_RECOMMEND_THEATER);
        widgetDrawParams.drawContentType(DJXWidgetDrawParams.DRAW_CONTENT_TYPE_ONLY_DRAMA);
        widgetDrawParams.hideChannelName(false);
        widgetDrawParams.hideDramaInfo(hideInfo);
        widgetDrawParams.hideDramaEnter(hide_enter);
        widgetDrawParams.dramaFree(dramaDetailConfig.getFreeSet());
        widgetDrawParams.topDramaId(topDramaId);
        widgetDrawParams.setEnterDelegate(new IDJXDramaDetailDelegate() {
            @Override
            public void onEnter(Context context, DJXDrama djxDrama, int i) {
                // 点击进入短剧详情
                Log.d("DrawActivity", "onEnter: " + djxDrama.id);
            }
        });
        widgetDrawParams.listener(new IDJXDrawListener() {
            @Override
            public View createCustomView(ViewGroup viewGroup, @Nullable Map<String, Object> map) {
                Context context = viewGroup.getContext();;
                DramaInfoView dramaInfoView = new DramaInfoView(context, 52, true,false);
                // 2. 从参数 map 中获取需要设置的数据（假设 map 来自 Flutter）
                String coverUrl = map != null && map.containsKey("cover_image") ? map.get("cover_image").toString() : "";
                String title = map != null && map.containsKey("title") ? map.get("title").toString() : "默认标题";
                String desc = map != null && map.containsKey("desc") ? map.get("desc").toString() : "默认简介";

                int nTotal = getIntFromMap(map,"total",1);
                int nIndex = getIntFromMap(map,"index",1);
                String jvJiDesc = String.format("第%d集 - 共%d集", nIndex, nTotal);

                // 标签可能是 List<String> 类型
                String[] tags = new String[]{"标签1", "标签2"};
                if (map != null && map.containsKey("tags") && map.get("tags") instanceof List) {
                    List<?> tagList = (List<?>) map.get("tags");
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        tags = tagList.stream().map(Object::toString).toArray(String[]::new);
                    }
                }

                // 3. 设置数据
                dramaInfoView.setData(coverUrl, title, tags, desc, jvJiDesc);
                return dramaInfoView;

            }
        });
        mWidget = DJXSdk.factory().createDraw(widgetDrawParams);
        // 更新 View
        getSupportFragmentManager().beginTransaction().replace(R.id.fl_theater_container, mWidget.getFragment()).commit();
    }

    public static int getIntFromMap(Map<String, Object> map, String key, int defaultValue) {
        if (map != null && map.containsKey(key)) {
            Object value = map.get(key);
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            if (value instanceof String) {
                try {
                    return Integer.parseInt((String) value);
                } catch (NumberFormatException e) {
                    // fall through to return default
                }
            }
        }
        return defaultValue;
    }
}