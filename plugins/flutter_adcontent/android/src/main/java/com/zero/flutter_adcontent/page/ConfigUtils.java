package com.zero.flutter_adcontent.page;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.FragmentActivity;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bytedance.sdk.djx.DJXRewardAdResult;
import com.bytedance.sdk.djx.interfaces.listener.IDJXAdListener;
import com.bytedance.sdk.djx.interfaces.listener.IDJXDramaListener;
import com.bytedance.sdk.djx.interfaces.listener.IDJXDramaUnlockListener;
import com.bytedance.sdk.djx.model.DJXDrama;
import com.bytedance.sdk.djx.model.DJXDramaDetailConfig;
import com.bytedance.sdk.djx.model.DJXDramaUnlockAdMode;
import com.bytedance.sdk.djx.model.DJXDramaUnlockInfo;
import com.bytedance.sdk.djx.model.DJXDramaUnlockMethod;
import com.bytedance.sdk.djx.model.DJXUnlockModeType;
import com.bytedance.sdk.dp.DPSdk;
import com.bytedance.sdk.dp.DPWidgetDrawParams;
import com.bytedance.sdk.dp.DPWidgetGridParams;
import com.bytedance.sdk.dp.IDPAdListener;
import com.bytedance.sdk.dp.IDPDrawListener;
import com.bytedance.sdk.dp.IDPGridListener;
import com.bytedance.sdk.dp.IDPWidget;
import com.bytedance.sdk.nov.api.NovRewardAdResult;
import com.bytedance.sdk.nov.api.iface.INovLockerViewProvider;
import com.bytedance.sdk.nov.api.iface.INovReaderListener;
import com.bytedance.sdk.nov.api.iface.INovUnlockListener;
import com.bytedance.sdk.nov.api.model.NovPage;
import com.bytedance.sdk.nov.api.model.NovStory;
import com.bytedance.sdk.nov.api.params.NovReaderConfig;
import com.bytedance.sdk.nov.api.widget.AbsNovLockerView;
import com.zero.flutter_adcontent.R;
import com.zero.flutter_adcontent.utils.AdEventUtils;
import com.zero.flutter_adcontent.utils.FADSDramaUtils;
import com.zero.flutter_adcontent.utils.FADSNovelStoryUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import kotlin.Pair;

/**
 * 配置工具类
 */
public class ConfigUtils {
    private static final String TAG = ConfigUtils.class.getSimpleName();
    //短剧详情页参数
    public static MethodCall detailCall;

    public static MethodCall detailCallThreat;
    public static MethodChannel detailChannel;

    public static MethodChannel detailChannelThreat;
    public static IDJXDramaUnlockListener.UnlockCallback unlockCallback;
    public static IDJXDramaUnlockListener.CustomAdCallback customAdCallback;
    // 小视频详情参数
    public static MethodCall videoCall;
    public static MethodChannel videoChannel;
    // 短故事参数
    public static MethodCall novelCall;
    public static MethodChannel novelChannel;
    public static INovUnlockListener.CustomAdCallback novelCustomAdCallback;

    public static boolean bCollectDrama = false;
    public static boolean bCollectThreat = false;

    public  static  Map<Integer, DramaInfoView> viewCache = new HashMap<>();

    /**
     * 解析参数，构建短剧详情配置
     */
    public static DJXDramaDetailConfig getDetailConfig() {
        if (detailCall == null) {
            return null;
        }
        // 创建详情配置
        DJXDramaDetailConfig config = createDramaDetailConfig();
        // 配置参数
        config.hideTopInfo(detailCall.argument("hideTopInfo"));
        config.setTopOffset((Integer) detailCall.argument("setTopOffset"));
        config.hideBottomInfo((Boolean) detailCall.argument("hideBottomInfo"));
        config.setBottomOffset((Integer) detailCall.argument("setBottomOffset"));
        config.hideRewardDialog((Boolean) detailCall.argument("hideRewardDialog"));
        config.hideMore((Boolean) detailCall.argument("hideMore"));
        config.hideCellularToast((Boolean) detailCall.argument("hideCellularToast"));
        config.hideBack((Boolean) detailCall.argument("hideBack"), null);
        config.hideLikeButton(detailCall.argument("hideLikeButton"));
        config.hideFavorButton(detailCall.argument("hideFavorButton"));
        return config;
    }

    public static DJXDramaDetailConfig getDetailFeedConfig() {
        if (detailCallThreat == null) {
            return null;
        }
        // 创建详情配置
        DJXDramaDetailConfig config = createDramaFlowDetailConfig();
        // 配置参数
        config.hideTopInfo(detailCallThreat.argument("hideTopInfo"));
        config.setTopOffset((Integer) detailCallThreat.argument("setTopOffset"));
        config.hideBottomInfo((Boolean) detailCallThreat.argument("hideBottomInfo"));
        config.setBottomOffset((Integer) detailCallThreat.argument("setBottomOffset"));
        config.hideRewardDialog((Boolean) detailCallThreat.argument("hideRewardDialog"));
        config.hideBack((Boolean) detailCallThreat.argument("hideBack"), null);
        config.hideLikeButton(detailCallThreat.argument("hideLikeButton"));
        config.hideFavorButton(detailCallThreat.argument("hideFavorButton"));
        config.hideLongClickSpeed(detailCallThreat.argument("hideLongClickSpeed"));
        return config;
    }

    public static void setDramaFavStatus(boolean bFav,int nViewId) {
        bCollectDrama = bFav;
        for (Map.Entry<Integer, DramaInfoView> entry : viewCache.entrySet()) {
            DramaInfoView view = entry.getValue();
            if (view != null) {
                view.applyFavoriteDrawable(bFav); // 或 false，取决于你的逻辑
            }
        }

    }

    @SuppressWarnings("unchecked")
    public static <T> T getValue(Map<String, Object> map, String key, T defaultValue) {
        if (map == null || !map.containsKey(key)) return defaultValue;

        Object value = map.get(key);
        if (value == null) return defaultValue;

        Class<?> clazz = defaultValue.getClass();

        // 1. 类型一致，直接返回
        if (clazz.isInstance(value)) {
            return (T) value;
        }

        // 2. 处理 Number -> int, long, double, float, etc.
        if (value instanceof Number) {
            Number number = (Number) value;
            if (clazz == Integer.class) return (T) Integer.valueOf(number.intValue());
            if (clazz == Long.class) return (T) Long.valueOf(number.longValue());
            if (clazz == Double.class) return (T) Double.valueOf(number.doubleValue());
            if (clazz == Float.class) return (T) Float.valueOf(number.floatValue());
            if (clazz == Short.class) return (T) Short.valueOf(number.shortValue());
            if (clazz == Byte.class) return (T) Byte.valueOf(number.byteValue());
            if (clazz == Boolean.class) return (T) Boolean.valueOf(number.intValue() != 0);
        }

        // 3. String 转换
        if (clazz == String.class) {
            return (T) value.toString();
        }

        // 4. Boolean 支持字符串和数字
        if (clazz == Boolean.class) {
            if (value instanceof String) {
                return (T) Boolean.valueOf((String) value);
            }
            if (value instanceof Number) {
                return (T) Boolean.valueOf(((Number) value).intValue() != 0);
            }
        }

        // 5. 尝试强转（如 List、Map）
        try {
            return (T) value;
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }




    public static int getIntFromMap(Map<String, Object> map, String key, int defaultValue) {
        if (map != null && map.containsKey(key)) {
            Object value = map.get(key);
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            if (value instanceof String) {
                try {
                    return Integer.parseInt((String) value);
                } catch (NumberFormatException e) {
                    // fall through to return default
                }
            }
        }
        return defaultValue;
    }

    /**
     * 生成详情配置
     *
     * @return 详情配置
     */
    public static DJXDramaDetailConfig createDramaDetailConfig() {
        int detailFree = -1;
        int unlockCount = 1;
        try {
            detailFree = (Integer) detailCall.argument("detailFree");
            unlockCount = (Integer) detailCall.argument("unlockCount");
        } catch (Exception ex) {
            Log.w("FlutterAdcontentPlugin", "createDramaDetailConfig detailFree is null");
        }
        // 是否隐藏默认激励弹窗
        boolean hideRewardDialog = Boolean.TRUE.equals(detailCall.argument("hideRewardDialog"));
        // 解锁广告是否为广告 SDK
        int unlockAdMode = (Integer) detailCall.argument("unlockAdMode");
        DJXDramaUnlockAdMode unlockMode = unlockAdMode == 0 ? DJXDramaUnlockAdMode.MODE_COMMON : DJXDramaUnlockAdMode.MODE_SPECIFIC;
        // 第二个参数是短剧详情免费集数，默认-1，会使用sdk的默认设置
        IDJXAdListener adListener = AdEventUtils.getIDJXAdListener("ConfigUtils");
        // 每次广告解锁的集数
        int finalUnlockCount = unlockCount;

        bCollectDrama = Boolean.TRUE.equals(detailCall.argument("bFav"));
        viewCache.clear();
        ///描述
        return DJXDramaDetailConfig.obtain(unlockMode, detailFree, new IDJXDramaUnlockListener() {
                    @Override
                    public void unlockFlowStart(@NonNull DJXDrama drama, @NonNull UnlockCallback callback, @Nullable Map<String, ?> map) {
                        unlockCallback = callback;
                        sendEvent("unlockFlowStart", FADSDramaUtils.toMap(drama));
                        if (!hideRewardDialog) {
                            callback.onConfirm(genUnlockInfo(drama.id, finalUnlockCount, false));
                        }
                    }

                    @Override
                    public void unlockFlowEnd(@NonNull DJXDrama drama, @Nullable UnlockErrorStatus unlockErrorStatus, @Nullable Map<String, ?> map) {
                        // 发送解锁结果
                        HashMap<String, Object> sendData;
                        if (unlockErrorStatus == null) {
                            sendData = FADSDramaUtils.toErrMap(200, "正常解锁");
                        } else {
                            sendData = FADSDramaUtils.toErrMap(unlockErrorStatus.ordinal(), unlockErrorStatus.name());
                        }
                        sendEvent("unlockFlowEnd", sendData);
                    }

                    @Override
                    public void showCustomAd(@NonNull DJXDrama djxDrama, @NonNull CustomAdCallback callback) {
                        customAdCallback = callback;
                        sendEvent("showCustomAd", FADSDramaUtils.toMap(djxDrama));
                    }
                })
                .listener(new IDJXDramaListener() {
                    @Override
                    public void onDJXSeekTo(int index, long duration) {
                        super.onDJXSeekTo(index, duration);
                        sendEvent("onSeekTo", duration);
                    }

                    @Override
                    public void onDJXPageChange(int i, Map<String, Object> map) {
                        super.onDJXPageChange(i, map);
                        sendDramaEvent("onDJXPageChange", map);
                    }

                    @Override
                    public void onDJXVideoPlay(Map<String, Object> map) {
                        super.onDJXVideoPlay(map);
                        sendDramaEvent("onDJXVideoPlay", map);
                    }

                    @Override
                    public void onDJXVideoPause(Map<String, Object> map) {
                        super.onDJXVideoPause(map);
                        sendDramaEvent("onDJXVideoPause", map);
                    }

                    @Override
                    public void onDJXVideoContinue(Map<String, Object> map) {
                        super.onDJXVideoContinue(map);
                        sendDramaEvent("onDJXVideoContinue", map);
                    }

                    @Override
                    public void onDJXVideoCompletion(Map<String, Object> map) {
                        super.onDJXVideoCompletion(map);
                        sendDramaEvent("onDJXVideoCompletion", map);
                    }

                    @Override
                    public void onDJXVideoOver(Map<String, Object> map) {
                        super.onDJXVideoOver(map);
                        sendDramaEvent("onDJXVideoOver", map);
                    }

                    @Override
                    public void onDJXClose() {
                        super.onDJXClose();
                        sendEvent("onVideoClose", null);
                    }

                    @Override
                    public void onDJXRequestStart(@Nullable Map<String, Object> map) {
                        super.onDJXRequestStart(map);
                    }

                    @Override
                    public void onDJXRequestFail(int i, String s, @Nullable Map<String, Object> map) {
                        super.onDJXRequestFail(i, s, map);
                        Log.w(TAG, "onDJXRequestFail code:" + i + " msg:" + s);
                        sendEvent("onError", FADSDramaUtils.toErrMap(i, s));
                    }

                    @Override
                    public void onDJXRequestSuccess(List<Map<String, Object>> list) {
                        super.onDJXRequestSuccess(list);
                    }

                    @Override
                    public void onDramaSwitch(@Nullable Map<String, Object> map) {
                        super.onDramaSwitch(map);
                    }

                    @Override
                    public void onDramaGalleryShow(@Nullable Map<String, Object> map) {
                        super.onDramaGalleryShow(map);
                    }

                    @Override
                    public void onDramaGalleryClick(@Nullable Map<String, Object> map) {
                        super.onDramaGalleryClick(map);
                    }

                    @Override
                    public void onRewardDialogShow(@Nullable Map<String, Object> map) {
                        super.onRewardDialogShow(map);
                    }

                    @Override
                    public void onUnlockDialogAction(String s, @Nullable Map<String, Object> map) {
                        super.onUnlockDialogAction(s, map);
                    }

                    @Override
                    public void onDurationChange(long duration) {
                        super.onDurationChange(duration);
                        sendEvent("onDurationChange", duration);
                    }

                    @Override
                    public View createCustomView(ViewGroup viewGroup, @Nullable Map<String, Object> map) {
                        Context context = viewGroup.getContext();
                        DramaInfoView dramaInfoView = new  DramaInfoView(context, 62, false,bCollectDrama);
                        int viewId = View.generateViewId();
                        dramaInfoView.setId(viewId);
                        viewCache.put(viewId, dramaInfoView);
                        // 2. 从参数 map 中获取需要设置的数据（假设 map 来自 Flutter）
                        String coverUrl = map != null && map.containsKey("cover_image") ? map.get("cover_image").toString() : "";
                        String title = map != null && map.containsKey("title") ? map.get("title").toString() : "默认标题";
                        String desc = map != null && map.containsKey("desc") ? map.get("desc").toString() : "默认简介";

                        int nTotal = getIntFromMap(map,"total",1);
                        int nIndex = getIntFromMap(map,"index",1);
                        String jvJiDesc = String.format("第%d集 - 共%d集", nIndex, nTotal);

                        // 标签可能是 List<String> 类型
                        String tags = map != null && map.containsKey("type") ? map.get("type").toString() : "";
                        // 3. 设置数据
                        dramaInfoView.setData(coverUrl, title, new String[]{tags}, desc, jvJiDesc);

                        // 4. 设置点击事件（可选）
                        dramaInfoView.setOnPosterClickListener(v -> {
                            sendDramaEvent("onOpenSelfDetail", map);
                        });

                        dramaInfoView.setOnTitleClickListener(v -> {
                            sendDramaEvent("onOpenSelfDetail", map);
                        });

                        dramaInfoView.setOnEpisodeClickListener(v -> {
                            sendDramaEvent("onOpenDetail", map);
                        });

                        dramaInfoView.setOnCollectClickListener( v -> {
                            boolean bSetFav = !dramaInfoView.isFavorite();
                            Map<String, Object> mapParam = new HashMap<>();
                            mapParam.put("isFav", bSetFav);
                            mapParam.put("viewId", dramaInfoView.getId());
                            sendEvent("dramaFav",mapParam);
                        });

                        return dramaInfoView;
                    }
                })
                .adListener(adListener);
    }


    public static DJXDramaDetailConfig createDramaFlowDetailConfig() {
        int detailFree = -1;
        int unlockCount = 1;
        try {
            detailFree = (Integer) detailCallThreat.argument("detailFree");
            unlockCount = (Integer) detailCallThreat.argument("unlockCount");
        } catch (Exception ex) {
            Log.w("FlutterAdcontentPlugin", "createDramaDetailConfig detailFree is null");
        }
        // 是否隐藏默认激励弹窗
        boolean hideRewardDialog = detailCallThreat.argument("hideRewardDialog");
        // 解锁广告是否为广告 SDK
        int unlockAdMode = detailCallThreat.argument("unlockAdMode");
        DJXDramaUnlockAdMode unlockMode = unlockAdMode == 0 ? DJXDramaUnlockAdMode.MODE_COMMON : DJXDramaUnlockAdMode.MODE_SPECIFIC;
        // 第二个参数是短剧详情免费集数，默认-1，会使用sdk的默认设置
        IDJXAdListener adListener = AdEventUtils.getIDJXAdListener("ConfigUtils");
        // 每次广告解锁的集数
        int finalUnlockCount = unlockCount;
        bCollectThreat = Boolean.TRUE.equals(detailCallThreat.argument("bFav"));
        ///描述
        return DJXDramaDetailConfig.obtain(unlockMode, detailFree, new IDJXDramaUnlockListener() {
                    @Override
                    public void unlockFlowStart(@NonNull DJXDrama drama, @NonNull UnlockCallback callback, @Nullable Map<String, ?> map) {
                        unlockCallback = callback;
                        sendEvent("unlockFlowStart", FADSDramaUtils.toMap(drama));
                        if (!hideRewardDialog) {
                            callback.onConfirm(genUnlockInfo(drama.id, finalUnlockCount, false));
                        }
                    }

                    @Override
                    public void unlockFlowEnd(@NonNull DJXDrama drama, @Nullable UnlockErrorStatus unlockErrorStatus, @Nullable Map<String, ?> map) {
                        // 发送解锁结果
                        HashMap<String, Object> sendData;
                        if (unlockErrorStatus == null) {
                            sendData = FADSDramaUtils.toErrMap(200, "正常解锁");
                        } else {
                            sendData = FADSDramaUtils.toErrMap(unlockErrorStatus.ordinal(), unlockErrorStatus.name());
                        }
                        sendEvent("unlockFlowEnd", sendData);
                    }

                    @Override
                    public void showCustomAd(@NonNull DJXDrama djxDrama, @NonNull CustomAdCallback callback) {
                        customAdCallback = callback;
                        sendEvent("showCustomAd", FADSDramaUtils.toMap(djxDrama));
                    }
                }).listener(new IDJXDramaListener() {
                    @Override
                    public View createCustomView(ViewGroup viewGroup, @Nullable Map<String, Object> map) {

                        Context context = viewGroup.getContext();;
                        DramaInfoView dramaInfoView = new DramaInfoView(context, 52, true,bCollectThreat);
                        // 2. 从参数 map 中获取需要设置的数据（假设 map 来自 Flutter）
                        String coverUrl = map != null && map.containsKey("cover_image") ? map.get("cover_image").toString() : "";
                        String title = map != null && map.containsKey("title") ? map.get("title").toString() : "默认标题";
                        String desc = map != null && map.containsKey("desc") ? map.get("desc").toString() : "默认简介";

                        int nTotal = getIntFromMap(map,"total",1);
                        int nIndex = getIntFromMap(map,"index",1);
                        String jvJiDesc = String.format("第%d集 - 共%d集", nIndex, nTotal);

                        // 标签可能是 List<String> 类型
                        String[] tags = new String[]{"标签1", "标签2"};
                        if (map != null && map.containsKey("tags") && map.get("tags") instanceof List) {
                            List<?> tagList = (List<?>) map.get("tags");
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                tags = tagList.stream().map(Object::toString).toArray(String[]::new);
                            }
                        }

                        // 3. 设置数据
                        dramaInfoView.setData(coverUrl, title, tags, desc, jvJiDesc);

                        // 4. 设置点击事件（可选）
                        dramaInfoView.setOnPosterClickListener(v -> {
                            sendDramaEvent("onOpenSelfDetail",map);
                            // TODO: 可以发送 Flutter 通知或跳转
                        });

                        dramaInfoView.setOnTitleClickListener(v -> {
                            sendDramaEvent("onOpenSelfDetail",map);
                        });

                        dramaInfoView.setOnEpisodeClickListener(v -> {
                            sendDramaEvent("onOpenDetail",map);
                        });

                        dramaInfoView.setOnCollectClickListener( v -> {
//                            favoriteButton.setImageResource(isFavorite ? R.drawable.ic_favorite_selected : R.drawable.ic_favorite_unselected);
                            Map<String, Object> mapParam = new HashMap<>();
                            mapParam.put("isFav", true);
                            ConfigUtils.sendEvent("dramaFav", mapParam);
                        });

                        return dramaInfoView;
                    }
                });
//                .adListener(adListener);
    }

    /**
     * 获取解锁信息
     */
    public static DJXDramaUnlockInfo genUnlockInfo(long dramaId, int lockSet, boolean cancelUnlock) {
        return new DJXDramaUnlockInfo(dramaId, lockSet, DJXDramaUnlockMethod.METHOD_AD, false, "unlockId", cancelUnlock, DJXUnlockModeType.UNLOCKTYPE_DEFAULT);
    }

    /**
     * 设置解锁状态
     *
     * @param call 参数
     */
    public static boolean setUnlock(MethodCall call) {
        int id = (Integer) call.argument("id");
        int lockSet = (int) call.argument("lockSet");
        boolean cancel = (boolean) call.argument("cancel");
        DJXDramaUnlockInfo unlockInfo = genUnlockInfo(id, lockSet, cancel);
        if (unlockCallback != null) {
            unlockCallback.onConfirm(unlockInfo);
        }
        return cancel;
    }

    /**
     * 设置自定义广告价格信息
     */
    public static void setCustomAdOnShow(MethodCall call) {
        String cpm = (String) call.arguments;
        if (customAdCallback != null && cpm != null) {
            customAdCallback.onShow(cpm);
        }
    }

    /**
     * 设置自定义广告解锁情况
     */
    public static void setCustomAdOnReward(MethodCall call) {
        boolean verify = (Boolean) call.argument("verify");
        Map<String, Object> extraData = call.argument("extraData");
        if (customAdCallback != null) {
            customAdCallback.onRewardVerify(new DJXRewardAdResult(verify, extraData));
        }
    }


    /**
     * 发送通道消息
     */
    public static void sendEvent(String method, Object data) {
        if (detailChannel != null) {
            detailChannel.invokeMethod(method, data);
        }
    }

    /**
     * 发送短剧事件
     */
    public static void sendDramaEvent(String method, Map<String, Object> map) {
        sendEvent(method, FADSDramaUtils.toMap(map));
    }


    /**
     * 发送通道消息
     */
    public static void sendFeedEvent(String method, Object data) {
        if (detailChannelThreat != null) {
            detailChannelThreat.invokeMethod(method, data);
        }
    }

    /**
     * 发送短剧事件
     */
    public static void sendDramaFeedEvent(String method, Map<String, Object> map) {
        sendFeedEvent(method, FADSDramaUtils.toMap(map));
    }

    /**
     * 发送视频通道消息
     */
    public static void sendVideoEvent(String method, Object data) {
        if (videoChannel != null) {
            videoChannel.invokeMethod(method, data);
        }
    }

    /**
     * 发送视频通道消息
     */
    public static void sendNovelEvent(String method, Object data) {
        if (novelChannel != null) {
            novelChannel.invokeMethod(method, data);
        }
    }

    /**
     * 发送短故事通道消息
     */
    public static void sendNovelEventMap(String method, Map<String, ?> map) {
        if (novelChannel != null && map != null) {
            NovStory storyInfo= (NovStory) map.get("storyInfo");
            sendNovelEvent(method,FADSNovelStoryUtils.toMap(storyInfo));
        }
    }

    /**
     * 获取小视频配置
     */
    public static IDPWidget getVideoWidget() {
        if (videoCall == null) {
            return null;
        }
        // 解析参数
        int channelType = videoCall.argument("channelType");
        int contentType = videoCall.argument("contentType");
        int style = videoCall.argument("style");
        int bottomOffset = videoCall.argument("bottomOffset");
        int titleTopMargin = videoCall.argument("titleTopMargin");
        boolean hideChannelName = videoCall.argument("hideChannelName");
        boolean hideClose = videoCall.argument("hideClose");
        boolean hideFollow = videoCall.argument("hideFollow");
        boolean showGuide = videoCall.argument("showGuide");
        boolean enableRefresh = videoCall.argument("enableRefresh");
        // 获取广告监听
        IDPAdListener adListener = AdEventUtils.getIDPAdListener(TAG);
        // 获取视频监听
        IDPDrawListener videoListener = getDPDrawListener();
        // 构建小视频
        IDPWidget mWidget;
        if (style == 3 || style == 4) {
            // 创建 Grid 配置
            DPWidgetGridParams widgetDrawParams = DPWidgetGridParams.obtain()
                    .cardStyle(DPWidgetGridParams.CARD_STAGGERED_STYLE)
                    .enableRefresh(enableRefresh)
                    .adListener(adListener)
                    .listener(new IDPGridListener() {
                        @Override
                        public void onDPRefreshFinish() {
                            super.onDPRefreshFinish();
                        }

                        @Override
                        public void onDPGridItemClick(Map<String, Object> map) {
                            super.onDPGridItemClick(map);
                            Log.d(TAG, "onDPGridItemClick msg:" + map.toString());
                        }

                        @Override
                        public void onDPClientShow(@Nullable Map<String, Object> map) {
                            super.onDPClientShow(map);
                        }
                    });
            mWidget = (style == 3) ? DPSdk.factory().createGrid(widgetDrawParams) : DPSdk.factory().createDoubleFeed(widgetDrawParams);
        } else {
            // 创建小视频配置
            DPWidgetDrawParams widgetDrawParams = DPWidgetDrawParams.obtain()
                    .drawChannelType(channelType) // 频道类型
                    .drawContentType(contentType) // 内容类型
                    .hideClose(hideClose, null) // 是否隐藏关闭按钮
                    .hideChannelName(hideChannelName) // 是否隐藏频道名称
                    .hideFollow(hideFollow) // 是否隐藏关注按钮
                    .bottomOffset(bottomOffset) // 底部偏移量
                    .titleTopMargin(titleTopMargin)
                    .showGuide(showGuide) // 是否显示引导
                    .enableRefresh(enableRefresh) // 是否开启刷新
                    .adListener(adListener)// 广告监听
                    .listener(videoListener); // 事件监听
            mWidget = DPSdk.factory().createDraw(widgetDrawParams);
        }
        return mWidget;
    }

    /**
     * 获取小视频监听
     *
     * @return 监听器
     */
    private static IDPDrawListener getDPDrawListener() {
        return new IDPDrawListener() {
            @Override
            public void onDPRefreshFinish() {
                super.onDPRefreshFinish();
            }

            @Override
            public void onDPSeekTo(int index, long duration) {
                super.onDPSeekTo(index, duration);
                sendVideoEvent("onSeekTo", duration);
            }

            @Override
            public void onDPPageChange(int i, Map<String, Object> map) {
                super.onDPPageChange(i, map);
                sendVideoEvent("onDPPageChange", i);
            }

            @Override
            public void onDPVideoPlay(Map<String, Object> map) {
                super.onDPVideoPlay(map);
                sendVideoEvent("onDPVideoPlay", null);
            }

            @Override
            public void onDPVideoPause(Map<String, Object> map) {
                super.onDPVideoPause(map);
                sendVideoEvent("onDPVideoPause", null);
            }

            @Override
            public void onDPVideoContinue(Map<String, Object> map) {
                super.onDPVideoContinue(map);
                sendVideoEvent("onDPVideoContinue", null);
            }

            @Override
            public void onDPVideoCompletion(Map<String, Object> map) {
                super.onDPVideoCompletion(map);
                sendVideoEvent("onDPVideoCompletion", null);
            }

            @Override
            public void onDPVideoOver(Map<String, Object> map) {
                super.onDPVideoOver(map);
                sendVideoEvent("onDPVideoOver", null);
            }

            @Override
            public void onDPClose() {
                super.onDPClose();
                sendVideoEvent("onVideoClose", null);
            }

            @Override
            public void onDurationChange(long duration) {
                super.onDurationChange(duration);
                sendEvent("onDurationChange", duration);
            }
        };
    }

    /**
     * 获取短故事阅读页面配置
     */
    public static NovReaderConfig getNovReaderConfig() {
        if (novelCall == null) {
            Log.e(TAG, "getNovReaderConfig: novelCall is null");
            return null;
        }
        // 阅读页配置
        // 激励广告模式
        int rewardAdMode = novelCall.argument("rewardAdMode");
        // 激励视频广告位
        String rewardCodeId = novelCall.argument("rewardCodeId");
        // 翻页模式
        int pageTurnMode = novelCall.argument("pageTurnMode");
        // 默认文字大小
        int defaultTextSize = novelCall.argument("defaultTextSize");
        // 文末客片样式
        int endPageCardStyle = novelCall.argument("endPageCardStyle");
        // 文末推荐页个数
        int endPageRecSize = novelCall.argument("endPageRecSize");
        // 获取广告监听
        IDJXAdListener adListener = AdEventUtils.getIDJXAdListener("ConfigUtils");
        // 创建配置
        NovReaderConfig config = new NovReaderConfig();
        config.setRewardAdMode(NovReaderConfig.NovRewardAdMode.values()[rewardAdMode]);
        config.setDefaultPageTurnMode(NovReaderConfig.NovPageTurnMode.values()[pageTurnMode]);
        config.setDefaultTextSize(defaultTextSize);
        config.setEndPageCardStyle(NovReaderConfig.NovEndPageCardStyle.values()[endPageCardStyle]);
        config.setEndPageRecSize(endPageRecSize);
        // 自定义代码位 id
        if (!TextUtils.isEmpty(rewardCodeId)) {
            config.setRewardCodeId(rewardCodeId);
        }
        config.setAdListener(adListener);
        config.setUnlockListener(new INovUnlockListener() {
            @Override
            public void onUnlockStart(@NonNull NovStory novStory, @NonNull NovPage novPage) {
                sendNovelEvent("unlockFlowStart", FADSNovelStoryUtils.toMap(novStory));
            }

            @Override
            public void onUnlockEnd(boolean b, @Nullable UnlockErrorStatus unlockErrorStatus, @Nullable NovStory novStory, @Nullable NovPage novPage) {
                // 发送解锁结果
                HashMap<String, Object> sendData;
                if (unlockErrorStatus == null) {
                    sendData = FADSDramaUtils.toErrMap(200, "正常解锁");
                } else {
                    sendData = FADSDramaUtils.toErrMap(unlockErrorStatus.ordinal(), unlockErrorStatus.name());
                }
                sendNovelEvent("unlockFlowEnd", sendData);
            }

            @Override
            public void onShowCustomAd(@NonNull CustomAdCallback customAdCallback) {
                novelCustomAdCallback = customAdCallback;
                sendNovelEvent("showCustomAd", null);
            }
        });

        config.setReaderListener(new INovReaderListener() {
            @Override
            public void onEnter(@NonNull Map<String, ?> map) {
                Log.d(TAG, "getNovReaderConfig: onEnter ");
                sendNovelEventMap("NovOnEnter",map);
            }

            @Override
            public void onExit(@NonNull Map<String, ?> map) {
                Log.d(TAG, "getNovReaderConfig: onExit");
                sendNovelEventMap("NovOnExit",map);
            }

            @Override
            public void onPageSelected(@NonNull Map<String, ?> map) {
                Log.d(TAG, "getNovReaderConfig: onPageSelected");
                sendNovelEventMap("NovOnPageSelected",map);
            }

            @Nullable
            @Override
            public Pair<Boolean, String> onChapterSelected(@NonNull Map<String, ?> map) {
                Log.d(TAG, "getNovReaderConfig: onChapterSelected");
                return null;
            }

            @Override
            public void onBookEnd(@NonNull Map<String, ?> map) {
                Log.d(TAG, "getNovReaderConfig: onBookEnd");
                sendNovelEventMap("NovOnBookEnd",map);
            }

            @Override
            public boolean isDelegateBackPressed() {
                Log.d(TAG, "getNovReaderConfig: isDelegateBackPressed");
                return false;
            }

            @Override
            public void onBackPressed(@NonNull Activity activity, @NonNull NovStory novStory, @NonNull INovBackCallback iNovBackCallback) {
                Log.d(TAG, "getNovReaderConfig: onBackPressed");
            }
        });


        return config;
    }

    /**
     * 方法调用
     *
     * @param call   参数
     * @param result 结果
     */
    public static void novelMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        String method = call.method;
        Log.d(TAG, "onMethodCall method:" + method + " arguments:" + call.arguments);
        if ("setCustomAdOnShow".equals(method)){
            // 自定义广告解锁，展示时设置价格
            setNovelCustomAdOnShow(call);
        }else if ("setCustomAdOnReward".equals(method)){
            // 自定义广告解锁，奖励
            setNovelCustomAdOnReward(call);
        }
        result.success(true);
    }

    /**
     * 设置小说自定义广告展示价格
     */
    public static void setNovelCustomAdOnShow(MethodCall call) {
        String cpm = (String) call.arguments;
        if (novelCustomAdCallback != null && cpm != null) {
            novelCustomAdCallback.onShow(cpm);
        }
    }

    /**
     * 设置小说自定义广告解锁结果
     */
    public static void setNovelCustomAdOnReward(MethodCall call) {
        boolean verify = (Boolean) call.argument("verify");
        Map<String, Object> extraData = call.argument("extraData");
        if (novelCustomAdCallback != null) {
            novelCustomAdCallback.onRewardVerify(new NovRewardAdResult(verify, extraData));
        }
    }


    /**
     * 获取 FragmentActivity
     *
     * @param context 上下文
     * @return FragmentActivity
     */
    public static FragmentActivity getActivity(Context context) {
        Context currentContext = context;
        while (currentContext instanceof ContextWrapper) {
            if (currentContext instanceof FragmentActivity) {
                return (FragmentActivity) currentContext;
            }
            currentContext = ((ContextWrapper) currentContext).getBaseContext();
        }
        return null;
    }
}
