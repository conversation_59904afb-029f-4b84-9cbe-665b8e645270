package com.zero.flutter_adcontent.page;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

import com.bytedance.sdk.djx.DJXSdk;
import com.bytedance.sdk.djx.IDJXWidget;
import com.bytedance.sdk.djx.model.DJXDramaDetailConfig;
import com.bytedance.sdk.djx.params.DJXWidgetDramaDetailParams;
import com.zero.flutter_adcontent.R;

public class DetailActivity extends AppCompatActivity {
    private static final String TAG = DetailActivity.class.getSimpleName();
    private IDJXWidget mWidget;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_detail);
        initView();
        initData();
    }

    protected void initView() {

    }

    protected void initData() {
        // 解析参数
        Intent intent = getIntent();
        long dramaId = intent.getIntExtra("id",0);
        int dramaIndex = intent.getIntExtra("index",0);
        String groupId = intent.getStringExtra("groupId");
        // 创建剧场配置
        DJXDramaDetailConfig detailConfig=ConfigUtils.getDetailConfig();
        if (detailConfig==null){
            Log.e(TAG, "getDetailConfig is null");
            return;
        }
        // 创建短剧详情
        DJXWidgetDramaDetailParams djxWidgetDramaHomeParams = DJXWidgetDramaDetailParams.obtain(dramaId, dramaIndex, detailConfig)
                .currentDuration(0)
                .fromGid(groupId) // 必传，否则影响推荐效果
                .from(DJXWidgetDramaDetailParams.DJXDramaEnterFrom.DEFAULT);
        mWidget = DJXSdk.factory().createDramaDetail(djxWidgetDramaHomeParams);
        // 更新 View
        getSupportFragmentManager().beginTransaction().replace(R.id.fl_detail_container, mWidget.getFragment()).commit();
    }
}