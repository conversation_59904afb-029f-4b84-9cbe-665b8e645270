package com.zero.flutter_adcontent.page;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.*;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.zero.flutter_adcontent.R;

import java.util.HashMap;
import java.util.Map;

public class DramaInfoView extends FrameLayout {

    private ImageView poster;
    private TextView title;
    private  int sourceId;

    private RelativeLayout titleLayout;
    private LinearLayout tagsLayout;
    private TextView description;
    private TextView episodeInfo;

    private static DramaInfoView cachedInstance = null;

    public ImageView favoriteButton;
    private boolean isFavorite = false;
    private  int bottomPadding;

    public DramaInfoView(Context context, int bottomPadding, boolean isThreat,boolean bIsFavState) {
        super(context);
        this.bottomPadding = bottomPadding;
        this.isFavorite = bIsFavState;
        init(context, isThreat);
    }
    public DramaInfoView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context,false);
    }

    public DramaInfoView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,int bottomPadding) {
        super(context, attrs, defStyleAttr);
        this.bottomPadding = bottomPadding;
        init(context,false);
    }

    public void setFavoriteState(boolean isFav) {
        this.isFavorite = isFav;
        if (favoriteButton != null) {
            favoriteButton.setImageResource(isFavorite ? R.drawable.ic_favorite_selected : R.drawable.ic_favorite_unselected
            );
        }
    }

    public void applyFavoriteDrawable(boolean bFav) {
        for (Map.Entry<Integer, DramaInfoView> entry : ConfigUtils.viewCache.entrySet()) {
            DramaInfoView view = entry.getValue();
            if (view != null) {
                view.setFavoriteState(bFav); // 或 false，取决于你的逻辑
            }
        }
    }

    @Override
    protected void onWindowVisibilityChanged(int visibility) {
        super.onWindowVisibilityChanged(visibility);
        if (visibility == VISIBLE) {
            applyFavoriteDrawable(isFavorite);
        }
    }


    // 如果你还需要获取当前状态
    public boolean isFavorite() {
        return isFavorite;
    }


    private void init(Context context, boolean isThreat) {
        setLayoutParams(new FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));

        // 内容整体容器（垂直）
        LinearLayout verticalLayout = new LinearLayout(context);
        verticalLayout.setOrientation(LinearLayout.VERTICAL);
        FrameLayout.LayoutParams verticalParams = new FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        verticalParams.gravity = Gravity.BOTTOM;
        verticalLayout.setPadding(dpToPx(16), dpToPx(16), dpToPx(16), dpToPx(bottomPadding));
        addView(verticalLayout, verticalParams); // 添加内容容器

        // 收藏按钮（始终右下角）
        favoriteButton = new ImageView(context);
        favoriteButton.setScaleType(ImageView.ScaleType.FIT_CENTER);
//        favoriteButton.setImageResource(R.drawable.ic_favorite_unselected); // 初始图标
//        favoriteButton.setBackgroundColor(Color.RED); // 调试用

        int buttonSize = dpToPx(36);
        int bottomMargin = dpToPx(250);
        int rightMargin = dpToPx(14);

        FrameLayout.LayoutParams favoriteParams = new FrameLayout.LayoutParams(buttonSize, buttonSize);
        favoriteParams.gravity = Gravity.BOTTOM | Gravity.END;
        favoriteParams.setMargins(0, 0, rightMargin, bottomMargin);

        if (!isThreat) {
            addView(favoriteButton, favoriteParams);  // ✅ 添加到最外层 FrameLayout
        }

        // ================== 以下是内容布局 ==================

        // 第一行（左图右文字）
        LinearLayout firstRow = new LinearLayout(context);
        firstRow.setOrientation(LinearLayout.HORIZONTAL);
        firstRow.setGravity(Gravity.CENTER_VERTICAL);
        verticalLayout.addView(firstRow, new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, dpToPx(60)));

        // 左侧海报
        CardView cardView = new CardView(context);
        cardView.setRadius(dpToPx(8));
        cardView.setCardElevation(0f);
        cardView.setUseCompatPadding(false);
        cardView.setPreventCornerOverlap(true);
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(dpToPx(44), dpToPx(60));
        cardParams.rightMargin = dpToPx(12);
        firstRow.addView(cardView, cardParams);

        poster = new ImageView(context);
        poster.setScaleType(ImageView.ScaleType.CENTER_CROP);
        cardView.addView(poster);

        // 右侧文字列
        LinearLayout textColumn = new LinearLayout(context);
        textColumn.setOrientation(LinearLayout.VERTICAL);
        textColumn.setGravity(Gravity.CENTER_VERTICAL);
        firstRow.addView(textColumn, new LinearLayout.LayoutParams(0, LayoutParams.MATCH_PARENT, 1f));

        // 标题 + 箭头
        titleLayout = new RelativeLayout(context);
        textColumn.addView(titleLayout, new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));

        title = new TextView(context);
        title.setId(View.generateViewId());
        title.setTextColor(Color.WHITE);
        title.setTextSize(14);
        title.setTypeface(null, Typeface.BOLD);
        title.setMaxWidth(dpToPx(200));
        title.setSingleLine(true);
        title.setEllipsize(TextUtils.TruncateAt.END);
        RelativeLayout.LayoutParams titleParams = new RelativeLayout.LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        titleParams.addRule(RelativeLayout.CENTER_VERTICAL);
        titleLayout.addView(title, titleParams);

        ImageView arrow = new ImageView(context);
        arrow.setImageResource(R.drawable.nav_right_white);
        arrow.setScaleType(ImageView.ScaleType.FIT_CENTER);
        RelativeLayout.LayoutParams arrowParams = new RelativeLayout.LayoutParams(dpToPx(16), dpToPx(16));
        arrowParams.addRule(RelativeLayout.RIGHT_OF, title.getId());
        arrowParams.addRule(RelativeLayout.ALIGN_BOTTOM, title.getId());
        arrowParams.leftMargin = dpToPx(4);
        titleLayout.addView(arrow, arrowParams);

        // 标签容器
        tagsLayout = new LinearLayout(context);
        tagsLayout.setOrientation(LinearLayout.HORIZONTAL);
        tagsLayout.setPadding(0, dpToPx(4), 0, 0);
        textColumn.addView(tagsLayout);

        // 简介（第二行）
        description = new TextView(context);
        description.setTextColor(Color.WHITE);
        description.setTextSize(12);
        description.setMaxLines(2);
        description.setEllipsize(TextUtils.TruncateAt.END);
        description.setLineSpacing(0, 1.2f);
        description.setGravity(Gravity.CENTER_VERTICAL);
        LinearLayout.LayoutParams descParams = new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, dpToPx(46));
        descParams.rightMargin = dpToPx(64); // 留出右边按钮空间
        verticalLayout.addView(description, descParams);

        // 剧集信息（第三行）
        episodeInfo = new TextView(context);
        episodeInfo.setTextColor(Color.WHITE);
        episodeInfo.setTextSize(14);
        episodeInfo.setGravity(Gravity.CENTER_VERTICAL);
        episodeInfo.setPadding(dpToPx(16), 0, dpToPx(16), 0);
        GradientDrawable bg = new GradientDrawable();
        bg.setColor(Color.parseColor("#33ffffff"));
        bg.setCornerRadius(dpToPx(8));
        episodeInfo.setBackground(bg);
        verticalLayout.addView(episodeInfo, new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, dpToPx(40)));
    }



    public void setData(String coverUrl, String titleText, String[] tags, String descText, String episodeText) {
        // 加载封面
        Glide.with(getContext())
                .load(coverUrl)
                .placeholder(R.drawable.poster_placeholder)
                .error(R.drawable.poster_placeholder)
                .centerCrop()
                .listener(new RequestListener<Drawable>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                        Log.e("Glide", "Load failed", e);
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target,
                                                   DataSource dataSource, boolean isFirstResource) {
                        Log.d("Glide", "Load success");
                        return false;
                    }
                })
                .into(poster);

        // 设置标题
        title.setText(titleText);

        // 设置标签
        tagsLayout.removeAllViews();
        for (String tag : tags) {
            TextView tagView = new TextView(getContext());
            tagView.setText(tag);
            tagView.setTextColor(Color.WHITE);
            tagView.setTextSize(9);
            tagView.setPadding(dpToPx(8), dpToPx(4), dpToPx(8), dpToPx(4));
            tagView.setBackground(createTagBackground());
            LinearLayout.LayoutParams tagParams = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            tagParams.rightMargin = dpToPx(8);
            tagsLayout.addView(tagView, tagParams);
        }

        // 设置简介
        description.setText(descText);

        // 设置剧集信息
        episodeInfo.setText(episodeText);
    }

    public void setOnPosterClickListener(OnClickListener listener) {
        poster.setOnClickListener(listener);
    }

    public void setOnTitleClickListener(OnClickListener listener) {
        titleLayout.setOnClickListener(listener);
    }

    public void setOnEpisodeClickListener(OnClickListener listener) {
        episodeInfo.setOnClickListener(listener);
    }

    public void setOnCollectClickListener(OnClickListener listener) {
      favoriteButton.setOnClickListener(listener);
    }

    private int dpToPx(float dp) {
        return (int) (dp * getResources().getDisplayMetrics().density + 0.5f);
    }

    private GradientDrawable createTagBackground() {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setCornerRadius(dpToPx(4));
        drawable.setColor(Color.parseColor("#19FFFFFF"));
        return drawable;
    }
}

