package com.zero.flutter_adcontent.page;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.zero.flutter_adcontent.PluginDelegate;

import java.util.Map;

import io.flutter.plugin.common.StandardMessageCodec;
import io.flutter.plugin.platform.PlatformView;
import io.flutter.plugin.platform.PlatformViewFactory;

/**
 * 原生平台 View 工厂
 */
public class NativeViewFactory extends PlatformViewFactory {
    private final String viewName;// View 名字
    private final PluginDelegate pluginDelegate; // 插件代理类

    public NativeViewFactory(String viewName, @NonNull PluginDelegate pluginDelegate) {
        super(StandardMessageCodec.INSTANCE);
        this.viewName = viewName;
        this.pluginDelegate = pluginDelegate;
    }

    @Override
    public PlatformView create(@NonNull Context context, int id, @Nullable Object args) {
        Map<String, Object> creationParams = (Map<String, Object>) args;
        if (creationParams == null) {
            return null;
        }
        if (this.viewName.equals(PluginDelegate.KEY_VIEW_DRAMA)) {
            return new DramaDetailView(context, id, creationParams, pluginDelegate);
        } else if (this.viewName.equals(PluginDelegate.KEY_VIEW_THEATER)) {
            return new TheaterView(context, id, creationParams, pluginDelegate);
        } else if (this.viewName.equals(PluginDelegate.KEY_VIEW_VIDEO)) {
            return new VideoView(context, id, creationParams, pluginDelegate);
        } else if (this.viewName.equals(PluginDelegate.KEY_VIEW_NOVEL_STORY)) {
            return new NovelStoryView(context, id, creationParams, pluginDelegate);
        }
        return null;
    }
}