package com.zero.flutter_adcontent.utils;

import android.util.Log;

import com.bytedance.sdk.djx.interfaces.listener.IDJXAdListener;
import com.bytedance.sdk.dp.IDPAdListener;
import com.zero.flutter_gromore_ads.event.AdErrorEvent;
import com.zero.flutter_gromore_ads.event.AdEvent;
import com.zero.flutter_gromore_ads.event.AdEventAction;
import com.zero.flutter_gromore_ads.event.AdEventHandler;
import com.zero.flutter_gromore_ads.event.AdRewardEvent;

import java.util.Map;

/// 广告事件工具类
public class AdEventUtils {

    /**
     * 获取短剧广告监听
     *
     * @param Tag 日志标签
     */
    public static IDJXAdListener getIDJXAdListener(String Tag) {
        return new IDJXAdListener() {
            @Override
            public void onDJXAdRequest(Map<String, Object> map) {
                super.onDJXAdRequest(map);
                Log.d(Tag, "onDJXAdRequest");
                sendEvent(map, AdEventAction.onAdLoaded);
            }

            @Override
            public void onDJXAdRequestSuccess(Map<String, Object> map) {
                super.onDJXAdRequestSuccess(map);
                Log.d(Tag, "onDJXAdRequestSuccess");
                sendEvent(map, AdEventAction.onAdPresent);
            }

            @Override
            public void onDJXAdRequestFail(int i, String s, Map<String, Object> map) {
                super.onDJXAdRequestFail(i, s, map);
                Log.d(Tag, "onDJXAdRequestFail errCode:" + i + " errMsg:" + s + " map:" + map.toString());
                sendErrorEvent(map, i, s);
            }

            @Override
            public void onDJXAdFillFail(Map<String, Object> map) {
                super.onDJXAdFillFail(map);
                Log.d(Tag, "onDJXAdFillFail map:" + map.toString());
                sendErrorEvent(map, -200, "渲染失败");
            }

            @Override
            public void onDJXAdShow(Map<String, Object> map) {
                super.onDJXAdShow(map);
                Log.d(Tag, "onDJXAdShow");
                sendEvent(map, AdEventAction.onAdExposure);
            }

            @Override
            public void onDJXAdPlayStart(Map<String, Object> map) {
                super.onDJXAdPlayStart(map);
                Log.d(Tag, "onDJXAdPlayStart");
            }

            @Override
            public void onDJXAdPlayPause(Map<String, Object> map) {
                super.onDJXAdPlayPause(map);
                Log.d(Tag, "onDJXAdPlayPause");
            }

            @Override
            public void onDJXAdPlayContinue(Map<String, Object> map) {
                super.onDJXAdPlayContinue(map);
                Log.d(Tag, "onDJXAdPlayContinue");
            }

            @Override
            public void onDJXAdPlayComplete(Map<String, Object> map) {
                super.onDJXAdPlayComplete(map);
                Log.d(Tag, "onDJXAdPlayComplete");
                sendEvent(map, AdEventAction.onAdComplete);
            }

            @Override
            public void onDJXAdClicked(Map<String, Object> map) {
                super.onDJXAdClicked(map);
                Log.d(Tag, "onDJXAdClicked");
                sendEvent(map, AdEventAction.onAdClicked);
            }

            @Override
            public void onRewardVerify(Map<String, Object> map) {
                super.onRewardVerify(map);
                Log.d(Tag, "onRewardVerify");
                sendRewardEvent(map);
            }

            @Override
            public void onSkippedVideo(Map<String, Object> map) {
                super.onSkippedVideo(map);
                Log.d(Tag, "onSkippedVideo");
                sendEvent(map, AdEventAction.onAdSkip);
            }
        };
    }


    /**
     * 获取小视频广告监听
     *
     * @param Tag 日志标签
     */
    public static IDPAdListener getIDPAdListener(String Tag) {
        return new IDPAdListener() {
            @Override
            public void onDPAdRequest(Map<String, Object> map) {
                super.onDPAdRequest(map);
                Log.d(Tag, "onDPAdRequest");
                sendEvent(map, AdEventAction.onAdLoaded);
            }

            @Override
            public void onDPAdRequestSuccess(Map<String, Object> map) {
                super.onDPAdRequestSuccess(map);
                Log.d(Tag, "onDPAdRequestSuccess");
                sendEvent(map, AdEventAction.onAdPresent);
            }

            @Override
            public void onDPAdRequestFail(int i, String s, Map<String, Object> map) {
                super.onDPAdRequestFail(i, s, map);
                Log.d(Tag, "onDPAdRequestFail errCode:" + i + " errMsg:" + s + " map:" + map.toString());
                sendErrorEvent(map, i, s);
            }

            @Override
            public void onDPAdFillFail(Map<String, Object> map) {
                super.onDPAdFillFail(map);
                Log.d(Tag, "onDPAdFillFail map:" + map.toString());
                sendErrorEvent(map, -200, "渲染失败");
            }

            @Override
            public void onDPAdShow(Map<String, Object> map) {
                super.onDPAdShow(map);
                Log.d(Tag, "onDPAdShow map:" + map.toString());
                sendEvent(map, AdEventAction.onAdExposure);
            }

            @Override
            public void onDPAdPlayStart(Map<String, Object> map) {
                super.onDPAdPlayStart(map);
                Log.d(Tag, "onDPAdPlayStart");
            }

            @Override
            public void onDPAdPlayPause(Map<String, Object> map) {
                super.onDPAdPlayPause(map);
                Log.d(Tag, "onDPAdPlayPause");
            }

            @Override
            public void onDPAdPlayContinue(Map<String, Object> map) {
                super.onDPAdPlayContinue(map);
                Log.d(Tag, "onDPAdPlayContinue");
            }

            @Override
            public void onDPAdPlayComplete(Map<String, Object> map) {
                super.onDPAdPlayComplete(map);
                Log.d(Tag, "onDPAdPlayComplete");
                sendEvent(map, AdEventAction.onAdComplete);
            }

            @Override
            public void onDPAdClicked(Map<String, Object> map) {
                super.onDPAdClicked(map);
                Log.d(Tag, "onDPAdClicked");
                sendEvent(map, AdEventAction.onAdClicked);
            }

            @Override
            public void onRewardVerify(Map<String, Object> map) {
                super.onRewardVerify(map);
                Log.d(Tag, "onRewardVerify map:" + map);
                sendRewardEvent(map);
            }

            @Override
            public void onSkippedVideo(Map<String, Object> map) {
                super.onSkippedVideo(map);
                Log.d(Tag, "onSkippedVideo");
                sendEvent(map, AdEventAction.onAdSkip);
            }
        };
    }


    /**
     * 发送广告事件
     *
     * @param event 广告事件
     */
    public static void sendEvent(AdEvent event) {
        AdEventHandler.getInstance().sendEvent(event);
    }

    /**
     * 发送广告事件
     *
     * @param map    参数
     * @param action 操作
     */
    public static void sendEvent(Map<String, Object> map, String action) {
        String adId = (String) map.get("ad_id");
        sendEvent(new AdEvent(adId, action));
    }

    /**
     * 发送错误事件
     *
     * @param map 参数
     */
    public static void sendErrorEvent(Map<String, Object> map, int errCode, String errMsg) {
        String adId = (String) map.get("ad_id");
        sendEvent(new AdErrorEvent(adId, errCode, errMsg));
    }

    /**
     * 发送奖励事件
     *
     * @param map 参数
     */
    public static void sendRewardEvent(Map<String, Object> map) {
        String adId = (String) map.get("ad_id");
        boolean rewardVerify = (Boolean) map.get("reward_verify");
        sendEvent(new AdRewardEvent(adId, 0, rewardVerify, 0, "不支持", 0, "none", "不支持", "不支持","aaaa",""));
    }
}
