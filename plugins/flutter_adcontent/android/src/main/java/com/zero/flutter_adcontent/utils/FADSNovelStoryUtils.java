package com.zero.flutter_adcontent.utils;

import com.bytedance.sdk.djx.model.DJXDrama;
import com.bytedance.sdk.djx.model.DJXEpisodeStatus;
import com.bytedance.sdk.nov.api.model.NovCategory;
import com.bytedance.sdk.nov.api.model.NovStory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 短故事解析类
public class FADSNovelStoryUtils {

    /**
     * 解析短故事列表
     *
     * @param novelStories 短故事列表
     * @return 短故事列表
     */
    static public List<HashMap<String, Object>> toList(List<? extends NovStory> novelStories) {
        List<HashMap<String, Object>> list = new ArrayList<>();
        for (NovStory novelStory : novelStories) {
            list.add(toMap(novelStory));
        }
        return list;
    }

    /**
     * 解析短故事类目列表
     *
     * @param novelCategories 短故事类目列表
     * @return 短故事类目列表
     */
    static public List<HashMap<String, Object>> toCategoryList(List<NovCategory> novelCategories) {
        if (novelCategories == null) {
            return null;
        }
        List<HashMap<String, Object>> list = new ArrayList<>();
        for (NovCategory category : novelCategories) {
            list.add(toMap(category));
        }
        return list;
    }

    /**
     * 解析短故事类目
     *
     * @param novelCategory 短故事类目
     * @return 短故事类目
     */
    static public HashMap<String, Object> toMap(NovCategory novelCategory) {
        return new HashMap<String, Object>() {
            {
                put("id", novelCategory.getId());
                put("name", novelCategory.getName());
                put("level", novelCategory.getLevel());
                put("children", toCategoryList(novelCategory.getChildren()));
            }
        };
    }

    /**
     * 解析短剧详情
     *
     * @param novelStory 短剧详情
     * @return 短剧详情
     */
    static public HashMap<String, Object> toMap(NovStory novelStory) {
        return new HashMap<String, Object>() {
            {
                put("id", novelStory.getId());
                put("title", novelStory.getTitle());
                put("desc", novelStory.getDesc());
                put("content", novelStory.getContent());
                put("author", novelStory.getAuthor());
                put("coverImage", novelStory.getCoverImage());
                put("imageType", novelStory.getImageType());
                put("categoryId", novelStory.getCategoryId());
                put("categoryName", novelStory.getCategoryName());
                put("total", novelStory.getTotal());
                put("createTime", novelStory.getCreateTime());
                put("index", novelStory.getIndex());
                put("progress", novelStory.getProgress());
                put("statsCount", novelStory.getStatsCount());
                put("isFavorite", novelStory.isFavorite());
                put("favoriteTime", novelStory.getFavoriteTime());
                put("actionTime", novelStory.getActionTime());
            }
        };
    }

    /**
     * 解析短故事详情
     *
     * @param novelStory 短故事详情
     * @return 短故事详情
     */
    static public NovStory fromMap(HashMap<String, Object> novelStory) {
        int id = (int) novelStory.get("id");
        String title = (String) novelStory.get("title");
        String desc = (String) novelStory.get("desc");
        String content = (String) novelStory.get("content");
        String author = (String) novelStory.get("author");
        String coverImage = (String) novelStory.get("coverImage");
        int imageType = (int) novelStory.get("imageType");
        int categoryId = (int) novelStory.get("categoryId");
        String categoryName = (String) novelStory.get("categoryName");
        int total = (int) novelStory.get("total");
        int createTime = (int) novelStory.get("createTime");
        int index = (int) novelStory.get("index");
        double progress = (double) novelStory.get("progress");
        int statsCount = (int) novelStory.get("statsCount");
        boolean isFavorite = (boolean) novelStory.get("isFavorite");
        String favoriteTime = (String) novelStory.get("favoriteTime");
        int actionTime = (int) novelStory.get("actionTime");
        NovStory novStory = new NovStory(id, title, desc, content, author, coverImage, imageType, categoryId, categoryName, total, createTime, index, (float) progress, statsCount, isFavorite, favoriteTime, actionTime);
        return novStory;
    }

    /**
     * 解析 map 中的短故事详情，保持一致
     *
     * @param novelStoryMap 短故事详情
     * @return 短故事详情
     */
    static public HashMap<String, Object> toMap(Map<String, Object> novelStoryMap) {
        return new HashMap<String, Object>() {
            {
                put("id", novelStoryMap.get("id"));
                put("title", novelStoryMap.get("title"));
                put("desc", novelStoryMap.get("desc"));
                put("content", novelStoryMap.get("content"));
                put("author", novelStoryMap.get("author"));
                put("coverImage", novelStoryMap.get("coverImage"));
                put("imageType", novelStoryMap.get("imageType"));
                put("categoryId", novelStoryMap.get("categoryId"));
                put("categoryName", novelStoryMap.get("categoryName"));
                put("total", novelStoryMap.get("total"));
                put("createTime", novelStoryMap.get("createTime"));
                put("index", novelStoryMap.get("index"));
                put("progress", novelStoryMap.get("progress"));
                put("statsCount", novelStoryMap.get("statsCount"));
                put("isFavorite", novelStoryMap.get("isFavorite"));
                put("favoriteTime", novelStoryMap.get("favoriteTime"));
                put("actionTime", novelStoryMap.get("actionTime"));
            }
        };
    }


    /**
     * 解析错误
     *
     * @param errCode 错误码
     * @param errMsg  错误信息
     * @return 错误
     */
    static public HashMap<String, Object> toErrMap(int errCode, String errMsg) {
        return new HashMap<String, Object>() {
            {
                put("errCode", errCode);
                put("errMsg", errMsg);
            }
        };
    }

}
