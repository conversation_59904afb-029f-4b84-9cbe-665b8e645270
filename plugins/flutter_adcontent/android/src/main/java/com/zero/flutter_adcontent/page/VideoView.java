package com.zero.flutter_adcontent.page;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.bytedance.sdk.dp.IDPWidget;
import com.zero.flutter_adcontent.PluginDelegate;
import com.zero.flutter_adcontent.R;

import java.util.Map;

import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

/**
 * 小视频 View
 */
public class VideoView implements PlatformView {
    private final String TAG = VideoView.class.getSimpleName();
    private final FrameLayout frameLayout;
    private IDPWidget mWidget;
    private final PluginDelegate pluginDelegate;
    private final int id;
    private MethodChannel methodChannel;


    VideoView(Context context, int id, Map<String, Object> creationParams, PluginDelegate pluginDelegate) {
        this.id = id;
        this.pluginDelegate = pluginDelegate;
        methodChannel = new MethodChannel(this.pluginDelegate.bind.getBinaryMessenger(), PluginDelegate.KEY_VIEW_VIDEO + "/" + id);
        methodChannel.setMethodCallHandler(this::onMethodCall);
        int style = (int) creationParams.get("style");
        // 判断 style
        if (style == 3) {
            frameLayout = (FrameLayout) LayoutInflater.from(context).inflate(R.layout.view_video_grid, null);
        } else if (style == 4) {
            frameLayout = (FrameLayout) LayoutInflater.from(context).inflate(R.layout.view_video_feed, null);
        } else {
            frameLayout = (FrameLayout) LayoutInflater.from(context).inflate(R.layout.view_video, null);
        }
        ConfigUtils.videoCall = new MethodCall("video_view", creationParams);
        ConfigUtils.videoChannel = methodChannel;
        mWidget = ConfigUtils.getVideoWidget();
        // 获取参数
        boolean autoPlay = (boolean) creationParams.get("autoPlay");
        // 获取 Activity
        FragmentActivity activity = ConfigUtils.getActivity(context);
        if (activity instanceof FlutterFragmentActivity) {
            // 获取 FragmentManager
            FragmentManager fragmentManager = activity.getSupportFragmentManager();
            FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
            // 更新 View
            if (style == 3) {
                fragmentTransaction.replace(R.id.fl_video_grid_container, mWidget.getFragment()).commitAllowingStateLoss();
            } else if (style == 4) {
                fragmentTransaction.replace(R.id.fl_video_feed_container, mWidget.getFragment()).commitAllowingStateLoss();
            } else {
                fragmentTransaction.replace(R.id.fl_view_video_container, mWidget.getFragment()).commitAllowingStateLoss();
            }
            // 设置不自动播放
            if (!autoPlay) {
                setUserVisibleHint(false);
            }
        } else {
            Log.e(TAG, "当前 Activity 不是 FlutterFragmentActivity 无法加载 View");
        }
    }

    @Override
    public View getView() {
        return frameLayout;
    }


    @Override
    public void dispose() {
        frameLayout.removeAllViews();
        if (mWidget != null) {
            mWidget.destroy();
        }
    }

    /**
     * 设置用户可见性，用于暂停和播放
     *
     * @param isVisibleToUser 是否可见
     */
    private void setUserVisibleHint(boolean isVisibleToUser) {
        if (mWidget != null) {
            mWidget.getFragment().setUserVisibleHint(isVisibleToUser);
        }
    }

    /**
     * 方法调用
     *
     * @param call   参数
     * @param result 结果
     */
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        String method = call.method;
        Log.d(TAG, "onMethodCall method:" + method + " arguments:" + call.arguments);
        if ("pause".equals(method)) {
            setUserVisibleHint(false);
        } else if ("resume".equals(method)) {
            setUserVisibleHint(true);
        }
        result.success(true);
    }


}
