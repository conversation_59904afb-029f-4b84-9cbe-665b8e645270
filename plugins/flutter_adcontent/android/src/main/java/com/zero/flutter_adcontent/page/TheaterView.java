package com.zero.flutter_adcontent.page;

import static com.zero.flutter_adcontent.page.ConfigUtils.getIntFromMap;

import android.content.Context;
import android.content.ContextWrapper;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Lifecycle;

import com.bytedance.sdk.djx.DJXPlaySpeedScope;
import com.bytedance.sdk.djx.DJXSdk;
import com.bytedance.sdk.djx.IDJXDramaDetailDelegate;
import com.bytedance.sdk.djx.IDJXWidget;
import com.bytedance.sdk.djx.interfaces.listener.IDJXDramaHomeListener;
import com.bytedance.sdk.djx.interfaces.listener.IDJXDramaListener;
import com.bytedance.sdk.djx.interfaces.listener.IDJXDrawListener;
import com.bytedance.sdk.djx.model.DJXDrama;
import com.bytedance.sdk.djx.model.DJXDramaDetailConfig;
import com.bytedance.sdk.djx.params.DJXWidgetDramaDetailParams;
import com.bytedance.sdk.djx.params.DJXWidgetDramaHomeParams;
import com.bytedance.sdk.djx.params.DJXWidgetDrawParams;
import com.zero.flutter_adcontent.PluginDelegate;
import com.zero.flutter_adcontent.R;
import com.zero.flutter_adcontent.utils.FADSDramaUtils;

import java.util.List;
import java.util.Map;

import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

/// 短剧 View
public class TheaterView implements PlatformView {
    private final String TAG = TheaterView.class.getSimpleName();
    private final FrameLayout frameLayout;
    private IDJXWidget mWidget;
    private final PluginDelegate pluginDelegate;
    private final int id;
    private MethodChannel methodChannel;

    FragmentManager fragmentManager;
    FragmentTransaction fragmentTransaction;


    TheaterView(Context context, int id, Map<String, Object> creationParams, PluginDelegate pluginDelegate) {
        Log.d(TAG, "init id:" + id);
        this.id = id;
        this.pluginDelegate = pluginDelegate;
        methodChannel = new MethodChannel(this.pluginDelegate.bind.getBinaryMessenger(), PluginDelegate.KEY_VIEW_THEATER + "/" + id);
        methodChannel.setMethodCallHandler(this::onMethodCall);
        frameLayout = (FrameLayout) LayoutInflater.from(context).inflate(R.layout.activity_teater, null);
        MethodCall call = new MethodCall("theater_view", creationParams);
        loadView(context, call);
    }

    @Override
    public View getView() {
        return frameLayout;
    }

    public void loadView(Context context, @NonNull MethodCall call) {
        Log.d(TAG, "loadView: ");
        FragmentActivity activity = getActivity(context);
        if (activity instanceof FlutterFragmentActivity) {
            // 获取 FragmentManager
            fragmentManager = activity.getSupportFragmentManager();
            fragmentTransaction = fragmentManager.beginTransaction();
            // 解析参数
            ConfigUtils.detailCallThreat = call;
            ConfigUtils.detailChannelThreat = methodChannel;

            Log.d("thread view","here" +call.arguments);
            // 创建剧场配置
            DJXDramaDetailConfig detailConfig = ConfigUtils.getDetailFeedConfig();
            if (detailConfig == null) {
                Log.e(TAG, "getDetailConfig is null");
                return;
            } else {
                Log.d("feedMap" ,"----" + detailConfig.toString());
            }
            // 获取参数
            int channelType = call.argument("channelType");
            boolean hideBack = detailConfig.isHideBack();
            boolean hideTopInfo = detailConfig.isHideTopInfo();
            int setTopOffset = detailConfig.getTopOffset();
            boolean hideBottomInfo = detailConfig.isHideBottomInfo();
            int setBottomOffset = detailConfig.getBottomOffset();
            boolean hideLikeButton = detailConfig.isHideLikeButton();
            boolean hideFavorButton = detailConfig.isHideFavorButton();
            boolean hideEnter = call.argument("hideEnter");
            boolean showChangeBtn = call.argument("showChangeBtn");

            if (channelType == DJXWidgetDrawParams.DRAW_CHANNEL_TYPE_THEATER) {
                // 创建剧场配置
                DJXWidgetDramaHomeParams dramaHomeParams = DJXWidgetDramaHomeParams.obtain(detailConfig);
                dramaHomeParams.mShowBackBtn = !hideBack;
                dramaHomeParams.mShowPageTitle = !hideTopInfo;
                dramaHomeParams.mShowChangeBtn = showChangeBtn;
                dramaHomeParams.setTopOffset(setTopOffset);
                dramaHomeParams.setEnterDelegate(getDramaDetailDelegate());
                mWidget = DJXSdk.factory().createDramaHome(dramaHomeParams);
            } else {
                // 创建滑滑溜配置
                DJXWidgetDrawParams drawParams = DJXWidgetDrawParams.obtain();
                drawParams.detailConfig(detailConfig);
                drawParams.drawChannelType(channelType);
                drawParams.drawContentType(DJXWidgetDrawParams.DRAW_CONTENT_TYPE_ONLY_DRAMA);
                drawParams.hideChannelName(hideTopInfo);
                drawParams.hideDramaInfo(hideBottomInfo);
                drawParams.hideDramaEnter(hideEnter);
                drawParams.hideClose(hideBack, null);
                drawParams.titleTopMargin(setTopOffset);
                drawParams.bottomOffset(setBottomOffset);
                drawParams.dramaFree(detailConfig.getFreeSet());
                drawParams.hideLikeButton(hideLikeButton);
                drawParams.hideFavorButton(hideFavorButton);
                drawParams.setEnterDelegate(getDramaDetailDelegate());
                drawParams.listener(new IDJXDrawListener() {

                    @Override
                    public void onDJXRefreshFinish() {
                        super.onDJXRefreshFinish();
                    }

                    @Override
                    public void onDJXSeekTo(int i, long l) {
                        super.onDJXSeekTo(i, l);
                    }

                    @Override
                    public void onDJXPageChange(int i, Map<String, Object> map) {
                        super.onDJXPageChange(i, map);
                        sendDramaEvent("onDJXPageChange", map);
                    }

                    @Override
                    public void onDJXVideoPlay(Map<String, Object> map) {
                        super.onDJXVideoPlay(map);
                        sendDramaEvent("onDJXVideoPlay", map);
                    }

                    @Override
                    public void onDJXVideoPause(Map<String, Object> map) {
                        super.onDJXVideoPause(map);
                        sendDramaEvent("onDJXVideoPause", map);
                    }

                    @Override
                    public void onDJXVideoContinue(Map<String, Object> map) {
                        super.onDJXVideoContinue(map);
                        sendDramaEvent("onDJXVideoContinue", map);
                    }

                    @Override
                    public void onDJXVideoCompletion(Map<String, Object> map) {
                        super.onDJXVideoCompletion(map);
                        sendDramaEvent("onDJXVideoCompletion", map);
                    }

                    @Override
                    public void onDJXVideoOver(Map<String, Object> map) {
                        super.onDJXVideoOver(map);
                        sendDramaEvent("onDJXVideoOver", map);
                    }

                    @Override
                    public void onDJXClose() {
                        super.onDJXClose();
                        sendEvent("onVideoClose", null);
                    }

                    @Override
                    public void onDJXReportResult(boolean b, Map<String, Object> map) {
                        super.onDJXReportResult(b, map);
                    }

                    @Override
                    public void onDJXRequestStart(@Nullable Map<String, Object> map) {
                        super.onDJXRequestStart(map);
                    }

                    @Override
                    public void onDJXRequestFail(int i, String s, @Nullable Map<String, Object> map) {
                        super.onDJXRequestFail(i, s, map);
                        sendEvent("onError", FADSDramaUtils.toErrMap(i, s));
                    }

                    @Override
                    public void onDJXRequestSuccess(List<Map<String, Object>> list) {
                        super.onDJXRequestSuccess(list);
                    }

                    @Override
                    public void onChannelTabChange(int i) {
                        super.onChannelTabChange(i);
                    }

                    @Override
                    public void onDurationChange(long l) {
                        super.onDurationChange(l);
                    }
                    @Override
                    public View createCustomView(ViewGroup viewGroup, @Nullable Map<String, Object> map) {

                        Context context = viewGroup.getContext();;
                        int dramaId = getIntFromMap(map,"drama_id",1);
                        DramaInfoView dramaInfoView = new DramaInfoView(context, 20, true,false);
                        // 2. 从参数 map 中获取需要设置的数据（假设 map 来自 Flutter）
                        String coverUrl = map != null && map.containsKey("cover_image") ? map.get("cover_image").toString() : "";
                        String title = map != null && map.containsKey("title") ? map.get("title").toString() : "默认标题";
                        String desc = map != null && map.containsKey("desc") ? map.get("desc").toString() : "默认简介";

                        int nTotal = getIntFromMap(map,"total",1);
                        int nIndex = getIntFromMap(map,"index",1);
                        String jvJiDesc = String.format("第%d集 - 共%d集", nIndex, nTotal);

                        // 标签可能是 List<String> 类型
                        String tags = map != null && map.containsKey("type") ? map.get("type").toString() : "";

                        // 3. 设置数据
                        dramaInfoView.setData(coverUrl, title, new String[]{tags}, desc, jvJiDesc);

                        // 4. 设置点击事件（可选）
                        dramaInfoView.setOnPosterClickListener(v -> {
                            ConfigUtils.sendDramaFeedEvent("onOpenSelfDetail", map);
                        });

                        dramaInfoView.setOnTitleClickListener(v -> {
                            ConfigUtils.sendDramaFeedEvent("onOpenSelfDetail", map);
                        });

                        dramaInfoView.setOnEpisodeClickListener(v -> {
                            ConfigUtils.sendDramaFeedEvent("onOpenDetail", map);
                        });

                        return dramaInfoView;
                    }
                });
                mWidget = DJXSdk.factory().createDraw(drawParams);
            }
            // 更新 View
            fragmentTransaction.replace(R.id.fl_theater_container, mWidget.getFragment()).commit();
        } else {
            Log.e(TAG, "当前 Activity 不是 FlutterFragmentActivity 无法加载 View");
        }
    }

    /**
     * 获取 FragmentActivity
     *
     * @param context 上下文
     * @return FragmentActivity
     */
    private FragmentActivity getActivity(Context context) {
        Context currentContext = context;
        while (currentContext instanceof ContextWrapper) {
            if (currentContext instanceof FragmentActivity) {
                return (FragmentActivity) currentContext;
            }
            currentContext = ((ContextWrapper) currentContext).getBaseContext();
        }
        return null;
    }

    @Override
    public void dispose() {
        frameLayout.removeAllViews();
        if (mWidget != null) {
            mWidget.destroy();
        }
    }

    /**
     * 设置用户可见性，用于暂停和播放
     *
     * @param isVisibleToUser 是否可见
     */
    private void setUserVisibleHint(boolean isVisibleToUser) {
        if (mWidget == null) {
            return;
        }
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        transaction.setMaxLifecycle(mWidget.getFragment(), isVisibleToUser ? Lifecycle.State.RESUMED : Lifecycle.State.STARTED);
        if (isVisibleToUser) {
            transaction.show(mWidget.getFragment());
        } else {
            transaction.hide(mWidget.getFragment());
        }
        transaction.commitAllowingStateLoss();
    }

    /**
     * 方法调用
     *
     * @param call   参数
     * @param result 结果
     */
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        String method = call.method;
        Log.d(TAG, "onMethodCall method:" + method + " arguments:" + call.arguments);
        if ("pause".equals(method)) {
            // 暂停
            setUserVisibleHint(false);
        } else if ("resume".equals(method)) {
            // 继续
            setUserVisibleHint(true);
        } else if("setSpeedPlay".equals(method)){
            double speed = call.argument("speed");
            int scope = call.argument("scope");
            // 设置倍速
            if (mWidget != null) {
                mWidget.setSpeedPlay((float) speed, DJXPlaySpeedScope.values()[scope]);
            }
        }
        result.success(true);
    }

    /**
     * 发送通道消息
     */
    public void sendEvent(String method, Object data) {
        if (methodChannel != null) {
            methodChannel.invokeMethod(method, data);
        }
    }

    /**
     * 发送短剧事件
     */
    public void sendDramaEvent(String method, Map<String, Object> map) {
        sendEvent(method, FADSDramaUtils.toMap(map));
    }

    /**
     * 获取短剧详情代理
     *
     * @return 代理
     */
    private IDJXDramaDetailDelegate getDramaDetailDelegate() {
        return new IDJXDramaDetailDelegate() {
            @Override
            public void onEnter(Context context, DJXDrama drama, int i) {
                // 点击进入短剧详情
                Log.d(TAG, "DJXWidgetDrawParams onEnter onOpenDetail: " + drama.id);
                sendEvent("onOpenDetail", FADSDramaUtils.toMap(drama));
            }
        };
    }
}
