package com.zero.flutter_adcontent.page;

import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.os.Bundle;

import com.bytedance.sdk.djx.DJXSdk;
import com.bytedance.sdk.djx.IDJXWidget;
import com.bytedance.sdk.djx.interfaces.listener.IDJXDramaHomeListener;
import com.bytedance.sdk.djx.model.DJXDramaDetailConfig;
import com.bytedance.sdk.djx.params.DJXWidgetDramaHomeParams;
import com.zero.flutter_adcontent.R;

public class TheaterActivity extends AppCompatActivity {
    private IDJXWidget mWidget;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_teater);
        initView();
        initData();
    }

    protected void initView() {

    }

    protected void initData() {
        // 解析参数
        Intent intent = getIntent();
        boolean showBackBtn = intent.getBooleanExtra("showBackBtn", true);
        boolean showPageTitle = intent.getBooleanExtra("showPageTitle", true);
        boolean showChangeBtn = intent.getBooleanExtra("showChangeBtn", true);
        // 创建剧场配置
        DJXDramaDetailConfig dramaDetailConfig = ConfigUtils.createDramaFlowDetailConfig();
        DJXWidgetDramaHomeParams djxWidgetDramaHomeParams = DJXWidgetDramaHomeParams.obtain(dramaDetailConfig);
        djxWidgetDramaHomeParams.mShowBackBtn = showBackBtn;
        djxWidgetDramaHomeParams.mShowPageTitle = showPageTitle;
        djxWidgetDramaHomeParams.mShowChangeBtn = showChangeBtn;
        mWidget = DJXSdk.factory().createDramaHome(djxWidgetDramaHomeParams);
        // 更新 View
        getSupportFragmentManager().beginTransaction().replace(R.id.fl_theater_container, mWidget.getFragment()).commit();
    }
}