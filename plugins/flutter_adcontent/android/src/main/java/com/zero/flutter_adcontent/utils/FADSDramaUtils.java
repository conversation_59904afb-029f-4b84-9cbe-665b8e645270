package com.zero.flutter_adcontent.utils;

import android.util.Log;

import com.bytedance.sdk.djx.model.DJXDrama;
import com.bytedance.sdk.djx.model.DJXEpisodeStatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 短剧详情解析类
public class FADSDramaUtils {

    /**
     * 解析短剧列表
     *
     * @param dramas 短剧列表
     * @return 短剧列表
     */
    static public List<HashMap<String, Object>> toList(List<? extends DJXDrama> dramas) {
        List<HashMap<String, Object>> list = new ArrayList<>();
        for (DJXDrama drama : dramas) {
            list.add(toMap(drama));
        }
        return list;
    }
    /**
     * 解析短剧解锁状态列表
     *
     * @param episodeStatusList 短剧解锁列表
     * @return 短剧列表
     */
    static public List<HashMap<String, Object>> toEpisodeStatusList(List<DJXEpisodeStatus> episodeStatusList) {
        List<HashMap<String, Object>> list = new ArrayList<>();
        if (episodeStatusList == null) {
            return list;
        }
        for (DJXEpisodeStatus status : episodeStatusList) {
            list.add(new HashMap<String, Object>() {
                {
                    put("index", status.getIndex());
                    put("isLocked", status.isLocked());
                }
            });
        }
        return list;
    }

    /**
     * 解析短剧详情
     *
     * @param drama 短剧详情
     * @return 短剧详情
     */
    static public HashMap<String, Object> toMap(DJXDrama drama) {
        return new HashMap<String, Object>() {
            {
                put("id", drama.id);
                put("groupId", drama.groupId);
                put("title", drama.title);
                put("coverImage", drama.coverImage);
                put("status", drama.status);
                put("total", drama.total);
                put("index", drama.index);
                put("unlockIndex", drama.unlockIndex);
                put("type", drama.type);
                put("typeId", drama.typeId);
                put("desc", drama.desc);
                put("createTime", drama.createTime);
                put("actionTime", drama.actionTime);
                put("freeSet", drama.freeSet);
                put("lockSet", drama.lockSet);
                put("icpNumber", drama.icpNumber);
                put("isFavor", drama.isFavor);
                put("favoriteTime", drama.favoriteTime);
                put("favoriteCount", drama.favoriteCount);
                put("levelLabel", drama.levelLabel);
                put("isPotential", drama.isPotential);
            }
        };
    }

    /**
     * 解析 map 中的短剧详情，保持一致
     */
    static public HashMap<String, Object> toMap(Map<String, Object> dramaMap) {
        Log.d("hashMapHere", "toMap================" + dramaMap);
        return new HashMap<String, Object>() {
            {
                put("id", dramaMap.get("drama_id"));
                put("groupId", dramaMap.get("groupId"));
                put("title", dramaMap.get("title"));
                put("coverImage", dramaMap.get("cover_image"));
                put("status", dramaMap.get("status"));
                put("total", dramaMap.get("total"));
                put("index", dramaMap.get("index"));
                put("unlockIndex", dramaMap.get("unlockIndex"));
                put("type", dramaMap.get("type"));
                put("desc", dramaMap.get("desc"));
                put("icpNumber", dramaMap.get("icpNumber"));
                put("duration", dramaMap.get("videoDuration"));
                put("episode", dramaMap.get("episode"));
                put("tags", dramaMap.get("script_name"));
            }
        };
    }


    /**
     * 解析短剧详情
     *
     * @param errCode 错误码
     * @param errMsg  错误信息
     * @return 短剧详情
     */
    static public HashMap<String, Object> toErrMap(int errCode, String errMsg) {
        return new HashMap<String, Object>() {
            {
                put("errCode", errCode);
                put("errMsg", errMsg);

            }
        };
    }

}
