package com.zero.flutter_adcontent.page;

import android.content.Context;
import android.content.ContextWrapper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.bytedance.sdk.djx.DJXSdk;
import com.bytedance.sdk.djx.IDJXDramaDetailDelegate;
import com.bytedance.sdk.djx.IDJXWidget;
import com.bytedance.sdk.djx.model.DJXDrama;
import com.bytedance.sdk.djx.model.DJXDramaDetailConfig;
import com.bytedance.sdk.djx.params.DJXWidgetDramaHomeParams;
import com.bytedance.sdk.djx.params.DJXWidgetDrawParams;
import com.bytedance.sdk.nov.api.NovSdk;
import com.bytedance.sdk.nov.api.iface.INovHomeListener;
import com.bytedance.sdk.nov.api.model.NovStory;
import com.bytedance.sdk.nov.api.params.NovReaderConfig;
import com.bytedance.sdk.nov.api.params.NovWidgetHomeParams;
import com.zero.flutter_adcontent.PluginDelegate;
import com.zero.flutter_adcontent.R;
import com.zero.flutter_adcontent.utils.FADSDramaUtils;
import com.zero.flutter_adcontent.utils.FADSNovelStoryUtils;

import java.util.Map;

import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

/// 短故事聚合页 View
public class NovelStoryView implements PlatformView {
    private final String TAG = NovelStoryView.class.getSimpleName();
    private final FrameLayout frameLayout;
    private IDJXWidget mWidget;
    private final PluginDelegate pluginDelegate;
    private final int id;
    private MethodChannel methodChannel;


    NovelStoryView(Context context, int id, Map<String, Object> creationParams, PluginDelegate pluginDelegate) {
        Log.d(TAG, "init id:" + id);
        this.id = id;
        this.pluginDelegate = pluginDelegate;
        methodChannel = new MethodChannel(this.pluginDelegate.bind.getBinaryMessenger(), PluginDelegate.KEY_VIEW_NOVEL_STORY + "/" + id);
        methodChannel.setMethodCallHandler(ConfigUtils::novelMethodCall);
        frameLayout = (FrameLayout) LayoutInflater.from(context).inflate(R.layout.view_novel_story, null);
        ConfigUtils.novelChannel = methodChannel;
        MethodCall call = new MethodCall("novel_story_view", creationParams);
        loadView(context, call);
    }

    @Override
    public View getView() {
        return frameLayout;
    }

    public void loadView(Context context, @NonNull MethodCall call) {
        Log.d(TAG, "loadView: ");
        FragmentActivity activity = getActivity(context);
        if (activity instanceof FlutterFragmentActivity) {
            // 获取 FragmentManager
            FragmentManager fragmentManager = activity.getSupportFragmentManager();
            FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
            // 解析参数
            ConfigUtils.novelCall = call;
            // 短故事阅读页配置
            NovReaderConfig novReaderConfig = ConfigUtils.getNovReaderConfig();
            // 创建短故事聚合页配置
            int setTopOffset = call.argument("setTopOffset");
            int recPageSize = call.argument("recPageSize");
            boolean canPullRefresh = call.argument("canPullRefresh");
            NovWidgetHomeParams novWidgetHomeParams = new NovWidgetHomeParams(novReaderConfig);
            novWidgetHomeParams.setTopOffset(setTopOffset);
            novWidgetHomeParams.setRecPageSize(recPageSize);
            novWidgetHomeParams.setCanPullRefresh(canPullRefresh);
            novWidgetHomeParams.setHomeListener(new INovHomeListener() {
                @Override
                public void onItemClick(@NonNull NovStory novStory, @Nullable Map<String, ?> map) {
                    // 点击短故事
                    Log.d(TAG, "NovWidgetHomeParams onItemClick: " + novStory.getTitle());
                    sendEvent("onNovelHomeItemClick", FADSNovelStoryUtils.toMap(novStory));
                    ConfigUtils.novelChannel = methodChannel;
                }
            });
            mWidget = NovSdk.factory().createStoryHome(novWidgetHomeParams);
            // 更新 View
            fragmentTransaction.replace(R.id.fl_view_novel_story_container, mWidget.getFragment()).commit();
        } else {
            Log.e(TAG, "当前 Activity 不是 FlutterFragmentActivity 无法加载 View");
        }
    }

    /**
     * 获取 FragmentActivity
     *
     * @param context 上下文
     * @return FragmentActivity
     */
    private FragmentActivity getActivity(Context context) {
        Context currentContext = context;
        while (currentContext instanceof ContextWrapper) {
            if (currentContext instanceof FragmentActivity) {
                return (FragmentActivity) currentContext;
            }
            currentContext = ((ContextWrapper) currentContext).getBaseContext();
        }
        return null;
    }

    @Override
    public void dispose() {
        frameLayout.removeAllViews();
        if (mWidget != null) {
            mWidget.destroy();
        }
    }

    /**
     * 发送通道消息
     */
    public void sendEvent(String method, Object data) {
        if (methodChannel != null) {
            methodChannel.invokeMethod(method, data);
        }
    }

    /**
     * 获取短剧详情代理
     *
     * @return 代理
     */
    private IDJXDramaDetailDelegate getDramaDetailDelegate() {
        return new IDJXDramaDetailDelegate() {
            @Override
            public void onEnter(Context context, DJXDrama drama, int i) {
                // 点击进入短剧详情
                Log.d(TAG, "DJXWidgetDrawParams onEnter onOpenDetail: " + drama.id);
                sendEvent("onOpenDetail", FADSDramaUtils.toMap(drama));
            }
        };
    }
}
