package com.zero.flutter_adcontent.page;

import android.content.Context;
import android.content.ContextWrapper;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Lifecycle;

import com.bytedance.sdk.djx.DJXPlaySpeedScope;
import com.bytedance.sdk.djx.DJXSdk;
import com.bytedance.sdk.djx.IDJXWidget;
import com.bytedance.sdk.djx.model.DJXDramaDetailConfig;
import com.bytedance.sdk.djx.params.DJXWidgetDramaDetailParams;
import com.zero.flutter_adcontent.PluginDelegate;
import com.zero.flutter_adcontent.R;

import java.util.Map;

import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

/// 短剧 View
public class DramaDetailView implements PlatformView {
    private final String TAG = DramaDetailView.class.getSimpleName();
    private final FrameLayout frameLayout;
    private IDJXWidget mWidget;
    private final PluginDelegate pluginDelegate;
    private final int id;
    private MethodChannel methodChannel;

    FragmentManager fragmentManager;
    FragmentTransaction fragmentTransaction;


    DramaDetailView(Context context, int id, Map<String, Object> creationParams, PluginDelegate pluginDelegate) {
        Log.d(TAG, "init id:" + id);
        this.id = id;
        this.pluginDelegate = pluginDelegate;
        methodChannel = new MethodChannel(this.pluginDelegate.bind.getBinaryMessenger(), PluginDelegate.KEY_VIEW_DRAMA + "/" + id);
        methodChannel.setMethodCallHandler(this::onMethodCall);
//        frameLayout = (FrameLayout) LayoutInflater.from(context).inflate(R.layout.activity_detail, null);
        frameLayout = new FrameLayout(context);
        frameLayout.setLayoutParams(new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
        int containerId = View.generateViewId(); // 动态生成唯一 id
        frameLayout.setId(containerId);
        MethodCall call = new MethodCall("drama_view", creationParams);
        loadView(context, call, containerId);
    }

    @Override
    public View getView() {
        return frameLayout;
    }

    public void loadView(Context context, @NonNull MethodCall call,int containerId) {
        Log.d(TAG, "loadView: ");
        FragmentActivity activity = getActivity(context);
        if (activity instanceof FlutterFragmentActivity) {
            // 获取 FragmentManager
            fragmentManager = activity.getSupportFragmentManager();
            fragmentTransaction = fragmentManager.beginTransaction();
            // 解析参数
            int id = call.argument("id");
            int index = call.argument("index");
            String groupId = call.argument("groupId");
            ConfigUtils.detailCall = call;
            ConfigUtils.detailChannel = methodChannel;
            // 创建剧场配置
            DJXDramaDetailConfig detailConfig = ConfigUtils.getDetailConfig();
            if (detailConfig == null) {
                Log.e(TAG, "getDetailConfig is null");
                return;
            }
            // 创建短剧详情
            DJXWidgetDramaDetailParams djxWidgetDramaHomeParams = DJXWidgetDramaDetailParams.obtain(id, index, detailConfig)
                    .currentDuration(0)
                    .fromGid(groupId) // 必传，否则影响推荐效果
                    .from(DJXWidgetDramaDetailParams.DJXDramaEnterFrom.DEFAULT);
            mWidget = DJXSdk.factory().createDramaDetail(djxWidgetDramaHomeParams);
            // 更新 View
            fragmentTransaction.replace(containerId, mWidget.getFragment()).commit();
        } else {
            Log.e(TAG, "当前 Activity 不是 FlutterFragmentActivity 无法加载 View");
        }
    }

    /**
     * 获取 FragmentActivity
     *
     * @param context 上下文
     * @return FragmentActivity
     */
    private FragmentActivity getActivity(Context context) {
        Context currentContext = context;
        while (currentContext instanceof ContextWrapper) {
            if (currentContext instanceof FragmentActivity) {
                return (FragmentActivity) currentContext;
            }
            currentContext = ((ContextWrapper) currentContext).getBaseContext();
        }
        return null;
    }

    @Override
    public void dispose() {
        if (mWidget != null) {
            Fragment fragment = mWidget.getFragment();
            if (fragmentManager != null && fragment != null && fragment.isAdded()) {
                fragmentManager.beginTransaction().remove(fragment).commitAllowingStateLoss();
            }
            mWidget.destroy();
        }
        frameLayout.removeAllViews();
    }

    /**
     * 设置用户可见性，用于暂停和播放
     *
     * @param isVisibleToUser 是否可见
     */
    private void setUserVisibleHint(boolean isVisibleToUser) {
        if (mWidget == null) {
            return;
        }
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        transaction.setMaxLifecycle(mWidget.getFragment(), isVisibleToUser ? Lifecycle.State.RESUMED : Lifecycle.State.STARTED);
        if (isVisibleToUser) {
            transaction.show(mWidget.getFragment());
        } else {
            transaction.hide(mWidget.getFragment());
        }
        transaction.commitAllowingStateLoss();
    }

    /**
     * 方法调用
     *
     * @param call   参数
     * @param result 结果
     */
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        String method = call.method;
        Log.d(TAG, "onMethodCall method:" + method + " arguments:" + call.arguments);
        if ("unlock".equals(method)) {
            // 解锁
            ConfigUtils.setUnlock(call);
        }else if ("setCustomAdOnShow".equals(method)){
            // 自定义广告解锁，展示时设置价格
            ConfigUtils.setCustomAdOnShow(call);
        }else if ("setCustomAdOnReward".equals(method)){
            // 自定义广告解锁，奖励
            ConfigUtils.setCustomAdOnReward(call);
        }else if ("setCurrentIndex".equals(method)){
            // 设置当前集数
            int index = (int) call.arguments;
            if (mWidget != null) {
                mWidget.setCurrentDramaIndex(index);
            }
        }else if ("openDramaGallery".equals(method)){
            // 打开选集面板
            if (mWidget != null) {
                mWidget.openDramaGallery();
            }
        } else if ("openMoreDialog".equals(method)) {
            // 打开更多弹窗
            if (mWidget != null) {
                mWidget.openMoreDialog();
            }
        }else if ("pause".equals(method)) {
            // 暂停
            setUserVisibleHint(false);
        } else if ("resume".equals(method)) {
            // 继续
            setUserVisibleHint(true);
        } else if ("dramaFavNative".equals(method)) {
            boolean bFav = Boolean.TRUE.equals(call.argument("isFav"));
            int nViewId = call.argument("viewId");
            ConfigUtils.setDramaFavStatus(bFav,nViewId);
//            new Handler(Looper.getMainLooper()).post(() -> {
//                ConfigUtils.setDramaFavStatus(bFav);
//            });

        } else if("setSpeedPlay".equals(method)){
            double speed = call.argument("speed");
            int scope = call.argument("scope");
            // 设置倍速
            if (mWidget != null) {
                mWidget.setSpeedPlay((float) speed, DJXPlaySpeedScope.values()[scope]);
            }
        }
        result.success(true);
    }
}
