group 'com.zero.flutter_adspark'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.3'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }
    }
}

apply plugin: 'com.android.library'

android {
    namespace = 'com.zero.flutter_adspark'
    compileSdkVersion 31

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 16
    }
}

dependencies {
    // 版本更新：https://artifact.bytedance.com/repository/Volcengine/
    //强烈建议升级到6.16.3，适配了荣耀新机型oaid获取不到的问题
     implementation 'com.bytedance.applog:RangersAppLog-Lite-cn:6.16.11'
     //埋点开发工具
//     debugImplementation 'com.bytedance.applog:RangersAppLog-DevTools:3.4.8'
//     debugImplementation 'androidx.recyclerview:recyclerview:1.2.1'
}
