package com.zero.flutter_adspark;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bytedance.applog.AppLog;
import com.bytedance.applog.InitConfig;
import com.bytedance.applog.util.UriConstants;

import org.json.JSONObject;

import java.util.Map;

import io.flutter.BuildConfig;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

/**
 * FlutterAdsparkPlugin
 */
public class FlutterAdsparkPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    private final String TAG = FlutterAdsparkPlugin.class.getSimpleName();
    // 方法通道
    private MethodChannel methodChannel;
    // 当前 Activity
    public Activity activity;
    // 插件连接器
    private FlutterPluginBinding bind;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        bind = flutterPluginBinding;
        methodChannel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "flutter_adspark");
        methodChannel.setMethodCallHandler(this);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        String method = call.method;
        Log.d(TAG, "MethodChannel onMethodCall method:" + method + " arguments:" + call.arguments);
        if (method.equals("getPlatformVersion")) {
            result.success("Android " + android.os.Build.VERSION.RELEASE);
        } else if (method.equals("init")) {
            initAd(call, result);
        } else if (method.equals("addEvent")) {
            addEvent(call, result);
        } else if (method.equals("setUserUniqueID")) {
            setUserUniqueID(call, result);
        } else {
            result.notImplemented();
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        methodChannel.setMethodCallHandler(null);
    }

    /**
     * 初始化广告
     *
     * @param call   MethodCall
     * @param result Result
     */
    public void initAd(MethodCall call, final MethodChannel.Result result) {
        String appId = call.argument("appId");
        String channel = call.argument("channel");
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(channel)) {
            result.error("-1", "appId或channel不能为空", null);
        }
        boolean autoTrackEnabled = call.argument("autoTrackEnabled");
        //初始化开始，appid和渠道，appid如不清楚请联系客户成功经理, 注意第二个参数 channel 不能为空
        final InitConfig config = new InitConfig(appId, channel);
        //上报地址
        config.setUriConfig(UriConstants.DEFAULT);
        // 加密开关，SDK 5.5.1 及以上版本支持，false 为关闭加密，上线前建议设置为 true
        AppLog.setEncryptAndCompress(true);
        //日志开关，debug阶段建议开启
        config.setLogEnable(false);
        // 自动启动
        config.setAutoStart(true);
        //全埋点开关，默认不开启
        config.setAutoTrackEnabled(autoTrackEnabled);
        //针对联调管理功能更新SDK初始化配置，因此在集成SDK的时候必须开启延迟深度链接能力
        config.enableDeferredALink();
        /*
         * 为确保合规，集成SDK后，会在获得用户授权之后进行SDK的初始化并开始采集信息，
         * 请确保您采集用户信息前已得到用户的授权
         * 补偿延迟对启动事件的数据影响，如不采用此方式初始化(init时使用三个参数的方法，第三
         * 个参数传当前activity)，会对设备ID获取率有较大影响，进而影响归因结果
         */
        AppLog.init(bind.getApplicationContext(), config, activity);
        //非常重要！！！会直接影响归因结果，必须设置
        AppLog.setHeaderInfo("csj_attribution", 1);
        result.success(true);
    }

    /**
     * 上报事件
     *
     * @param call   MethodCall
     * @param result Result
     */

    public void addEvent(MethodCall call, final MethodChannel.Result result) {
        String eventName = call.argument("eventName");
        if (TextUtils.isEmpty(eventName)) {
            result.error("-1", "eventName不能为空", null);
            return;
        }

        Map<String, Object> params = call.argument("params");
        if (params == null) {
            AppLog.onEventV3(eventName);
        } else {
            int type = call.argument("type");
            JSONObject jsonObject = new JSONObject(params);
            if (type != -1) {
                AppLog.onEventV3(eventName, jsonObject,type);
            } else {
                AppLog.onEventV3(eventName, jsonObject);
            }
        }
        result.success(true);
    }

    /**
     * 设置用户标识
     *
     * @param call   MethodCall
     * @param result Result
     */

    public void setUserUniqueID(MethodCall call, final MethodChannel.Result result) {
        String userUniqueId = call.argument("userUniqueId");
        AppLog.setUserUniqueID(userUniqueId);
        result.success(true);
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        onAttachedToActivity(binding);
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        onDetachedFromActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        activity = null;
    }
}
