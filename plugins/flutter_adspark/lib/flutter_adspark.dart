import 'dart:async';

import 'package:flutter/services.dart';
export 'grown_event_type.dart';

class FlutterAdspark {
  static const MethodChannel _channel = MethodChannel('flutter_adspark');

  static Future<String?> get platformVersion async {
    final String? version = await _channel.invokeMethod('getPlatformVersion');
    return version;
  }

  /// 初始化
  /// [appId] 应用ID
  /// [channel] 渠道
  /// [autoTrackEnabled] 是否开启自动采集
  static Future<bool?> init(String appId, String channel,
      {bool autoTrackEnabled = true}) async {
    final bool? result = await _channel.invokeMethod('init', {
      "appId": appId,
      "channel": channel,
      "autoTrackEnabled": autoTrackEnabled,
    });
    return result;
  }

  /// 设置用户标识
  /// [userUniqueId] 设置您自己的账号体系ID或设备ID, 并保证其唯一性
  static Future<bool?> setUserUniqueID(String? userUniqueId) async {
    final bool? result = await _channel
        .invokeMethod('setUserUniqueID', {"userUniqueId": userUniqueId});
    return result;
  }

  /// 上报事件（系统SDK预定义+自定义）
  /// [eventName] 事件名称
  /// [params] 参数信息
  static Future<bool?> addEvent(String eventName,
      {Map<String, dynamic>? params, int type = -1}) async {
    final bool? result = await _channel.invokeMethod('addEvent', {
      "eventName": eventName,
      "params": params,
      "type": type,
    });
    return result;
  }

  /// 注册事件（系统SDK预定义）
  /// [method] 注册方式
  /// [isSuccess] 是否成功
  static Future<bool?> registerEvent(String method, bool isSuccess) async {
    final bool? result = await addEvent(
      'register',
      params: {
        "method": method,
        "is_success": isSuccess,
      },
      type: 1,
    );
    return result;
  }

  /// 登录事件
  /// [method] 登录方式
  /// [isSuccess] 是否成功
  static Future<bool?> loginEvent(String method, bool isSuccess) async {
    final bool? result = await addEvent(
      'log_in',
      params: {
        "method": method,
        "is_success": isSuccess,
      },
      type: 1,
    );
    return result;
  }

  /// 购买事件
  /// [contentType] 商品类型
  /// [contentName] 商品名称
  /// [contentId] 商品ID
  /// [contentNum] 商品数量
  /// [paymentChannel] 支付渠道
  /// [currency] 货币
  /// [isSuccess] 是否成功
  /// [currencyAmount] 货币数量
  static Future<bool?> purchaseEvent(
      String contentType,
      String contentName,
      String contentId,
      int contentNum,
      String paymentChannel,
      String currency,
      bool isSuccess,
      int currencyAmount) async {
    final bool? result = await addEvent(
      'purchase',
      params: {
        "content_type": contentType,
        "content_name": contentName,
        "content_id": contentId,
        "content_num": contentNum,
        "payment_channel": paymentChannel,
        "currency": currency,
        "is_success": isSuccess,
        "currency_amount": currencyAmount,
      },
      type: 1,
    );
    return result;
  }

  /// 绑定社交账号
  /// [accountType] 账户类型
  /// [isSuccess] 是否成功
  static Future<bool?> accessAccountEvent(
      String accountType, bool isSuccess) async {
    final bool? result = await addEvent(
      'access_account',
      params: {
        "account_type": accountType,
        "is_success": isSuccess,
      },
      type: 1,
    );
    return result;
  }

  /// 任务事件
  /// [questId] 任务ID
  /// [questType] 任务类型
  /// [questName] 任务名称
  /// [questNo] 任务编号
  /// [isSuccess] 是否成功
  /// [description] 描述
  static Future<bool?> questEvent(String questId, String questType,
      String questName, int questNo, bool isSuccess, String description) async {
    final bool? result = await addEvent(
      'quest',
      params: {
        "quest_id": questId,
        "quest_type": questType,
        "quest_name": questName,
        "quest_no": questNo,
        "is_success": isSuccess,
        "description": description,
      },
      type: 1,
    );
    return result;
  }

  /// 更新等级事件
  /// [level] 等级
  static Future<bool?> updateLevelEvent(int level) async {
    final bool? result = await addEvent(
      'update_level',
      params: {
        "level": level,
      },
      type: 1,
    );
    return result;
  }

  /// 创建游戏角色事件
  /// [gameRoleId] 游戏角色ID
  static Future<bool?> createGameRoleEvent(String gameRoleId) async {
    final bool? result = await addEvent(
      'create_gamerole',
      params: {
        "gamerole_id": gameRoleId,
      },
      type: 1,
    );
    return result;
  }

  /// 结账事件
  /// [contentType] 商品类型
  /// [contentName] 商品名称
  /// [contentId] 商品ID
  /// [contentNum] 商品数量
  /// [isVirtualCurrency] 是否虚拟货币
  /// [virtualCurrency] 虚拟货币
  /// [currency] 货币
  /// [isSuccess] 是否成功
  /// [currencyAmount] 货币数量
  static Future<bool?> checkOutEvent(
      String contentType,
      String contentName,
      String contentId,
      int contentNum,
      bool isVirtualCurrency,
      String virtualCurrency,
      String currency,
      bool isSuccess,
      int currencyAmount) async {
    final bool? result = await addEvent(
      'check_out',
      params: {
        "content_type": contentType,
        "content_name": contentName,
        "content_id": contentId,
        "content_num": contentNum,
        "is_virtual_currency": isVirtualCurrency,
        "virtual_currency": virtualCurrency,
        "currency": currency,
        "is_success": isSuccess,
        "currency_amount": currencyAmount,
      },
      type: 1,
    );
    return result;
  }

  /// 添加到收藏事件
  /// [contentType] 商品类型
  /// [contentName] 商品名称
  /// [contentId] 商品ID
  /// [contentNum] 商品数量
  /// [isSuccess] 是否成功
  static Future<bool?> addToFavoriteEvent(
      String contentType,
      String contentName,
      String contentId,
      int contentNum,
      bool isSuccess) async {
    final bool? result = await addEvent(
      'add_to_favourite',
      params: {
        "content_type": contentType,
        "content_name": contentName,
        "content_id": contentId,
        "content_num": contentNum,
        "is_success": isSuccess,
      },
      type: 1,
    );
    return result;
  }

  /// 添加支付渠道
  /// [paymentChannel] 支付渠道
  /// [isSuccess] 是否成功
  static Future<bool?> accessPaymentChannelEvent(
      String paymentChannel, bool isSuccess) async {
    final bool? result = await addEvent(
      'access_payment_channel',
      params: {
        "payment_channel": paymentChannel,
        "is_success": isSuccess,
      },
      type: 1,
    );
    return result;
  }

  /// 添加到购物车事件
  /// [contentType] 商品类型
  /// [contentName] 商品名称
  /// [contentId] 商品ID
  /// [contentNum] 商品数量
  /// [isSuccess] 是否成功
  static Future<bool?> addCartEvent(String contentType, String contentName,
      String contentId, int contentNum, bool isSuccess) async {
    final bool? result = await addEvent(
      'add_cart',
      params: {
        "content_type": contentType,
        "content_name": contentName,
        "content_id": contentId,
        "content_num": contentNum,
        "is_success": isSuccess,
      },
      type: 1,
    );
    return result;
  }

  /// 查看商品事件
  /// [contentType] 商品类型
  /// [contentName] 商品名称
  /// [contentId] 商品ID
  static Future<bool?> viewContentEvent(
      String contentType, String contentName, String contentId) async {
    final bool? result = await addEvent(
      'view_content',
      params: {
        "content_type": contentType,
        "content_name": contentName,
        "content_id": contentId,
      },
      type: 1,
    );
    return result;
  }
}
