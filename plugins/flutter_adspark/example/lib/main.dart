import 'dart:io';

import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter_adspark/flutter_adspark.dart';

import 'style.dart';

void main() {
  // 绑定引擎
  WidgetsFlutterBinding.ensureInitialized();
  init();
  runApp(const MyApp());
}

/// 初始化
Future<void> init() async {
  bool? result;
  if (Platform.isAndroid) {
    result = await FlutterAdspark.init("379054", "huawei");
  } else {
    result = await FlutterAdspark.init("302118", "AppStore");
  }
  // bool? result = await FlutterAdspark.init("544828", "huawei");
  print("FlutterAdspark init result: $result");
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('FlutterAds Adspark Demo'),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Center(
              child: Column(
                children: [
                  kSizedBox10,
                  const ListTile(title: Text('👱🏻‍♂️ 账户信息')),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('注册'),
                    onTap: () {
                      FlutterAdspark.registerEvent("wechat", true);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('登录'),
                    onTap: () {
                      FlutterAdspark.loginEvent("wechat", true);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('设置用户标识'),
                    onTap: () {
                      FlutterAdspark.setUserUniqueID("userId_001");
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('清除用户标识'),
                    onTap: () {
                      FlutterAdspark.setUserUniqueID(null);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('绑定社交账号'),
                    onTap: () {
                      FlutterAdspark.accessAccountEvent("wechat", true);
                    },
                  ),
                  kDivider,
                  const ListTile(title: Text('🛒 购物支付')),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('下单'),
                    onTap: () {
                      FlutterAdspark.addCartEvent(
                          "gift", "flower", "008", 2, true);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('支付'),
                    onTap: () {
                      FlutterAdspark.purchaseEvent(
                          "gift", "flower", "008", 2, "wechat", "¥", true, 99);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('支付渠道'),
                    onTap: () {
                      FlutterAdspark.accessPaymentChannelEvent("wechat", true);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('提交购买/下单'),
                    onTap: () {
                      FlutterAdspark.checkOutEvent("gift", "flower", "008", 2,
                          false, "%%", "¥", true, 99);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('收藏'),
                    onTap: () {
                      FlutterAdspark.addToFavoriteEvent(
                          "gift", "flower", "008", 2, true);
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('查看商品'),
                    onTap: () {
                      FlutterAdspark.viewContentEvent("gift", "flower", "008");
                    },
                  ),
                  kDivider,
                  const ListTile(title: Text('🎮 游戏')),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('完成教学任务/副本'),
                    onTap: () {
                      FlutterAdspark.questEvent(
                          "0001", "新手任务", "新手村砍野猪", 3, true, "其他描述");
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('创建游戏角色'),
                    onTap: () {
                      FlutterAdspark.createGameRoleEvent("11001");
                    },
                  ),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('更新等级'),
                    onTap: () {
                      FlutterAdspark.updateLevelEvent(3);
                    },
                  ),
                  kDivider,
                  const ListTile(title: Text('📱 自定义事件')),
                  kDivider,
                  ListTile(
                    dense: true,
                    title: const Text('广告变现'),
                    onTap: () {
                      FlutterAdspark.addEvent(GrownEventType.ad_purchase,
                          params: {
                            "type": "激励视频",
                            "eCPM": "20000",
                          });
                    },
                  ),
                  kDivider,
                  kSizedBox10,
                  kSizedBox10,
                  const Center(child: Text("by FlutterAds")),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
