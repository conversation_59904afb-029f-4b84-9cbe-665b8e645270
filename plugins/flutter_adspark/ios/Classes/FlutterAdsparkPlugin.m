#import "FlutterAdsparkPlugin.h"
#import <RangersAppLog/BDAutoTrack.h>
#import <RangersAppLog/BDAutoTrackConfig.h>
#import <RangersAppLog/BDAutoTrackURLHostItemCN.h>
#ifdef DEBUG
#import <RangersAppLog/BDAutoTrackDevTools.h>
#endif


@interface FlutterAdsparkPlugin()<BDAutoTrackAlinkRouting>
@property(nonatomic, copy) NSDictionary<id,id> *launchOptions;
@end

@implementation FlutterAdsparkPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    FlutterMethodChannel* channel = [FlutterMethodChannel
        methodChannelWithName:@"flutter_adspark"
            binaryMessenger:[registrar messenger]];
    FlutterAdsparkPlugin* instance = [[FlutterAdsparkPlugin alloc] init];
    [registrar addMethodCallDelegate:instance channel:channel];
    [registrar addApplicationDelegate:instance];
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSString *methodStr=call.method;
    if ([@"getPlatformVersion" isEqualToString:methodStr]) {
        result([@"iOS " stringByAppendingString:[[UIDevice currentDevice] systemVersion]]);
    }else if ([@"init" isEqualToString:methodStr]) {
        [self init:call result:result];
    }else if ([@"addEvent" isEqualToString:methodStr]) {
        [self addEvent:call result:result];
    }else if ([@"setUserUniqueID" isEqualToString:methodStr]) {
        [self setUserUniqueID:call result:result];
    }else {
        result(FlutterMethodNotImplemented);
    }
}

// 初始化
- (void) init:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSString *appId=call.arguments[@"appId"];
    NSString *channel=call.arguments[@"channel"];
    BOOL autoTrackEnabled=[call.arguments[@"autoTrackEnabled"] boolValue];
    NSLog(@"init appId:%@ channel:%@",appId,channel);
    /* 初始化SDK开始 */
    // 参数APPID: 参考2.1节获取
    BDAutoTrackConfig *config = [BDAutoTrackConfig configWithAppID:appId launchOptions:self.launchOptions];
    // 设置渠道，iOS一般默认App Store渠道
    config.channel = channel;
    //针对联调管理功能更新SDK初始化配置，因此在集成SDK的时候必须开启延迟深度链接能力
    config.enableDeferredALink = YES;
    // 设置数据上送地址
    config.serviceVendor = BDAutoTrackServiceVendorCN;
    config.autoTrackEnabled = autoTrackEnabled; // 全埋点开关，默认关闭
    // 加密开关，YES开启，NO关闭
    config.logNeedEncrypt = YES; 
    // YES:开启日志，NO:关闭日志
    #ifdef DEBUG
        config.showDebugLog = YES; // 显示调试日志
        config.devToolsEnabled = YES; // 开启调试工具
    #endif
    
    [BDAutoTrack sharedTrackWithConfig:config]; //SDK初始化
    // 如果需要设置当前登陆态 since 6.13.0+
    // [[BDAutoTrack sharedTrack] setCurrentUserUniqueID:@"当前登陆态UUID" withType:@"uuid_type"];
    [[BDAutoTrack sharedTrack] startTrack]; //SDK启动
    
    // 注册接收ALink路由信息的代理, 在这里以AppDelegate对象举例。代理对象需要实现BDAutoTrackALinkRouting协议
    [BDAutoTrack setALinkRoutingDelegate:self];
    /* 初始化SDK结束 */
    /**
      * 必要条件
      * 必要条件
      * 必要条件
      * @description 而且建议在初始化后设置，否则可能不生效
      */
    [BDAutoTrack setCustomHeaderValue:@"1" forKey:@"csj_attribution"];
    // 显示悬浮按钮入口
    #ifdef DEBUG
        [BDAutoTrackDevTools showFloatingEntryButton];
    #endif
    // 返回成功
    result(@(YES));
}

// 上报事件
- (void) addEvent:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSString *eventName=call.arguments[@"eventName"];
    NSDictionary *params = call.arguments[@"params"];
    // 判断事件名称
    if (eventName == nil || [eventName isEqual:[NSNull null]] || eventName.length == 0) {
        result([FlutterError errorWithCode:@"INVALID_ARGUMENT"
                                   message:@"eventName 不能为空"
                                   details:nil]);
        return;
    }
    // 判断参数
    if (params == nil || [params isEqual:[NSNull null]]) {
        [[BDAutoTrack sharedTrack] eventV3:eventName];
    }else{
        [[BDAutoTrack sharedTrack] eventV3:eventName params:params];
    }
}

// 设置用户标识
- (void) setUserUniqueID:(FlutterMethodCall*) call result:(FlutterResult) result{
    NSString *userUniqueId=call.arguments[@"userUniqueId"];
    // 判断事件名称
    if (userUniqueId == nil || [userUniqueId isEqual:[NSNull null]] || userUniqueId.length == 0) {
        [[BDAutoTrack sharedTrack] clearUserUniqueID];
    }else{
        [[BDAutoTrack sharedTrack] setCurrentUserUniqueID:userUniqueId];
        
    }
}

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    self.launchOptions=launchOptions;
    NSLog(@"FlutterAdsparkPlugin application");
    return YES;
}

/// Deferred deep link callback 根据回调返回的路由信息路由页面
/// 发生于应用首启时（包括卸载重装）
/// @param routingInfo 路由信息
- (void)onAttributionData:(nullable NSDictionary *)routingInfo error:(nullable NSError *)error {
    NSLog(@"FlutterAdsparkPlugin onAttributionData");
    if (!error && routingInfo) {
       // 路由或打印日志
    }
}


@end
