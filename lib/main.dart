import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_pages.dart';
import 'package:flutter_common_base/util/util_routes/route_manager.dart';
import 'package:flutter_common_base/util/util_routes/native_route_manager.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_theme_config/theme_config.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  ///强制竖屏
  SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
  if (Platform.isAndroid) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarContrastEnforced: false,
      statusBarColor: Colors.transparent,
    ));
  } else {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
    ));
  }

  ///初始化存储服务
  await StorageUtil.init();

  runApp(ScreenUtilInit(
      designSize: const Size(BizValues.designWidth, BizValues.designHeight),

      ///设计稿的宽度和高度 px
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context)
              .copyWith(textScaler: const TextScaler.linear(1.0)),
          child: SafeArea(
            top: false,
            bottom: false,
            child: MaterialApp(
              debugShowCheckedModeBanner: false,
              title: "星雪短剧",
              navigatorKey: NativeRouteManager.navigatorKey,

              ///配置主题
              theme: ConfigUtil.themeData,
              initialRoute: AppPages.initial,
              navigatorObservers: [RouteUtil.routeObserver],

              ///路由生成器
              onGenerateRoute: AppPages.generateRoute,
              routes: AppPages.routes,

              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,

                ///是Flutter的一个本地化委托，用于提供Material组件库的本地化支持
                GlobalWidgetsLocalizations.delegate,

                ///用于提供通用部件（Widgets）的本地化支持
                GlobalCupertinoLocalizations.delegate,

                ///用于提供Cupertino风格的组件的本地化支持
              ],

              /// 支持的语言和地区
              supportedLocales: const [
                Locale('zh', 'CN'),
                Locale('en', 'US'),
              ],
              builder: EasyLoading.init(),
            ),
          ),
        );
      }));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});
  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text(
              'You have pushed the button this many times:',
            ),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
