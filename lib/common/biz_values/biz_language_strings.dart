

class LanStrings {

  static const String bizCodeSuccess = "SUCCESS";

  ///---*-客户端相关CODE
  static const String homeTabMain = "home_tab_main";

  ///tab 推荐
  static const String tabRecommend = "recommend";
  ///tab 短剧
  static const String tabShortVideo = "videos";
  ///tab 消息
  static const String tabMessages = "tabMessages";
  ///书籍
  static const String tabBook = "story";
  ///tab 我的
  static const String tabMy = "myself";
  ///福利
  static const String tabFuLi = "reward";

  ///网络异常
  static const String netWorkError = "netWorkError";
  ///点击重试
  static const String clickToRetry = "clickToRetry";
  ///没有数据
  static const String noDataNow = "noDataNow";


  ///下拉刷新，上拉加载更多部分
  static const String dataPullDownRefresh = "common_pull_down_to_refresh";
  static const String loadFailedRetry = "common_load_failed_retry";
  static const String releaseToRefresh = "common_release_to_refresh";
  static const String refreshComplete = "common_refresh_complete";
  static const String pullUpLoadMore = "common_pull_up_load_more";
  static const String loading = "common_loading";
  static const String releaseToLoadMore = "common_release_to_load_more";
  static const String noMoreData = "common_no_more_data";
  static const String loadFailedTapRefresh = "common_load_failed_tap_refresh"; // 新增
  ///下拉刷新，上拉加载更多部分



  static const String commonConfirm = "common_confirm";
  static const String tipInputPhoneNumber = "tip_input_phone_number";
  static const String tipInputVerifyCode = "tip_verify_code";
  static const String tipGetVerifyCode = "get_verify_code";
  static const String login = "login";
  static const String versionName = "versionName";

  ///多语言解析固定顺序参数，  例子：%1$s %2$s
  static String trArgs(tr, [List<dynamic> args = const []]) {
    var key = tr;
    if (args.isNotEmpty) {
      ///格式化一下，免得对不上
      key = key
          .replaceAll('%@', r'%1$s')
          .replaceAll('%d', r'%1$s')
          .replaceAll('%s', r'%1$s');

      for (int i = 0; i < args.length; i++) {
        key = key.replaceFirst(r'%' + (i + 1).toString() + r'$s', args[i].toString());
      }
    }
    return key;
  }
}
