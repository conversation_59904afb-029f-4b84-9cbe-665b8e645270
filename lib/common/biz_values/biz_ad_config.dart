import 'dart:io';

class BizAdConfig {
  ///appId
  static const String appId = "5663765";

  ///appId ios
  static const String appIdIOS = "5733125";
  // static const String appIdIOS = "5663765";

  static String uniAppId = "";

  ///快手appId
  static const String ksAppId = "2547600001";

  ///app名称
  static const String appName = "星雪短剧";

  ///banner广告位id
  static const String bannerId = "103515847";

  ///开屏位广告id
  static const String splashId = "103515217";

  ///激励广告id
  static const String jiLiAdId = "103515414";

  ///feed流广告
  static const String feedFlow = "103515846";

  ///draw 视频
  static const String drawVideo = "103515509";

  ///短故事
  static const String novelId = "103531306";

  static const int rewardModeSys = 0;
  static const int rewardModeSelfDef = 1;

  ///新视频模板
  static const String insertOrFull = "103513775";

  static String get settingFile => "SDK_Setting_$uniAppId";
  // static String get settingFile => "SDK_Setting_5663765";
  // 短剧密钥
  static String get skitSecureKey => Platform.isAndroid
      ? "HL9g2c0xNJddKN8B3NQ9ZNRe6TdvbKIdtKa6DXpnjxY="
      : "HL9g2c0xNJddKN8B3NQ9ZNRe6TdvbKIdtKa6DXpnjxY=";

  ///来自福利页的奖励
  static String strAdSrcFuLi = "srcFuLiPage";

  ///广告来源为播放页
  static String strAdSrcDrama = "srcDrama";

  ///来自小说的奖励
  static String strAdSrcNovel = "srcNovel";
}
