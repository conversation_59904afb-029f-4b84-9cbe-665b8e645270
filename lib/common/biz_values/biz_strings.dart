
class BizStrings {

  static const String defLanguageArea = "CN";
  static const String defLanguageCode = "zh";
  static const String defLanguageName = "简体中文";

  static const String defErrorLanguageArea = "US";
  static const String defErrorLanguageCode = "en";
  static const String defErrorLanguageName = "English";

  static const String strPrivacyStatement = "1、我们将通过";
  static const String strContextPre = "帮助您了解我们在收集、使用、存储个人信息方面的基本情况，特别是我们所收集的个人信息类型及用途，以及相关的保护措施。\n我们非常重视您的个人信息保护\n您可以在本软件内“我的-关于我们”中查看";/*《用户协议》《隐私政策》《个人信息收集清单》《第三方共享清单》《儿童个人信息保护规则》和《青少年文明公约》*/
  static const String extraLineTwo = "的内容，并了解您所享有的相关权利及实现途径。\n\n2、在您使用本软件期间，我们将根据合法、正当、必要的原则收集如下信息以实现服务功能，包括但不限于：\n- 设备标识信息（如设备型号、操作系统、MAC地址、IMEI、Android ID），用于生成或识别用户账号、保障账号与系统安全。\nMAC地址属于敏感个人信息，我们将仅在取得您授权同意后收集并妥善保护；\n- 已安装应用列表信息，用于安全风控、反作弊等目的，帮助我们识别设备环境的安全性，提升服务质量。\n该信息可能涉及您的使用偏好，属于敏感个人信息，我们将在获得您的明示授权后采集；\n- 获取蓝牙信息，用于设备安全风险识别与防控。\n\n3、我们可能会申请以下权限，以保障相关服务功能的正常实现：\n(1) 访问电话权限：用于读取设备识别信息以保障账号与系统安全，并进行统计与安全校验；\n(2) 访问相机权限：用于扫码签到等功能；\n(3) 访问本地存储权限：用于缓存视频内容，便于您顺畅观看；\n(4) 访问媒体和文件内容权限：用于读取/写入视频信息和视频集图片缓存，提升体验流畅度；\n(5) 开启推送权限：您可通过开启推送接收我们的重要通知和服务提醒；\n(6) 访问网络权限：您可通过无线网络或蜂窝数据连接使用本软件功能。\n\n以上权限不会默认开启，\n我们不会在未征得您明示同意的情况下收集与服务无关的敏感个人信息。\n您有权拒绝或随时取消授权，相关功能将可能受限但不会影响您使用本软件的核心服务。\n\n如您已充分阅读并理解上述内容并同意我们对个人信息的处理，\n请点击“同意”按钮以继续使用我们的服务。";
  static const String userPolicy = "《用户协议》";
  static const String userPrivacy = "《隐私政策》";
  static const String infoCollect = "《个人信息收集清单》";
  static const String infoThird = "《第三方共享清单》";
  static const String childProtect = "《儿童个人信息保护规则》";
  static const String youngRule = "《青少年文明公约》";

  static const String loginUserAgreement =  "《星雪短剧用户协议》";
  static const String privacyPolicy = "《星雪短剧隐私协议》";
  static const String appName = "星雪短剧";

  ///青少年模式开始
  static const String youthRuleLineOne = "1.为呵护青少年健康成长，星雪短剧特别推出青少年模式，该模式下将有部分功能不开放使用，我们精选了一批适合青少年观看内容呈现在剧场，以供青少年模式下的用户阅读。";
  static const String youthRuleLineSec = "2.开启青少年模式，需先设置密码，用于关闭青少年模式，如忘记密码可通过申诉重置密码。";
  static const String youthRuleLineThd = "3.为了保障充足的休息时间，你在每天晚上10点至次日早上6点, 无法使用APP，需由监护人输入密码，方可使用。";
  static const String youthRuleLineFor = "4.开启青少年模式后，将自动为您开启时间锁，时间锁默认设置为1小时;单日使用时长超过触发时间，需由监护人输入密码，方可继续使用。";
  static const String youthRuleLineFiv = "5.涉未成年人举报邮箱：<EMAIL>";

  static const String youthResetPwdTipFir = "同时，请将您本人手持身份证、写有“仅用于星雪短剧青少年模式忘记密码-申诉”纸张的照片（要求本人、身份证、纸张及手机号码在同一照片中，且字迹、证件信息清晰可辨》发送邮件至 <EMAIL>。";
  static const String youthResetPwdTipSec = "注：照片中需清晰呈现的内容包括：\n本人面部与手持动作; \n身份证正反面：(确保姓名、人像，身份证号、有效期等清晰)";
  static const String youthResetPwdTipThd = "手写纸张内容：需用黑色签字笔书写“仅用于星雪短剧青少年模式忘记密码-申诉”字样; \n本人手机号码：需手写或打印在纸张空白处，确保数字清晰无误 。";
  static const String youthResetPwdTipFor = "您的资料仅用与密码重置申诉，星雪短剧不会泄漏您的个人信息 ，并会在7个工作日内尽快为您处理。";
  ///青少年模式结束

  ///   ///余额详情页
    static const String balanceFir = "1.提现收款人须为当前用户登录手机号所绑定的微信、支付宝或其他收款账户。";
    static const String balanceSec = "2.提现申请提交后，预计7天内到账，请耐心等待。感谢您的理解与支持。";
    static const String balanceThd = "3.可用余额>";
    static const String balanceSuffix = "元，即可申请提现";
  ///余额详情页



  ///联系邮箱
  static const String myMailService = "<EMAIL>";
  ///主页
  static const String homeMainAddress = "https://************/";

  ///jsbridgeuserageentst
  static const String jsBridgeCallName = "meteorSnow";
  ///短剧详情页面的路由
  static const String dramaDetailPageRoute = "/dramaDetail";
  ///自定义详情页面
  static const String dramaSelfDetailPageRoute = "/dramaSelfDetail";
  ///aLiMaMa字体
  static const String aLiMaMaBold = "ALiMaMa_Bold";

  ///余额页面的路由名称
  static const String yuEPageRoute = "/YuMoneyPage";
  ///广告附加参数的分隔符
  static const String adExtraDiv = ";";
  ///微信的appId
  static const String wxSdkAppId = "wx8670a89f967d4456";
  ///IOS的Unilin
  static String strIOSUniLink = "https://api-video.meteorsnow.com/app/wechat/";
  ///微信付款到账号的固定参数
  static const String requestMerchantTransfer =  "requestMerchantTransfer";
}
