
import 'package:flutter/material.dart';

class BizValues {
  static const double designWidth = 375.0;

  ///设计稿的宽度
  static const double designHeight = 812.0;

  ///状态栏的高度
  static const double statusHeight = 44.0;
  ///appbar高度
  static const double appbarHeight = 44.0;

  ///默认的距离两边的距离
  static const double defMarginHorizon = 16;

  ///圆角
  static const double cornerRadius16 = 16;

  ///统一对话框的最大宽度和高度
  static const double commonDialogMaxHeight = 347.0;
  static const double commonDialogMaxWidth = 270.0;

  static const double commonHeight48 = 48;
  static const double commonInterval12 = 12;

  static const int maxSizePerPage = 15;

  static const double bottomMargin = 35;
  static const int searchLogMaxCnt = 10;

  static const double itemBlockHeight = 192;
  static const double itemBlockWidth = 109;
  static const double itemMainCross = 4;


  static LinearGradient gradientPink = const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFFC0D6), // 顶部：明显粉色
      Color(0xFFFFDDEB), // 中间：淡粉过渡
      Color(0xFFFFFFFF), // 底部：纯白 ✅
    ],
    stops: [0.0, 0.6, 1.0],
  );

  static LinearGradient gradientBlue = const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFCCE5FF), // 稍深蓝（更清晰顶部）
      Color(0xFFE6F3FF), // 中间过渡（更柔和）
      Color(0xFFFFFFFF), // 纯白收尾（融合底部背景）
    ],
    stops: [0.0, 0.6, 1.0],
  );

  static const int catHomePageId = 0;
  static const int nAdClickLimit = 7;

}
