/// @description :SP键值存储
class SPKey {
  ///同意用户协议
  static const String keyAgreePolicy = "agreePolicy";
  ///新安装
  static const String keyNewInstall = "newInstall";

  ///用户相关
  static const String keyToken = "token";


  ///包、设备相关
  static const String appName = "appName";
  static const String packageName = "packageName";
  static const String buildNumber = "buildNumber";
  static const String keyVersion = "version";

  static const String keyDevId = "devId";
  static const String isPhysicalDevice = "isPhysicalDevice";
  static const String keySysVersion = "OSVersion";
  static const String keyPlatForm = "platform";
  static const String keyNetType = "netType";
  static const String keyChannel = "channel";
  static const String keyBrand = "brand";
  static const String keyModel = "model";
  static const String keySdkInt = "SdkInt";


  static const String language = "language";
  static const String languageName = "languageName";
  static const String area = "area";

  ///网络类型
  static const String netType = "netType";

  ///搜索历史
  static const String searchHistory = "searchHistory";

  ///升级弹窗的最早的一次弹出的标识
  static const String keyUpdateTip = "updateTip";
}
