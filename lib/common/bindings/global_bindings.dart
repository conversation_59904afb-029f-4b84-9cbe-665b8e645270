import 'package:flutter_common_base/func_module/splash_route/splash_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/tab_fuli_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_manage_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_messages/tab_message_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/tab_my_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_recommend/tab_commend_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/tab_videos_controller.dart';
import 'package:get/get.dart';

class GlobalBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<RouteSplashPageController>(() => RouteSplashPageController());
    Get.lazyPut<TabsManageController>(() => TabsManageController());
    Get.lazyPut<TabRecommendController>(() => TabRecommendController());
    Get.lazyPut<TabVideosController>(() => TabVideosController());
    Get.lazyPut<TabMessageController>(() => TabMessageController());
    Get.lazyPut<TabMyController>(() => TabMyController());
    Get.lazyPut<TabFuLiController>(() => TabFuLiController());
  }
}
