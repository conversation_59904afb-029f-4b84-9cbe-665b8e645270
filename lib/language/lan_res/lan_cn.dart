import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';

///简中
class LanguageCN {
  static Map<String, String> toMap() {
    return {
      LanStrings.bizCodeSuccess: "成功",
      LanStrings.homeTabMain: "主页",

      ///tab 推荐
      LanStrings.tabRecommend: "推荐",

      ///tab 剧场
      LanStrings.tabShortVideo: "剧场",

      ///福利
      LanStrings.tabFuLi: "福利",

      ///tab 消息
      LanStrings.tabMessages: "消息",

      ///tab 书籍
      LanStrings.tabBook: "书籍",

      ///tab 我的
      LanStrings.tabMy: "我的",

      LanStrings.netWorkError: "网络错误,请检查网络",

      ///点击重试
      LanStrings.clickToRetry: "点击重试",

      ///没有数据
      LanStrings.noDataNow: "暂无数据",

      ///下拉刷新
      LanStrings.dataPullDownRefresh: "下拉刷新",
      LanStrings.loadFailedRetry: "加载失败！点击重试",
      LanStrings.releaseToRefresh: "松手,刷新",
      LanStrings.refreshComplete: "刷新完成",
      LanStrings.pullUpLoadMore: "上拉加载更多",
      LanStrings.loading: "加载中...",
      LanStrings.releaseToLoadMore: "松手,加载更多",
      LanStrings.noMoreData: "没有更多数据了",
      LanStrings.loadFailedTapRefresh: "加载失败，点击刷新",
      LanStrings.commonConfirm : "确定",
      LanStrings.tipInputPhoneNumber: "请输入手机号",

      LanStrings.tipInputVerifyCode: "请输入验证码",
      LanStrings.tipGetVerifyCode: "获取验证码",
      LanStrings.login: "登录",
      LanStrings.versionName: "版本号",
    };
  }
}
