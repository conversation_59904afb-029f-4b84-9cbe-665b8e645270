import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';

///英文
class LanguageEN {
  static Map<String, String> toMap() {
    return {
      LanStrings.bizCodeSuccess: "Success",
      LanStrings.homeTabMain: "Home",

      ///tab 推荐
      LanStrings.tabRecommend: "Recommend",

      ///tab 短剧
      LanStrings.tabShortVideo: "Videos",

      ///福利
      LanStrings.tabFuLi: "Lotty",

      ///tab 消息
      LanStrings.tabMessages: "Messages",

      ///书籍
      LanStrings.tabBook: "Book",

      ///tab 我的
      LanStrings.tabMy: "My",

      LanStrings.netWorkError: "Network Error",

      ///点击重试
      LanStrings.clickToRetry: "Press to Retry",

      LanStrings.dataPullDownRefresh: "Pull down to refresh",
      LanStrings.loadFailedRetry: "Load failed! Tap to retry",
      LanStrings.releaseToRefresh: "Release to refresh",
      LanStrings.refreshComplete: "Refresh complete",
      LanStrings.pullUpLoadMore: "Pull up to load more",
      LanStrings.loading: "Loading...",
      LanStrings.releaseToLoadMore: "Release to load more",
      LanStrings.noMoreData: "No more data",
      LanStrings.loadFailedTapRefresh: "Load failed, tap to refresh",
      LanStrings.commonConfirm : "Ok",
      LanStrings.tipInputPhoneNumber: "input phone number",
      LanStrings.tipInputVerifyCode: "input verify code",
      LanStrings.tipGetVerifyCode: "get verify code",
      LanStrings.login: "login",
      LanStrings.versionName: "version",
    };
  }
}
