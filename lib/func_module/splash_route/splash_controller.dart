import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/func_module/login/userAgreementPage.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_csj/csj_union_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_service/service_util.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:get/get.dart';

class RouteSplashPageController extends GetxController {
   @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    initService();
  }


  Future<void> initService() async {
    bool bAgreePress = (StorageUtil.get<bool>(SPKey.keyAgreePolicy,defaultValue: false));
    ///初始化协议
    if (!bAgreePress) {
      int nRet = await Get.to(() => const UserAgreementPage(),duration: const Duration(milliseconds: 50), transition: Transition.noTransition, opaque: false);
      if (nRet != ResponseCodeParse.codeSuccess) {
        return;
      }
    }
    ///是否是新安装
    StorageUtil.set<bool>(SPKey.keyNewInstall, false);
    ///初始化服务
    await ServiceInit.init();
    ///青少年模式
    ToolsUtil.initYouthMode();
  }
}