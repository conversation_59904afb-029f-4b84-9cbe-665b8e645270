import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/func_module/splash_route/splash_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class InitRouteSplashPage extends GetView<RouteSplashPageController> {
  const InitRouteSplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RouteSplashPageController>(
      builder: (_) {
        return Container(
          height: 1.sh,
          width: 1.sw,
          decoration: const BoxDecoration(
            color: Colors.transparent,
              image: DecorationImage(image: AssetImage(AssetsRes.splashTopLogo), fit: BoxFit.fill)),
        );
      },
    );
  }
}
