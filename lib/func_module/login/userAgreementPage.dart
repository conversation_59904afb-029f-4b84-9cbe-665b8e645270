import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_csj/csj_union_util.dart';
import 'package:flutter_common_base/util/util_service/service_util.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_webview/util_webview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class UserAgreementPage extends StatelessWidget {
  const UserAgreementPage({super.key});

  static const double interval = 11;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0x99000000),
      body: Center(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: BizValues.defMarginHorizon.w),
          alignment: Alignment.bottomCenter,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(BizValues.cornerRadius16.r)),
          ),
          width: BizValues.commonDialogMaxWidth.w,
          height: BizValues.commonDialogMaxHeight.h,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Column(
                children: [
                  Gap(16.h),
                  ///标题
                  Container(

                    height: BizValues.commonHeight48.h,
                    alignment: Alignment.center,
                    child: Text(
                      "欢迎使用星雪短剧",
                      style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: BizColors.rgb100333333),
                    ),
                  ),
                  ///内容
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      children: [
                        RichText(
                          textAlign: TextAlign.start,
                          text: TextSpan(
                            children: <InlineSpan>[
                              _buildSubTextSpan(BizStrings.strPrivacyStatement, TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///用户协议
                              _buildSubTextSpan(BizStrings.userPolicy, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.userPolicy);
                              }),
                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///隐私政策
                              _buildSubTextSpan(BizStrings.userPrivacy, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.userPrivacy);
                              }),

                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///个人信息收集
                              _buildSubTextSpan(BizStrings.infoCollect, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.userInfoCollect);
                              }),
                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///第三方共享
                              _buildSubTextSpan(BizStrings.infoThird, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.thirdInfoList);
                              }),
                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              _buildSubTextSpan(BizStrings.childProtect, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.childInfoPro);
                              }),
                              _buildSubTextSpan("和", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              _buildSubTextSpan(BizStrings.youngRule, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.youngRule);
                              }),
                              _buildSubTextSpan(BizStrings.strContextPre, TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///用户协议
                              _buildSubTextSpan(BizStrings.userPolicy, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.userPolicy);
                              }),
                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///隐私政策
                              _buildSubTextSpan(BizStrings.userPrivacy, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.userPrivacy);
                              }),

                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///个人信息收集
                              _buildSubTextSpan(BizStrings.infoCollect, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.userInfoCollect);
                              }),
                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              ///第三方共享
                              _buildSubTextSpan(BizStrings.infoThird, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.thirdInfoList);
                              }),
                              _buildSubTextSpan("、", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              _buildSubTextSpan(BizStrings.childProtect, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.childInfoPro);
                              }),
                              _buildSubTextSpan("和", TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {}),
                              _buildSubTextSpan(BizStrings.youngRule, TextStyle(height: 1.5, fontSize: 14.sp, decoration: TextDecoration.underline, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.youngRule);
                              }),

                              _buildSubTextSpan(BizStrings.extraLineTwo, TextStyle(height: 1.5, fontSize: 14.sp, color: BizColors.rgb100333333), onTab: () {
                                WebViewUtil.jumpToCommonWebPage(Get.context!,ApiReqUrl.youngRule);
                              }),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  Gap(BizValues.commonInterval12.h),
                  ///同意或不同意按钮
                  Container(
                    margin: EdgeInsets.only(bottom: 20.h),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _disagreeBtn("不同意"),
                        SizedBox(width: 2.w,),
                        _agreeBtn("同意"),
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _agreeBtn(String strDesc) {
    return CustomButtonView(
      text: strDesc,
      borderRadius: 16,
      width: 220.w,
      height: 42.h,
      bIsDefault: true,
      onTap: () async {
        StorageUtil.set(SPKey.keyAgreePolicy, true);
        Get.back(result: 0);
      },
    );
  }

  Widget _disagreeBtn(String strDesc) {
    return CustomButtonView(
      text: strDesc,
      width: 220.w,
      height: 42.h,
      enableTextColor: BizColors.rgb100939eb3,
      enabledBgColors: const [Colors.transparent,Colors.transparent],
      onTap: () {
        exit(0);
      },
    );
  }

  InlineSpan _buildSubTextSpan(String strTxt, TextStyle style,
      {VoidCallback? onTab}) {
    return TextSpan(
        text: strTxt,
        style: style,
        recognizer:
            onTab == null ? null : (TapGestureRecognizer()..onTap = onTab));
  }

  ///开关的引导段落
}
