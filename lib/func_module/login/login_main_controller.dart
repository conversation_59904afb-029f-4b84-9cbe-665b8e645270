import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/func_module/login/login_privacy_view.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';

class LoginMainController extends CommonBaseController {
  ViewLoaderState historyDataLoadState = ViewLoaderState();

  bool bChecked = false;

  String strTextCodeDesc = "获取验证码";
  String strTextCodeDescDefault = "获取验证码";
  final int nCountSeconds = 30;
  int nTimerCountShow = 30;
  int nIntervalMicroSeconds = 1000;
  ///电话号码
  late TextEditingController editPhoneController;
  late TextEditingController editCodeController;
  ///倒计时
  Timer? timerCount;
  bool bTimeUp = true;

  Fluwx fluWx = Fluwx();
  late WeChatResponseSubscriber listener;

  final String strTransId = "wechat_login";
  @override
  void onInit() {
    super.onInit();
    editPhoneController = TextEditingController();
    editCodeController = TextEditingController();

    nTimerCountShow = nCountSeconds;
    dataLoadState.loadState = LoaderState.stateSucceed;
    listener = chatResponseCallback;
  }

  Future<void> chatResponseCallback(WeChatResponse response) async {
    if (response is WeChatAuthResponse) {
      if (response.state != strTransId) {
        return;
      }
      loginByRetCodeAndCode(response.errCode ?? -1, response.code ?? "");
    }
  }

  Future<void> loginByRetCodeAndCode(int nRetCode ,String strOpenCode) async {
    if( nRetCode != 0 || strOpenCode.isEmpty) {
      return;
    }

    ///拿着这个去登录
    Map<String,dynamic> mapResult = await ApiReqInterface.thirdLoginWx(strCode: strOpenCode);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess ) {
      ToastUtil.showToast(mapResult["msg"] ?? "");
      return;
    }
    String strToken = mapResult['data']["token"] ?? "";
    if( strToken.isEmpty ) {
      ToastUtil.showToast("数据异常");
      return;
    }
    await StorageUtil.set<String>(SPKey.keyToken, strToken);
    UserInfoUtil.getUserInfoDetail();
    Get.back();
  }


  Future<void> loginItemPress(int nType) async {
    if (!bChecked) {
      bool bRet = await showPrivacyDialog(Get.context!) ?? false;
      if (!bRet) {
        return;
      }
    }

    ///先更新状态
    bChecked = true;
    update(["provState"]);
    if( nType == 1 ) {
      fluWx.removeSubscriber(listener);
      fluWx.addSubscriber(listener);
      bool bRet = await fluWx.authBy(which: NormalAuth(scope: 'snsapi_userinfo', state: strTransId,));
    }
  }

  void _startTimerCount() {
    timerCount?.cancel();
    startTimerPre();
    timerCount = Timer.periodic(Duration(milliseconds: nIntervalMicroSeconds), _timerCallback);
  }

  void startTimerPre() {
    nTimerCountShow = nTimerCountShow;
    strTextCodeDesc = '${nTimerCountShow}S 后重新获取';
    bTimeUp = false;
    update(["codeCountDown"]);
  }

  Future<void> _timerCallback(Timer value) async {
    nTimerCountShow = nTimerCountShow - 1;
    if (nTimerCountShow <= 0) {
      bTimeUp = true;
      strTextCodeDesc = strTextCodeDescDefault;
      timerCount?.cancel();
      timerCount = null;
      nTimerCountShow = nCountSeconds;
    } else {
      strTextCodeDesc = '${nTimerCountShow}S 后重新获取';
      bTimeUp = false;
    }
    update(["codeCountDown"]);
  }

  Future<void> getVerifyCodeBtn() async {
    if (!bTimeUp) {
      return;
    }

    if (!ToolsUtil.isValidPhone(editPhoneController.text)) {
      ToastUtil.showToast("请输入正确的手机号");
      return;
    }
    sendVerifyCode(editPhoneController.text);
  }


  Future<void> sendVerifyCode(String strPhoneNumber) async {
    _startTimerCount();
    Map<String,dynamic> mapResult = await ApiReqInterface.sendSms(strPhone: strPhoneNumber);
    bool nRet = ((mapResult["code"] ?? -1 ) == ResponseCodeParse.codeSuccess );
    if(!nRet ) {
      bTimeUp = true;
      strTextCodeDesc = strTextCodeDescDefault;
      timerCount?.cancel();
      timerCount = null;
      nTimerCountShow = nCountSeconds;
      update(["codeCountDown"]);
      // ToastUtil.showToast(mapResult["msg"] ?? "");
      return;
    }
  }


  @override
  void onClose() {
    editCodeController.dispose();
    editPhoneController.dispose();
    fluWx.removeSubscriber(listener);
    timerCount?.cancel();
  }


  Future<void> onLogin() async {

    ///先判断手机号
    if (!ToolsUtil.isValidPhone(editPhoneController.text)) {
      ToastUtil.showToast("请输入正确的手机号");
      return;
    }

    if (!bChecked) {
      bool bRet = await showPrivacyDialog(Get.context!) ?? false;
      if (!bRet) {
        return;
      }
    }

    ///先更新状态
    bChecked = true;
    update(["provState"]);

    if (editCodeController.text.isEmpty) {
      ToastUtil.showToast("验证码不能为空");
      return;
    }

    Map<String,dynamic> mapResult = await ApiReqInterface.phoneVerifyCodeLogin(strPhone: editPhoneController.text, strCode: editCodeController.text);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess ) {
      ToastUtil.showToast(mapResult["msg"] ?? "");
      return;
    }
    String strToken = mapResult['data']["token"] ?? "";
    if( strToken.isEmpty ) {
      ToastUtil.showToast("数据异常");
      return;
    }
    await StorageUtil.set<String>(SPKey.keyToken, strToken);
    UserInfoUtil.getUserInfoDetail();
    Get.back();
  }

  Future<void> onPriCheck() async {
    bChecked = !bChecked;
    update(["provState"]);
  }
}