import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_webview/util_webview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

Future<bool?> showPrivacyDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: true, // 点击空白区域可关闭
    builder: (context) {
      return Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Stack(
          children: [Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '服务协议以及隐私政策',
                  style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold,color: BizColors.rgb100333333),
                ),
                const SizedBox(height: 16),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: TextStyle(color: BizColors.rgb100333333, fontSize: 14.sp),
                    children: [
                      const TextSpan(text: '请阅读并同意星雪短剧 '),
                      TextSpan(
                        text: '《用户协议》',
                        style: TextStyle(
                          color: BizColors.rgb100333333, fontSize: 14.sp,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.userPolicy);
                          },
                      ),
                      const TextSpan(text: ' 和 '),
                      TextSpan(
                        text: '《隐私政策》',
                        style: TextStyle(
                          color: BizColors.rgb100333333, fontSize: 14.sp,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.userPrivacy);
                          },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                TextButton(
                  onPressed: () {
                    Get.back(result: false);
                  },
                  child: const Text(
                    '放弃登录',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
                const SizedBox(height: 8),
            CustomButtonView(
              text: "同意并登录",
              borderRadius: 16,
              width: 220.w,
              height: 42.h,
              bIsDefault: true,
              onTap: () async {
                Get.back(result: true);
              },
            ),
              ],
            ),
          ),
            Positioned(
              top: 0,
              right: 0,
              child: IconButton(
                iconSize: 20,
                padding: EdgeInsets.zero,
                color: Colors.black38,
                icon: const Icon(Icons.close_rounded),
                onPressed: () {
                  Navigator.of(context).pop(null); // 点击关闭按钮
                },
              ),
            ),
          ],
        ),
      );
    },
  );
}
