
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/user_info/login_type_info.dart';
import 'package:flutter_common_base/func_module/login/login_main_controller.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/uitl_login/login_util.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_webview/util_webview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///点击
typedef ThirdIconPress = void Function(int nType);

class LoginMainView extends CommonBaseView<LoginMainController> {
  const LoginMainView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  LoginMainViewState createState() => LoginMainViewState();
}


class LoginMainViewState extends CommonBaseViewState<LoginMainController> with CommonWidgetPub {
  @override
  LoginMainController createController() {
    return LoginMainController();
  }

  @override
  Widget buildBody() {
    return KeepAliveWrapper(child: _mainContent());
  }

  Widget _mainContent() {
    final double bottomPadding = MediaQuery.of(Get.context!).padding.bottom;
    return GestureDetector(
      onTap: () {
        ToolsUtil.hideKeyboard();
      },
      child: SafeArea(
        top: false,
        bottom: false,
        child: Stack(
          children: [
            Scaffold(
              resizeToAvoidBottomInset: false,
              extendBody: true,
              body: Column(
                children: [
                  _headBg(),
                  SizedBox(height: 34.h),
                  phoneEdit(editController: controller.editPhoneController),
                  SizedBox(height: 28.h),
                  verifyCodeEdit(editController: controller.editCodeController),
                  SizedBox(height: 48.h),
                  CustomButtonView(text: LanStrings.login.tr, margin: EdgeInsets.symmetric(horizontal: 40.w), onTap: controller.onLogin),
                  SizedBox(height: 36.h),
                  _buildThirdLogin(),
                  const Spacer(),
                  _buildPrivacyAgreement(),
                  SizedBox(height: bottomPadding),
                  SizedBox(height: 4.h),
                ],
              ),
            ),
            Positioned(top: 55.h, child: leadingWidget(onPress: () {Get.back();},bLoginPage: true)),
          ],
        ),
      ),
    );
  }

  Widget _buildThirdLogin() {
    if (LoginUtil().thdLoginTypeInfo.isEmpty) {
      return const SizedBox.shrink();
    }
    return SizedBox(width: 1.sw, height: 48, child: Row(mainAxisAlignment:MainAxisAlignment.center,children: _buildIcons(LoginUtil().thdLoginTypeInfo),),);
  }

  List<Widget> _buildIcons(List<ThirdLoginTypeInfo> icons) {
    List<Widget> listLogin = [];
    for (int nIndex = 0; nIndex < icons.length; nIndex++) {
      ThirdLoginTypeInfo item = icons[nIndex];
      listLogin.add(_subIcon(item.iconUrl, item.nType));
      if ((nIndex + 1) < icons.length) {
        listLogin.add(SizedBox(width: 8.w));
      }
    }
    return listLogin;
  }

  Widget _subIcon(String strIcon, int nType) {
    return InkWell(
      onTap: () {
        controller.loginItemPress(nType);
      },
      child: SizedBox(
        width: 36.w,
        height: 36.w,
        child: Image.network(strIcon, width: 36.w, height: 36.w, fit: BoxFit.cover),
      ),
    );
  }

  Widget _headBg() {
    return Container(
      width: 1.sw,
      height: 358.h,
      alignment: Alignment.center,
      decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.loginTopBg),fit: BoxFit.fill)),
    );
  }


  ///电话号码编辑框
  Widget phoneEdit({TextEditingController? editController}) {
    return Stack(
      alignment: Alignment.centerLeft,
      children: <Widget>[
        Container(
          alignment: Alignment.center,
          height: 48.h,
          width: 1.sw,
          margin: EdgeInsets.symmetric(horizontal: 40.w),
          child: TextFormField(
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              LengthLimitingTextInputFormatter(11) ///限制长度
            ],
            cursorColor: BizColors.mainLineGradientSecond,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
            controller: editController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 64.w),
              filled: true,
              fillColor: BizColors.rgb100fafafa,
              hintText: LanStrings.tipInputPhoneNumber.tr,
/*                suffixIcon: phoneSuffixIcon(controller.accountEditController.text.isNotEmpty, () {
                controller.clearPhoneEdit();
              }),*/
              hintStyle: TextStyle(fontSize: 16.sp, color: BizColors.rgb100d1d8e1),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(29), borderSide: BorderSide.none),),
            onChanged: (value) {
              // controller.onPhoneEditContextChanged(value);
            },
            onSaved: (value) {
              // strUserPhone = value;
            },
          ),
        ),
        Positioned(
          left: 52.w,
          child: InkWell(
            onTap: () {
              // controller.countryCodePage();
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("+86", style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
                ),
                SizedBox(width: 4.w,),
                Image.asset(AssetsRes.loginCodeArrow, width: 12.w, height: 12.w, fit: BoxFit.cover,),
                SizedBox(width: 12.w,),
              ],
            ),
          ),
        ),
        // Positioned(right:1.w,child: phoneSuffixIcon(controller.bShowPhoneSuffix, () {
        //   // controller.clearPhoneEdit();
        // }))
      ],
    );
  }

  ///电话号码编辑框
  Widget verifyCodeEdit({TextEditingController? editController}) {
    return Stack(
      alignment: Alignment.centerLeft,
      children: <Widget>[
        Container(
          alignment: Alignment.center,
          height: 48.h,
          width: 1.sw,
          margin: EdgeInsets.symmetric(horizontal: 40.w),
          child: TextFormField(
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              LengthLimitingTextInputFormatter(6) ///限制长度
            ],
            cursorColor: BizColors.mainLineGradientSecond,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
            controller: editController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 12.w),
              filled: true,
              fillColor: BizColors.rgb100fafafa,
              hintText: LanStrings.tipInputVerifyCode.tr,
    /*                suffixIcon: phoneSuffixIcon(controller.accountEditController.text.isNotEmpty, () {
                controller.clearPhoneEdit();
              }),*/
              hintStyle: TextStyle(fontSize: 16.sp, color: BizColors.rgb100d1d8e1),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(29), borderSide: BorderSide.none),),
            onChanged: (value) {
              // controller.onPhoneEditContextChanged(value);
            },
            onSaved: (value) {
              // strUserPhone = value;
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '';
              }
              return null;
            },
          ),
        ),
        Positioned(right:40.w,child: _getVerifyCodeBtn()),
      ],
    );
  }
  
  ///获取验证码按钮
  Widget _getVerifyCodeBtn() {
    return GetBuilder<LoginMainController>(
      id: "codeCountDown",
      builder: (_) {
        return TextButton(
            onPressed: controller.getVerifyCodeBtn,
            style: ButtonStyle(
              overlayColor: WidgetStateProperty.all(Colors.transparent),
            ),
            child: Text(controller.strTextCodeDesc,
                style: TextStyle(color: controller.bTimeUp ? BizColors.rgb10057b7fe : Colors.black12, fontSize: 16.sp)));
      },
    );
  }

  /// 抽离的隐私协议部分
  Widget _buildPrivacyAgreement() {
    return GetBuilder<LoginMainController>(
      id: "provState",
      builder: (_) {
        return GestureDetector(
          onTap: () {
            controller.onPriCheck();
          },
          child: SizedBox(
              width: 1.sw,
              height: 32.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  InkWell(
                        onTap: () {
                          controller.onPriCheck();
                        },
                        child: SizedBox(width: 32,height: 32,child: controller.bChecked
                            ? const Icon(
                          Icons.check_circle,
                          color: Colors.blue,
                          size: 18,
                        )
                            : const Icon(
                          Icons.radio_button_unchecked,
                          color: Colors.blue,
                          size: 18,
                        ),)),
                    SizedBox(width: 8.w,),
                  Text("我已阅读并同意", style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3)),
                    InkWell(
                        onTap: () {
                          WebViewUtil.jumpToCommonWebPage(context, ApiReqUrl.userPolicy);
                        },
                        child: Text("《用户协议》", style: TextStyle(fontSize: 12.sp, color: BizColors.rgb10057b7fe))),
                    Text("、", style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3)),
                    InkWell(
                        onTap: () {
                          WebViewUtil.jumpToCommonWebPage(context, ApiReqUrl.userPrivacy);
                        },
                        child: Text("《隐私政策》", style: TextStyle(fontSize: 12.sp, color: BizColors.rgb10057b7fe))),
                  ],
              )));
    },);
// 隐私协议的文本部分
  }
}