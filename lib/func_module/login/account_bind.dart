import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';


class AccountBindPage extends StatefulWidget {
  final String strTip;
  final bool bForceUpdate;
  final String strPackageUrl;
  const AccountBindPage({super.key, this.strTip = "微信账号已绑定，提现权限已开启～", this.bForceUpdate = false, this.strPackageUrl = ""});

  @override
  AccountBindPageState createState() => AccountBindPageState();
}

class AccountBindPageState extends State<AccountBindPage> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: widget.bForceUpdate ? false : true,
      child: Center(
        child: Container(
          width: 280.w,
          height: 280.w,
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFFB3E5FC), // 顶部浅蓝
                Color(0xFFF3E5F5), // 中部浅紫
                Color(0xFFFFFFFF), // 底部白色
              ],
              stops: [
                0.0, 0.6, 1.0, // 渐变延伸到 60% 高度，然后平滑过渡到白色 // 控制渐变在 40% 高度结束，后面保持白色
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 40.h,),
              Text("绑定成功",style: TextStyle(fontSize: 24.sp,color: BizColors.rgb1000c1018,fontFamily: "ALiMaMa_Bold"),),
              SizedBox(height: 46.h,),
              _updateTipDesc(widget.strTip),
              SizedBox(height: 6.h,),
              _operateBtn(widget.bForceUpdate, strUrl: widget.strPackageUrl),
            ],
          ),
        ),
      ),
    );
  }

  Widget _operateBtn(bool bForceUpdate, {String strUrl = ""}) {
    return SizedBox(
      width: 1.sw,
      height: 41.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // bForceUpdate
          //     ? const SizedBox.shrink()
          //     : CustomButtonView(
          //   text: "暂不更新",
          //   bIsDefault: false,
          //   disabledBgColors: const [BizColors.rgb100d1d8e1,BizColors.rgb100d1d8e1],
          //   disableTextColor: BizColors.rgb1000c1018,
          //   height: 41.h,
          //   width: 122.w,
          //   onTap: () {
          //     Get.back();
          //   },
          // ),
          // bForceUpdate
          //     ? const SizedBox.shrink()
          //     : SizedBox(width: 16.w,),
          CustomButtonView(
            text: "确定",
            width: 168.w,
            height: 41.h,
            onTap: () {
              onUpdate(strUrl);
            },
          ),
        ],
      ),
    );
  }

  Future<void> onUpdate(String strUrl) async {
    Get.back();
    // bool bRet = await ToolsUtil.launchMyUrlString(strUrl);
    // if (!bRet) {
    //   ToolsUtil.launchMyUrlString(strUrl);
    // }
  }

  Widget _updateTipDesc(String strDesc) {
    return SizedBox(width: 260.w,
      height: 90.h,
      child: SingleChildScrollView(child: Text(strDesc.replaceAll(r'\n', '\n'), style: TextStyle(fontSize: 14.sp, height: 1.8,color: BizColors.rgb1000c1018),),),
    );
  }

}


class CustomThreeSectionWidget extends StatefulWidget {
  final Widget? middleContent;
  final Widget? bottomContent;
  final double topHeight;
  final double bottomHeight;
  final double minTotalHeight;
  final double maxTotalHeight;
  final double widgetWidth;
  final String? strText;

  const CustomThreeSectionWidget({
    super.key,
    this.middleContent,
    this.bottomContent,
    this.topHeight = 130,
    this.bottomHeight = 65,
    this.minTotalHeight = 300,
    this.maxTotalHeight = 520,
    this.widgetWidth = 320,
    this.strText,
  });

  @override
  CustomThreeSectionWidgetState createState() =>
      CustomThreeSectionWidgetState();
}

class CustomThreeSectionWidgetState extends State<CustomThreeSectionWidget> {
  late double topHeight;
  late double bottomHeight;
  late double minTotalHeight;
  late double maxTotalHeight;
  late double minMiddleHeight;
  late double maxMiddleHeight;

  @override
  void initState() {
    super.initState();
    topHeight = widget.topHeight;
    bottomHeight = widget.bottomHeight;
    maxTotalHeight = widget.maxTotalHeight;
    minTotalHeight = widget.minTotalHeight;
    minMiddleHeight = minTotalHeight - topHeight - bottomHeight;
    maxMiddleHeight = maxTotalHeight - topHeight - bottomHeight;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return ConstrainedBox(
            constraints:  BoxConstraints(minHeight: minTotalHeight, maxHeight: maxTotalHeight,),
            child: SizedBox(
              width: widget.widgetWidth,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  /// 顶部
                  Container(
                    height: topHeight,
                    decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.newVersionTop),fit: BoxFit.fill)),
                    alignment: Alignment.center,
                    child: Stack(alignment: Alignment.centerLeft,children: [
                      Positioned(bottom: 28,left: 50,child: Text(widget.strText ?? "",style: TextStyle(fontSize: 20.sp,color: Colors.white),))
                    ],),
                  ),

                  /// 中部内容区域（关键处理）
                  widget.middleContent != null
                      ? Flexible(
                    child: LayoutBuilder(
                      builder: (context, innerConstraints) {
                        return SingleChildScrollView(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(minHeight: minMiddleHeight, maxHeight: maxMiddleHeight),
                            child: Container(
                              decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.newVersionMid), fit: BoxFit.fill,),),
                              padding: const EdgeInsets.all(12.0),
                              child: widget.middleContent!,
                            ),
                          ),
                        );
                      },
                    ),
                  )
                      : Container(
                    height: minMiddleHeight,
                    decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.newVersionMid), fit: BoxFit.fill,),),
                  ),

                  // 底部
                  Container(
                      height: bottomHeight,
                      decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.newVersionBot),fit: BoxFit.fill)),
                      alignment: Alignment.center,
                      child: Stack(alignment: Alignment.topCenter,children: [
                        widget.bottomContent ?? const SizedBox.shrink(),
                      ],)
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
