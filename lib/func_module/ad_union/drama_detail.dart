import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter/widgets.dart'; // Add this import for WidgetsBindingObserver
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_ad_config.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/ad_union/unlock_dialog.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/forground_service/forground_servcie.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_csj/csj_union_util.dart';
import 'package:flutter_common_base/util/util_csj/util_unlock_dialog/unlock_dlg_manage.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:flutter_common_base/util/util_routes/route_manager.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'ui_utils.dart';

/// 短剧页面
class DramaDetail extends StatefulWidget {
  DramaDetail({super.key, required this.drama, this.bCollect = false});
  final Drama drama;
  bool bCollect;

  @override
  _VideoPage2State createState() => _VideoPage2State();
}

class _VideoPage2State extends State<DramaDetail> with WidgetsBindingObserver {
  late ProVideoController videoController;
  int currentIndex = 1; // 当前已播放的集数
  int newIndex = 1; // 当前最新的集数
  bool bFav = false;
  int nCurDuration = 0;

  int nFreeSet = 1;
  int unLockCnt = 10;
  int nRecommendType = 1;

  ///唯一标识
  String strExtraData = "";

  ///防抖标识
  int _lastUnlockClickTime = 0;
  final int nMaxInterval = 1500;

  @override
  void initState() {
    super.initState();

    ///初始化参数
    bFav = widget.bCollect;
    WidgetsBinding.instance.addObserver(this); // Add observer
    videoController = ProVideoController();

    ///赋值
    CsjUnionUtil.videoController = videoController;

    ///设置模式和播放免费集数
    _processAdParam();

    ///不让熄屏
    ForGroundServiceUtil.wakeLockOn();
    newIndex = widget.drama.index;
    queryDramaFavState();
    getUnlockStatus();
  }

  void _processAdParam() {
    nFreeSet = ToolsUtil.roundToInt(widget.drama.total *
        (CsjUnionUtil.adSettings.videoSettings?.dFreeEpisodes ?? 0.2));
    unLockCnt = CsjUnionUtil.adSettings.videoSettings?.nUnlockEpisodes ?? 10;
    nRecommendType =
        CsjUnionUtil.adSettings.videoSettings?.adEpisodes?.nRecommendType ?? 1;
  }

  ///初始化的时候查询一下，把结果赋值过去
  Future<void> queryDramaFavState() async {
    if (!UserInfoUtil.isLogin()) {
      return;
    }

    Map<String, dynamic> mapResult =
        await ApiReqInterface.queryDataState(2, widget.drama.id);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    List fav = mapResult["data"] ?? [];
    if (fav.isEmpty) {
      bFav = false;
    } else {
      bFav = true;
    }

    ///查询完结果就设置
    nativeFavCall(bFav, -1);
  }

  Future<void> _setFavStateFav(int nViewId) async {
    Map<String, dynamic> mapResult =
        await ApiReqInterface.actionExeCute(2, 1, widget.drama.id);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    bFav = true;
    // videoController.setDramaFavStatus(bFav, nViewId);
    commonEventBus.fire(
        MyHisOrColTabChgEvent(nIndex: 1, strSourceId: '${widget.drama.id}'));
  }

  void safeMountRefresh() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _setFavStateUnFav(int nViewId) async {
    Map<String, dynamic> mapResult =
        await ApiReqInterface.actionECancel(2, widget.drama.id);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    bFav = false;
    // videoController.setDramaFavStatus(bFav, nViewId);
    commonEventBus.fire(
        MyHisOrColTabChgEvent(nIndex: 1, strSourceId: '${widget.drama.id}'));
  }

  Future<void> addHistoryItem(int nSourceId, int nCurJi, int nPro) async {
    ApiReqInterface.syncHistoryItem(nSourceId, nCurJi, nPro);
  }

  @override
  void dispose() {
    videoController.dispose();
    WidgetsBinding.instance.removeObserver(this); // Remove observer
    // ///熄屏
    // ForGroundServiceUtil.wakeLockOff();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.hidden:
        {
          // _onHidePause();
        }
        break;
      case AppLifecycleState.resumed:
        {
          // videoController.resume();
        }
        break;
      default:
        break;
    }
  }

  void _onHidePause() {
    if (!RouteUtil.routeTrace.contains(BizStrings.dramaDetailPageRoute)) {
      return;
    }

    ///第一个是
    if (RouteUtil.routeTrace.first != BizStrings.dramaDetailPageRoute) {
      videoController.resume();
    }
  }

  Future<void> onOpenDetail(Drama drama) async {
    // videoController.pause();
    videoController.openDramaGallery();
  }

  Future<void> onOpenSelfDetailPage(Drama drama) async {
    videoController.pause();
    await SingleRoutePageManage.routeToSelfDefineDramaDetail(drama);
    videoController.resume();
  }

  Future<void> nativeFavCall(bool bFav, int nViewId) async {
    if (bFav) {
      _setFavStateFav(nViewId);
    } else {
      _setFavStateUnFav(nViewId);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽高
    Size size = UiUtils.screenSize(context);
    return Scaffold(
      body: Stack(
        children: [
          DramaWidget(
            controller: videoController,
            width: size.width,
            height: size.height,
            id: widget.drama.id,
            index: newIndex,
            groupId: '${widget.drama.groupId ?? 24596}',
            hideTopInfo: true,
            // strCoverUrl: widget.drama.coverImage,
            hideBottomInfo: true,
            hideLikeButton: true,
            hideFavorButton: true,
            hideMore: true,
            // total: widget.drama.total,
            detailFree: nFreeSet,
            unlockAdMode: nRecommendType, // 0:SDK 广告解锁 1：自定义广告解锁
            unlockCount: unLockCnt,
            // bCollectDrama: bFav,
            // favStatusListener: FavDramaListener(favSet: nativeFavCall),
            // selfDetailListener:
            // SelfDetailListener(onOpenSelfDetail: onOpenSelfDetailPage),
            // theaterListener: TheaterListener(
            // onOpenDetail: onOpenDetail,
            // ),
            unlockFlowListener: UnlockFlowListener(
              unlockFlowStart: (data) {
                var unlockDrama = Drama.fromJson(data);
                // 解锁流程开始
                print(
                    'VideoPlayListener unlockFlowStart index:${unlockDrama.index} title:${unlockDrama.title}');
                showUnlockDialog(unlockDrama);
              },
              unlockFlowEnd: (int? errCode, String? errMsg) {
                // 解锁流程结束
                print(
                    'VideoPlayListener unlockFlowEnd: errCode:$errCode errMsg:$errMsg');
                if (errCode != 200) {
                  // 解锁失败
                  backIndex();
                } else {
                  /// 解锁成功
                  // unLockDrama(widget.drama, unLockCnt, false);
                }
              },
              showCustomAd: () {
                // 显示自定义广告
                print('VideoPlayListener showCustomAd');
                showCustomAd();
              },
            ),
            videoPlayListener: VideoPlayListener(
              onDJXPageChange: (Drama drama) {
                // 视频切换
                print(
                    'VideoPlayListener onDJXPageChange: ${drama.id}:${drama.title}:${drama.index} duration:${drama.duration}');
              },
              onDJXVideoPlay: (Drama drama) {
                // 视频播放开始
                print(
                    'VideoPlayListener onDJXVideoPlay: ${drama.id}:${drama.title}:${drama.index} duration:${drama.duration}');
                currentIndex = drama.index;
                videoController.resume(); //
                setState(() {
                  newIndex = currentIndex;
                });
                addHistoryItem(drama.id, currentIndex, nCurDuration);
              },
              onDJXVideoPause: (Drama drama) {
                // 视频暂停
                print(
                    'VideoPlayListener onDJXVideoPause:  ${drama.id}:${drama.title}:${drama.index}');
                addHistoryItem(drama.id, currentIndex, nCurDuration);
              },
              onDJXVideoContinue: (Drama drama) {
                // 视频继续
                print(
                    'VideoPlayListener onDJXVideoContinue: ${drama.id}:${drama.title}:${drama.index}');
              },
              onDJXVideoCompletion: (Drama drama) {
                // 视频播放完成
                print(
                    'VideoPlayListener onDJXVideoCompletion: ${drama.id}:${drama.title}:${drama.index}');
              },
              onDJXVideoOver: (Drama drama) {
                // 视频结束
                print(
                    'VideoPlayListener onDJXVideoOver: ${drama.id}:${drama.title}:${drama.index}');
              },
              onVideoClose: () {
                // 视频关闭
                print('VideoPlayListener onDJXVideoClose');
              },
              onSeekTo: (duration) {
                // 拖动进度
                print("VideoPlayListener onDJXSeekTo:$duration");
                nCurDuration = duration.toInt();
                addHistoryItem(widget.drama.id, currentIndex, nCurDuration);
              },
              onDurationChange: (duration) {
                // 播放进度
                print("VideoPlayListener onDurationChange:$duration");
                nCurDuration = duration.toInt();
              },
              onError: (int? errCode, String? errMsg) {
                // 播放错误
                print("VideoPlayListener onError:$errCode:$errMsg");
              },
            ),
          ),
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: AppBar(
                backgroundColor: Colors.transparent,
                centerTitle: true,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
                title: Column(
                  children: [
                    Text(
                      '第 $newIndex 集',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                      ),
                    ),
                    Text(
                      widget.drama.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    )
                  ],
                ),
                actions: [
                  // //选集
                  // IconButton(
                  //   icon: const Icon(Icons.list, color: Colors.white),
                  //   onPressed: () {
                  //     openDramaGallery();
                  //   },
                  // ),
                  // //更多和选集
                  // IconButton(
                  //   icon: const Icon(Icons.more_horiz, color: Colors.white),
                  //   onPressed: () {
                  //     openMoreSetting();
                  //   },
                  // ),
                ],
              )),

          ///收藏按钮
          // Positioned(bottom: 240, right: 15, child: _favBtn()),
        ],
      ),
    );
  }

  /// 返回上一集
  void backIndex() {
    videoController.setCurrentIndex(currentIndex);
  }

  /// 解锁短剧
  Future<void> unLockDrama(Drama drama, int nLockCnt, bool cancel) async {
    videoController.unLock(drama.id, nLockCnt, cancel);
  }

  /// 自定义广告解锁短剧
  /// 设置展示价格
  Future<void> setCustomAdOnShow() async {
    videoController.setCustomAdOnShow("1000");
    print('VideoPlayListener setCustomAdOnShow 1000');
  }

  /// 自定义广告解锁短剧
  /// 设置激励结果
  Future<void> setCustomAdOnReward(bool verify) async {
    videoController.setCustomAdOnReward(verify, extraData: {"key": "value"});
    print('VideoPlayListener setCustomAdOnReward $verify');
  }

  /// 调用自定义的广告
  Future<void> showCustomAd() async {
    // 1、先设置广告价格
    String strUid = '${UserInfoUtil.getUserInfo().id}';
    UnLockDlgMan.listDlg.add(strExtraData);
    bool bRet = await CsjUnionUtil.showRewardVideoAds(
        '${CsjUnionUtil.adSettings.videoSettings?.adEpisodes?.nId ?? BizAdConfig.jiLiAdId}',
        userId: strUid,
        customData: strExtraData);
    // setCustomAdOnShow();
    // await Future.delayed(const Duration(seconds: 3));
    // // 2、设置激励结果
    // setCustomAdOnReward(true);
  }

  /// 显示 Dialog
  Future<void> showUnlockDialog(Drama drama) async {
    strExtraData =
        '${BizAdConfig.strAdSrcDrama}${BizStrings.adExtraDiv}${ToolsUtil.genTimeNowMilliseconds()}';
    showDialog(
      context: context,
      barrierDismissible: false,
      routeSettings: const RouteSettings(name: Routes.routeUnLockDlg),
      builder: (context) => UnlockDialog(
        title: '${drama.title} ',
        indexText: '第${drama.index}集',
        unlockText:
            '免费解锁 ${CsjUnionUtil.adSettings.videoSettings?.nUnlockEpisodes} 集',
        tipText: "解锁还能提高红包奖励哦~",
        strExtraData: strExtraData,
        onClose: () {
          Navigator.pop(context);
          unLockDrama(drama, unLockCnt, true);
        },
        onUnlock: () {
          final now = DateTime.now().millisecondsSinceEpoch;
          if (now - _lastUnlockClickTime >= nMaxInterval) {
            _lastUnlockClickTime = now;
            unLockDrama(drama, unLockCnt, false);
          }
        },
      ),
    );
  }

  /// 获取解锁状态列表
  Future<void> getUnlockStatus() async {
    List<UnlockStatus> unlockStatus = await FlutterAdcontent.getEpisodesStatus(
        id: widget.drama.id, freeSet: nFreeSet);
    for (var status in unlockStatus) {
      print('unlockStatus: ${status.toJson()}');
    }
  }

  /// 打开选集面板
  Future<void> openDramaGallery() async {
    videoController.openDramaGallery();
  }

  /// 打开更多弹窗
  Future<void> openMoreSetting() async {
    videoController.openMoreDialog();
  }
}
