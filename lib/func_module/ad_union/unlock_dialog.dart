import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_ad_config.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:flutter_common_base/util/util_routes/route_manager.dart';
import 'package:flutter_gromore_ads/event/ad_event.dart';
import 'package:get/get.dart';

/// 解锁弹窗
class UnlockDialog extends StatefulWidget {
  final String title;
  final String indexText;
  final String unlockText;
  final String tipText;
  final VoidCallback onUnlock;
  final VoidCallback onClose;
  final String strExtraData;

  const UnlockDialog({
    Key? key,
    required this.title,
    required this.indexText,
    required this.unlockText,
    required this.tipText,
    required this.onUnlock,
    required this.onClose,
    this.strExtraData = "",
  }) : super(key: key);

  @override
  UnlockDialogState createState() => UnlockDialogState();
}

class UnlockDialogState extends State<UnlockDialog> {
  ///广告播放完成回调通知
  StreamSubscription? eventAdReward;
  ///广告行为
  StreamSubscription? eventAdAction;
  ///窗口是否能关闭
  bool canClose = false;
  ///是否能点击
  bool bCanClickLoad = true;
  ///广告的状态
  List<String> adLoadState = [AdEventAction.onAdSkip,AdEventAction.onAdClosed];
  ///是否可以点击加载广告
  List<String> adLoadStateClick = [
    AdEventAction.onAdError,
    AdEventAction.onAdLoaded,
    AdEventAction.onAdExposure,
    AdEventAction.onAdSkip,
    AdEventAction.onAdClosed,
    AdEventAction.onAdComplete,
  ];
  @override
  void initState() {
    super.initState();
    ///监听激励事件的回调
    eventAdReward = commonEventBus.on<AdRewardReceivedEvent>().listen(adRewardGetEvent);
    eventAdAction = commonEventBus.on<AdSelfActionEvent>().listen(adActionEvent);
  }

  @override
  void dispose() {
    eventAdReward?.cancel();
    eventAdAction?.cancel();
    super.dispose();
  }

  Future<void> adRewardGetEvent(AdRewardReceivedEvent event) async {
    if (event.strExtraData != widget.strExtraData) {
      canClose = false;
    } else {
      canClose = true;
    }
  }

  Future<void> adActionEvent(AdSelfActionEvent event) async {
    if (adLoadStateClick.contains(event.strAction)) {
      bCanClickLoad = true;
    } else {
      bCanClickLoad = false;
    }
    if( !adLoadState.contains(event.strAction)) {
      return;
    }

    if (canClose && RouteUtil.routeTrace.contains(Routes.routeUnLockDlg)) {
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      // backgroundColor: const Color(0xBD000000),
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: PopScope(
        // 设置无法返回来关闭弹窗
        canPop: false,
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Image.asset(AssetsRes.unlockAdBg,),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(height: 68),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFCEBB8),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        widget.indexText,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFCEBB8),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AssetsRes.unlockAdUnLocked,
                        width: 26,
                        height: 26,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        widget.unlockText,
                        style: const TextStyle(
                          fontSize: 26,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFCEBB8),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Text(
                    widget.tipText,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFFFAEFAE),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 20),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      if (!bCanClickLoad) {
                        return;
                      }
                      widget.onUnlock.call();
                      bCanClickLoad = false;
                    },
                    child: Image.asset(
                      AssetsRes.unlockAdBtnUnlock,
                      width: 200,
                      height: 62,
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 20,
              child: InkWell(
                onTap: widget.onClose,
                borderRadius: BorderRadius.circular(20),
                child: CircleAvatar(
                  backgroundColor: Colors.transparent,
                  radius: 20,
                  child: Image.asset(
                    AssetsRes.unlockAdClose,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
