import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_common_base/common/biz_values/biz_ad_config.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_csj/csj_union_util.dart';
import 'package:flutter_common_base/util/util_csj/util_unlock_dialog/unlock_dlg_manage.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

/// 页面
class NovelHomePage extends StatefulWidget {
  const NovelHomePage({Key? key}) : super(key: key);

  @override
  _NovelHomePageState createState() => _NovelHomePageState();
}

class _NovelHomePageState extends State<NovelHomePage> {
  late ProVideoController controller;

  String adCodeId = BizAdConfig.novelId;

  @override
  void initState() {
    controller = ProVideoController();
    CsjUnionUtil.novelController = controller;
    adCodeId = '${CsjUnionUtil.adSettings.storySettings?.adChapters?.nId ?? BizAdConfig.novelId}';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          // 获取 body 的大小
          var bodyWidth = constraints.maxWidth;
          var bodyHeight = constraints.maxHeight;
          print(
              'NovelHomePage build constraints: $constraints bodyWidth:$bodyWidth bodyHeight:$bodyHeight');
          return NovelStoryWidget(
            controller: controller,
            width: bodyWidth,
            height: bodyHeight,
            setTopOffset: 40,
            canPullRefresh: true,
            recPageSize: 9,
            rewardCodeId: adCodeId, // 仅 Android 支持
            rewardAdMode: BizAdConfig.rewardModeSelfDef,
            novelStoryHomeListener: NovelStoryHomeListener(
              onItemClick: (novelStory) {
                // iOS 没有这个事件
                print('NovelHomePage onItemClick: ${novelStory.toJson()}');
              },
            ),
            unlockFlowListener: UnlockFlowListener(
              unlockFlowStart: (data) {
                var novel = NovelStory.fromJson(data); // iOS 获取不到
                print('NovelHomePage unlockFlowStart title: ${novel.title}');
              },
              unlockFlowEnd: (errCode, errMsg) {
                print(
                    'NovelHomePage unlockFlowEnd errCode:$errCode errMsg:$errMsg');
              },
              showCustomAd: () {
                print('NovelHomePage showCustomAd');
                showCustomAd();
              },
            ),
          );
        },
      ),
    );
  }

  /// 自定义广告解锁短剧
  /// 设置展示价格
  Future<void> setCustomAdOnShow() async {
    controller.setCustomAdOnShow("1000");
    print('VideoPlayListener setCustomAdOnShow 1000');
  }

  /// 自定义广告解锁短剧
  /// 设置激励结果
  Future<void> setCustomAdOnReward(bool verify) async {
    controller.setCustomAdOnReward(verify, extraData: {"key": "value"});
    print('VideoPlayListener setCustomAdOnReward $verify');
  }

  /// 调用自定义的广告
  Future<void> showCustomAd() async {
    String strUid = '${UserInfoUtil.getUserInfo().id}';
    String strExtraData = '${BizAdConfig.strAdSrcNovel}${BizStrings.adExtraDiv}${ToolsUtil.genTimeNowMilliseconds()}';
    UnLockDlgMan.listDlg.add(strExtraData);
    bool bRet = await CsjUnionUtil.showRewardVideoAds(adCodeId, userId: strUid, customData: strExtraData);
    if( !bRet ) {
      CsjUnionUtil.showRewardVideoAds(adCodeId, userId: strUid, customData: strExtraData);
    }
  }
}
