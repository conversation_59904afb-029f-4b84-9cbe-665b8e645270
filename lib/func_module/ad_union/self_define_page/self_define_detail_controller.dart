import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_adcontent/entity/drama.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/func_module/ad_union/self_define_page/jvji_tabbar_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_set_pwd_page.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:get/get.dart';

class SelfDefineDetailController extends NormalPageControllerBase with GetSingleTickerProviderStateMixin {
  Drama? drama;
  List<String> jiJiRangeString = [];
  late TabController tabController;
  List<Widget> tabBarView = [];
  int nCurIndex = 1;
  List<VideoData> listSearchSubList = [];
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      drama = Get.arguments["drama"];
    }
    _initJvJiTab();
    _getLikeable();
  }

  Future<void> _getLikeable() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getRankingRandom();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess ) {
      update(["searchList"]);
      return;
    }
    List items = mapResult["data"] ?? [];
    for( var item in items)  {
      VideoData dataInfo = VideoData.fromJson(item);
      listSearchSubList.add(dataInfo);
    }
    update(["searchList"]);
  }

  void cutList(List<VideoData> src, List<VideoData> des, {int nMaxLimit = 3}) {
    des.clear();
    if (src.length <= nMaxLimit) {
      des.addAll(src);
    } else {
      des.addAll(src.sublist(0, nMaxLimit));
    }
  }

  void _initJvJiTab() {
    jiJiRangeString = divideRange(1, drama?.total ?? 1);
    tabController = TabController(length: jiJiRangeString.length, vsync: this);
    for (int nIndex = 0; nIndex < jiJiRangeString.length; nIndex++) {
      String strJvJi = jiJiRangeString[nIndex];
      tabBarView.add(KeepAliveWrapper(
          child: JvJiTabBarListView(strJvJiDesc: strJvJi, nCurIndex: nCurIndex, dramaInfo: drama)));
    }
  }


  @override
  void onClose() {
    super.onClose();
  }

  List<String> divideRange(int start, int end) {
    int total = end - start + 1;

    if (total <= 0) return [];

    if (total == 1) return ["$start"];
    if (total == 2) return ["$start", "$end"];

    int partSize = total ~/ 3;
    int remainder = total % 3;

    List<int> boundaries = [];
    int current = start;

    for (int i = 0; i < 3 && current <= end; i++) {
      int thisSize = partSize + (i < remainder ? 1 : 0);
      int rangeEnd = (current + thisSize - 1).clamp(current, end);
      boundaries.add(current);
      boundaries.add(rangeEnd);
      current = rangeEnd + 1;
    }

    List<String> result = [];
    for (int i = 0; i < boundaries.length; i += 2) {
      int s = boundaries[i];
      int e = boundaries[i + 1];
      if (s == e) {
        result.add("$s");
      } else {
        result.add("$s-$e");
      }
    }

    return result;
  }

}