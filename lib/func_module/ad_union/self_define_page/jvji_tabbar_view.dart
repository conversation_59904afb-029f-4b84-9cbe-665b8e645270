import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_adcontent/entity/drama.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class JvJiTabBarListView extends StatefulWidget {

  String strJvJiDesc = "";
  int nCurIndex = 0;
  Drama? dramaInfo;

  JvJiTabBarListView({super.key, this.strJvJiDesc = "1-1", this.nCurIndex = 1,required this.dramaInfo});
  @override
  JvJiTabBarListViewState createState() => JvJiTabBarListViewState();
}

class JvJiTabBarListViewState extends State<JvJiTabBarListView> {


  int nLen = 0;
  List<String> listItemIndex = [];

  @override
  void initState() {
    int nStart = 1;
    int nEnd = 1;
    List<String> listItem = widget.strJvJiDesc.split('-');
    if (listItem.length == 1) {
      nLen = 1;
    } else {
      nEnd = ToolsUtil.safeParseInt(listItem[1]);
      nStart = ToolsUtil.safeParseInt(listItem[0]);
      nLen = nEnd - nStart + 1;
    }

    for(int nIndex = nStart;nIndex <= nEnd;nIndex++) {
    listItemIndex.add('$nIndex');
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: 48.h,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) {
            String strIndex = listItemIndex[index];
            return InkWell(
              onTap: () {
                widget.dramaInfo?.index = ToolsUtil.safeParseInt(strIndex);
                SingleRoutePageManage.routeDramaDetailPage(widget.dramaInfo!);
              },
              child: Container(
                width: 48.w,
                height: 48.w,
                alignment: Alignment.center,
                margin: EdgeInsets.only(right: 8.w),
                decoration: const BoxDecoration(color: BizColors.rgb10ffffff, borderRadius: BorderRadius.all(Radius.circular(8))),
                child: Text(strIndex, style: TextStyle(fontSize: 14.sp, color: BizColors.rgb80ffffff),),
              ),
            );
          },
          itemCount: listItemIndex.length,
          padding: EdgeInsets.zero,
        ));
  }
}