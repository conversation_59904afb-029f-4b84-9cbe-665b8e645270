import 'dart:ui';

import 'package:expandable_text/expandable_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/ad_union/self_define_page/self_define_detail_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/material_indicator.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/sliver_grid_view_fixed_size.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SelfDefineDetailPage extends NormalPageViewBase<SelfDefineDetailController> {
  SelfDefineDetailPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  YSelfDefineDetailPageState createState() => YSelfDefineDetailPageState();
}

class YSelfDefineDetailPageState extends NormalPageBaseViewState<SelfDefineDetailController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "",
      bLightLeading: true
    );
  }


  @override
  Widget mainBg() {
    return Stack(
      children: [
        Container(width: 1.sw, height: 1.sh, color: BizColors.rgb10019113D),
        ShaderMask(
          shaderCallback: (Rect bounds) {
            return const RadialGradient(
              colors: [BizColors.rgb10019113D, Colors.transparent],
              stops: [0.8, 1.0],
              center: Alignment.center,
              radius: 1,
            ).createShader(bounds);
          },
          blendMode: BlendMode.dstIn,
          child: GetPlatform.isIOS?  _iosBlur() : _androidBlur(),
        )
      ],
    );
  }

  Widget _iosBlur() {
    return Opacity(
      opacity: 0.65,
      child: ImageFiltered(
        imageFilter: ImageFilter.compose(
            outer: ImageFilter.blur(sigmaX: 180, sigmaY: 180),
            inner: ImageFilter.matrix(Matrix4.diagonal3Values(1.1, 1.1, 1).storage)),
        child: ImageFiltered(
          imageFilter: ImageFilter.compose(
              outer: ImageFilter.blur(sigmaX: 180, sigmaY: 180),
              inner: ImageFilter.matrix(Matrix4.diagonal3Values(1.1, 1.1, 1).storage)),
          child: SizedBox(
            width: 1.sw,
            height: 336.h,
            child: Image.network(controller.drama?.coverImage ?? "", width: 1.sw, height: 336,fit: BoxFit.fill,),
          ),
        ),
      ),
    );
  }

  Widget _androidBlur() {
    return ImageFiltered(
      imageFilter: ImageFilter.compose(
          outer: ImageFilter.blur(sigmaX: 120, sigmaY: 120),
          inner: ImageFilter.matrix(Matrix4.diagonal3Values(1.1, 1.1, 1).storage)),
      child: SizedBox(
        width: 1.sw,
        height: 336.h,
        child: Image.network(controller.drama?.coverImage ?? "", width: 1.sw, height: 336,fit: BoxFit.fill,),
      ),
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: NestedScrollView(headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          SliverToBoxAdapter(child: SizedBox(height: 12.h)),
          SliverToBoxAdapter(child: _topInfo()),
          SliverToBoxAdapter(child: SizedBox(height: 28.h)),
        ];
      }, body: _bodyViews()),
    );
  }
  
  
  Widget _bodyViews() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _introduce(),
        SizedBox(height: 28.h,),
        _dramaJvJiDetail(),
        SizedBox(height: 28.h,),
        Text("猜你喜欢",style: TextStyle(fontSize: 14.sp,color: Colors.white,fontWeight: FontWeight.w500),),
        SizedBox(height: 12.h),
        Expanded(child: _rankContentLimit()),
        // SizedBox(height: BizValues.bottomMargin)
      ],
    );
  }


  Widget _topInfo() {
    return SizedBox(height: 93.h,child: Row(
      children: [
        ImageLoadView(controller.drama?.coverImage ?? "",width: 66.w,height: 93.h,radius: 8.r,),
        SizedBox(width: 20.w,),
        SizedBox(height: 93.h,child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ConstrainedBox(constraints: BoxConstraints(maxWidth: 240.w),child: Text(controller.drama?.title ?? "",overflow:TextOverflow.ellipsis,style: TextStyle(fontSize: 16.sp,color: Colors.white),),),
            SizedBox(height: 8.h,),
            Text("共${controller.drama?.total ?? 1}集",style: TextStyle(fontSize: 14.sp,color: Colors.white),),
            SizedBox(height: 12.h,),
            _labelItemInfo(Text(controller.drama?.type ?? "",style: TextStyle(fontSize: 12.sp,height:1.1,color: Colors.white),)),
          ],
        ),),
      ],
    ),);
  }

  ///简介
  Widget _introduce() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(alignment:Alignment.centerLeft,child: Text("简介",style: TextStyle(fontSize: 14.sp,color: Colors.white,fontWeight: FontWeight.w500),)),
        SizedBox(height: 12.h,),
        ExpandableText(
          controller.drama?.desc ?? "",
          style: const TextStyle(fontSize: 12, color: BizColors.rgb60ffffff),
          maxLines: 4,
          collapseOnTextTap: true,
          linkEllipsis:false,
          linkStyle: const TextStyle(fontSize: 12,color: BizColors.rgb10057b7fe),
          expandText: "展开",
          collapseText: "收起",
        ),
      ],
    );
  }

  ///剧集
  Widget _dramaJvJiDetail() {
    return SizedBox(
        width: 1.sw,
        height: 150.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            jvJiTip(),
            SizedBox(height: 18.h,),
            ///剧集开始
            _jvJiTab(),
            SizedBox(height: 18.h,),
            _jiJiTabBarView(),
          ],
        ));
  }

  ///猜你喜欢

  Widget _rankContentLimit() {
    return GetBuilder<SelfDefineDetailController>(
      tag: widget.tag,
      id: "searchList",
      builder: (_) {
        return GridView.builder(
            physics: const BouncingScrollPhysics(),
            itemCount: controller.listSearchSubList.length,
            gridDelegate: SliverGridDelegateWithFixedSize(BizValues.itemBlockWidth.w, BizValues.itemBlockHeight.h,
                mainAxisSpacing: BizValues.itemMainCross.w),
            itemBuilder: (BuildContext context, index) {
              VideoData dataInfoItem = controller.listSearchSubList[index];
              return gridItem(dataInfoItem,defTextStyle: const TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: BizColors.rgb100ffffff,
                  fontSize: 13,
                  fontWeight: FontWeight.w500),tagTextStyle: const TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: BizColors.rgb100999999,
                  fontSize: 11,
                  fontWeight: FontWeight.w500));
            });
      },
    );
  }

  Widget _jvJiTab() {
    return SizedBox(
      height: 44.h,
      child: TabBar(
        tabs: controller.jiJiRangeString.map((e) => Text(e, style: TextStyle(fontSize: 14.sp),)).toList(),
        controller: controller.tabController,
        isScrollable: true,
        labelPadding: EdgeInsets.only(right: 28.w),
        tabAlignment: TabAlignment.start,
        labelStyle: TextStyle(fontSize: 14.sp,color: Colors.white),
        unselectedLabelStyle: TextStyle(fontSize: 14.sp,color: BizColors.rgb50ffffff),
        indicator: MaterialIndicator(color: Colors.transparent,height: 1),

      ),
    );
  }

  Widget _jiJiTabBarView() {
    return SizedBox(
      height: 48.h,
      child: TabBarView(
          physics: const NeverScrollableScrollPhysics(),
          controller: controller.tabController,
          children: controller.tabBarView),
    );
  }

  Widget jvJiTip() {
    return SizedBox(
      height: 20.h,child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text("剧集",style: TextStyle(fontSize: 14.sp,color: Colors.white,fontWeight: FontWeight.w500),),
        Text("已完结   共${controller.drama?.total ?? 1}集",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb50ffffff,fontWeight: FontWeight.w500),),
      ],),);
  }

  Widget _labelItemInfo(Widget child,{double maxWidth = 100,double horizonPadding = 8}) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: 20.h, maxHeight:20.h,maxWidth: maxWidth.w),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: horizonPadding.w),
        decoration: const BoxDecoration(color: BizColors.rgb10ffffff, borderRadius: BorderRadius.all(Radius.circular(12))),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            child
          ],
        ),
      ),
    );
  }


  @override
  SelfDefineDetailController createController() {
    return SelfDefineDetailController();
  }
}

