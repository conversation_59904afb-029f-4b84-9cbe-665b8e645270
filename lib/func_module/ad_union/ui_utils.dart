import 'package:flutter/widgets.dart';

/// UI 工具类
class UiUtils {
  /// 获取屏幕宽高
  static Size screenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// 获取屏幕宽度
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// 获取屏幕高度
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// 获取安全距离
  static EdgeInsets safeArea(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// 动态计算宽高比例，主要用于 GridView
  static double childAspectRatio(BuildContext context,
      {double spacing = 10, int crossAxisCount = 2, double itemHeight = 200}) {
    double itemWidth = (screenWidth(context) - spacing) / crossAxisCount;
    return itemWidth / itemHeight;
  }
}
