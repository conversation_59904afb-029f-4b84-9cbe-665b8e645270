import 'package:flutter/material.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/cat_detail_tab_view/cat_detail_implement_view.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:get/get.dart';

class TabVideosController extends GetxController with GetTickerProviderStateMixin{
  ViewLoaderState dataLoadState = ViewLoaderState();
  List<VideoCatData> listCat = [];
  List<Widget> listCatDetailView = [];
  late TabController catTabsControllerVideo;
  @override
  void onInit() {
    super.onInit();
    getCatContents();
  }

  Future<void> getCatContents() async {
    Map<String, dynamic> mapRet = await ApiReqInterface.getVideoCats();
    if (mapRet["code"] != ResponseCodeParse.codeSuccess) {
      dataLoadState.loadState = LoaderState.stateNoData;
      update(["mainInfo"]);
      return;
    }

    List catItems = mapRet["data"] ?? [];
    listCat.clear();
    for (var item in catItems) {
      listCat.add(VideoCatData.fromJson(item));
    }

    if (listCat.isEmpty) {
      dataLoadState.loadState = LoaderState.stateNoData;
      update(["mainInfo"]);
      return;
    }

    catTabsControllerVideo = TabController(length: listCat.length, vsync: this);
    listCatDetailView.clear();
    initCatDetailViews();
    dataLoadState.loadState = LoaderState.stateSucceed;
    update(["mainInfo"]);
  }

  ///点击分类
  Future<void> onCatSel() async {

  }
  
  ///初始化分类下面对应的内容页面
  void initCatDetailViews() {
    for (int nIndex = 0; nIndex < listCat.length; nIndex++) {
      String strGuid = ToolsUtil().genUuId();
      VideoCatData info = listCat[nIndex];
      listCatDetailView.add(KeepAliveWrapper(child: VideosCatDetailView(nId: info.id, isGlobal: true, strTag: strGuid,mapArg: {"catId":info.id},)));
    }
  }
}
