import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SearchBarView extends StatefulWidget {
  @override
  SearchBarViewState createState() => SearchBarViewState();
}

class SearchBarViewState extends State<SearchBarView> {

 StreamSubscription? eventLogin;

 String strHeadImg = "";

 @override
  void initState() {
    super.initState();
    eventLogin = commonEventBus.on<LoginStatusEvent>().listen(loginStatusChange);
    strHeadImg = UserInfoUtil.getUserInfo().strAvatar;
  }

  Future<void> loginStatusChange(LoginStatusEvent event) async {
    strHeadImg = UserInfoUtil.getUserInfo().strAvatar;
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 1.sw,
      height: 44.h,
      child: Row(
        children: [
         ///logo
          _logoLeft(),
          const Spacer(),
         ///搜索框
          searchBox(),
         const Spacer(),
         ///头像
          _headImg(),
        ],
      ),
    );
  }

  Widget _logoLeft() {
  return SizedBox(width: 44.w,height: 44.w,child: ImageLoadView(AssetsRes.logoGrey,width: 44.w,height: 44.w),);
  }

 Widget searchBox() {
   return InkWell(
     onTap: () {
       SingleRoutePageManage.routeToSearchMainPage();
     },
     child: SizedBox(
       height: 32.h,
       width: 247.w,
       child: Stack(
         alignment: Alignment.centerLeft,
         children: [
           // Positioned(left:18.w,child: Image.asset(Res.search, width: 24.w, height: 24.w,)),
           Container(
             height: 32.h,
             width: 247.w,
             decoration: const BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(20))),
           ),
           Positioned(left:16.w,child: Text("请输入内容",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100d1d8e1),)),
           Positioned(right: 16.w, child: Image.asset(AssetsRes.iconSearchBlue, width: 16.w, height: 16.w)),
          ],
       ),
     ),
   );
 }

  Widget _headImg() {
    return InkWell(
        onTap: () {
          if (UserInfoUtil.isLogin()) {
            SingleRoutePageManage.routeMainPage(LanStrings.tabMy);
          } else {
            SingleRoutePageManage.routeToLoginMain();
          }
        },
        child: ImageLoadView(strHeadImg,placeholder: AssetsRes.iconUnLogin, width: 32.w, height: 32.w, shape: BoxShape.circle, /*borderWidth: 1, borderColor: Colors.white*/));
  }

  @override
  void dispose() {
   eventLogin?.cancel();
    super.dispose();
  }
}
