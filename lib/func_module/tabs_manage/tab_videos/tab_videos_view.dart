import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_index/search_index_page_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search_bar/search_bar_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/tab_videos_controller.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/buttons_tabbar.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TabVideosPage extends GetView<TabVideosController> {
  const TabVideosPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TabVideosController>(
      id: "mainInfo",
      builder: (_) {
        return LoaderContainer(
            contentBuilder: () => mainBody(),
            loaderState: controller.dataLoadState);
      },
    );
  }

  Widget mainBody() {
    return SafeArea(
        top: false,
        bottom: false,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            _bgImg(),
            _mainContent(),
          ],
        ));
  }

  ///背景
  Widget _bgImg() {
    return Image.asset(AssetsRes.tabVideoHeadBg,width: 1.sw,height: 336.h,fit: BoxFit.fill,);
  }

  ///主要内容
  Widget _mainContent() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///状态栏的高度
          SizedBox(height: 44.h,),
          ///appbar内容
          _appbarRelContent(),
          ///分类目录
          _catContents(),
          ///主要内容
          Expanded(child: _catDetailViews()),

        ],
      ),
    );
  }

  ///appbar位置的内容
  Widget _appbarRelContent() {
    return SearchBarView();
  }

  ///目录分类
  Widget _catContents() {
    return SizedBox(
        height: 42.h,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            _catInfo(),
            Positioned(
                right: 0,
                top: 11.h,
                child: InkWell(
                  onTap: () {
                    Get.to(()=> SearchPageIndexView(isGlobal: false,tag: ToolsUtil().genUuId(),));
                  },
                  child: SizedBox(
                    width: 16.w,
                    height: 16.w,
                    child: Image.asset(AssetsRes.tabVideoCatMenu,width: 16.w,height: 16.w,fit: BoxFit.fill,),
                  ),
                )),
          ],
        ));
  }

  Widget _catInfo() {
    return GetBuilder<TabVideosController>(
      id: "mainTab",
      builder: (_) {
        return controller.listCat.isNotEmpty
            ? Padding(
                padding: EdgeInsets.only(right: 18.w),
                child: MyCustomTabBar(
                    tabController: controller.catTabsControllerVideo,
                    listTabs: controller.listCat,
                    tabHeight: 42.h))
            : const SizedBox.shrink();
      },
    );
  }

  ///目录对下的内容
  Widget _catDetailViews() {
    return TabBarView(
      controller: controller.catTabsControllerVideo,
      children: controller.listCatDetailView,
    );
  }
}
