import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_main_view/default_search_body_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/sliver_grid_view_fixed_size.dart';
import 'package:flutter_common_base/util/util_state_management/provider_widgets.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SearchDefaultContentView
    extends NormalPageViewBase<SearchDefaultContentController> {
  SearchDefaultContentView(
      {super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  SearchDefaultContentViewState createState() =>
      SearchDefaultContentViewState();
}

class SearchDefaultContentViewState
    extends NormalPageBaseViewState<SearchDefaultContentController> {
  @override
  PreferredSizeWidget appbar() {
    return const PreferredSize(
        preferredSize: Size.fromHeight(0), child: SizedBox.shrink());
  }

  @override
  Widget bodyContentWidget() {
    return SingleChildScrollView(
      child: Column(
        children: [
          buildDelBlock(),
          ControllerBuilder<SearchDefaultContentController>(
            id: "searchList",
            builder: (context, controller) {
              return _contentSubBlock(
                  BizValues.gradientBlue,
                  AssetsRes.iconSearchBlue,
                  strRightIcon: AssetsRes.iconRefresh,
                  "猜你想搜",
                  hotAreaClick: controller.guessHotAreaClick,
                  onPressDetail: controller.refreshSearch,
                  aniController: controller.aniController,
                  child: _rankContentLimit3(controller.listSearchSubList),
                  TextStyle(
                    height: 1.2,
                    fontSize: 16.sp,
                    fontFamily: "ALiMaMa_Bold",
                    foreground: Paint()
                      ..shader = const LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            BizColors.mainLineGradientFirst,
                            BizColors.mainLineGradientSecond,
                          ]).createShader(const Rect.fromLTWH(0, 0, 150, 0)),
                    fontWeight: FontWeight
                        .w700, /*color: BizColors.mainLineGradientFirst*/
                  ));
            },
          ),
          SizedBox(
            height: 20.h,
          ),
          GetBuilder<SearchDefaultContentController>(
            tag: widget.tag,
            id: "hotList",
            builder: (_) {
              return _contentSubBlockHotSearch(
                  BizValues.gradientPink,
                  AssetsRes.iconSearchReSou,
                  "热搜榜",
                  hotAreaClick: controller.hotSearchClick,
                  onPressDetail: controller.toHotSearchList,
                  child: _rankContentLimit6(controller.listHotSubList),
                  TextStyle(
                      height: 1.2,
                      fontSize: 16.sp,
                      fontFamily: "ALiMaMa_Bold",
                      fontWeight: FontWeight.w700,
                      color: BizColors.rgb100ff4141));
            },
          ),
        ],
      ),
    );
  }

  Widget _contentSubBlock(LinearGradient lineEffect, String strIcon,
      String strTitle, TextStyle textStyle,
      {VoidCallback? onPressDetail,
      double defHeight = 260,
      String strRightIcon = AssetsRes.navSmallRightIcon,
      AnimationController? aniController,
      VoidCallback? hotAreaClick,
      Widget child = const SizedBox.shrink()}) {
    return AspectRatio(
      aspectRatio: 1.sw / defHeight.h,
      child: Container(
        width: 1.sw,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16.r)),
            gradient: lineEffect),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 16.h),
            InkWell(
                onTap: hotAreaClick,
                child: _iconAndTip(strIcon, strTitle, textStyle,
                    onPressDetail: onPressDetail,
                    strRightIcon: strRightIcon,
                    aniController: aniController)),
            SizedBox(height: 16.h),
            child,
          ],
        ),
      ),
    );
  }

  Widget _contentSubBlockHotSearch(
    LinearGradient lineEffect,
    String strIcon,
    String strTitle,
    TextStyle textStyle, {
    VoidCallback? onPressDetail,
    double defHeight = 460,
    String strRightIcon = AssetsRes.navSmallRightIcon,
    Widget child = const SizedBox.shrink(),
    VoidCallback? hotAreaClick,
  }) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(16.r)),
          gradient: lineEffect),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 16.h),
          InkWell(
              onTap: hotAreaClick,
              child: _iconAndTip(strIcon, strTitle, textStyle,
                  onPressDetail: onPressDetail, strRightIcon: strRightIcon)),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }

  Widget _rankContentLimit3(List<VideoData> dataInfo) {
    return SizedBox(
      height: BizValues.itemBlockHeight.h,
      child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: dataInfo.length,
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) {
            VideoData dataInfoItem = dataInfo[index];
            return gridItem(dataInfoItem, margin: EdgeInsets.only(right: 12.w));
          }),
    );
  }

  Widget _rankContentLimit6(List<VideoData> dataInfo) {
    return GridView.builder(
        physics: const NeverScrollableScrollPhysics(), // 禁止内部滚动
        shrinkWrap: true,
        itemCount: dataInfo.length,
        gridDelegate: SliverGridDelegateWithFixedSize(
            BizValues.itemBlockWidth.w, BizValues.itemBlockHeight.h,
            mainAxisSpacing: BizValues.itemMainCross.w),
        itemBuilder: (BuildContext context, index) {
          VideoData dataInfoItem = dataInfo[index];
          return gridItem(dataInfoItem, nIndex: index);
        });
  }

  Widget _iconAndTip(
    String strIcon,
    String strTip,
    TextStyle textStyle, {
    VoidCallback? onPressDetail,
    String strRightIcon = AssetsRes.navSmallRightIcon,
    AnimationController? aniController,
  }) {
    return ColoredBox(
      color: Colors.transparent,
      child: SizedBox(
        width: 1.sw,
        height: 32.h,
        child: Row(
          children: [
            Image.asset(strIcon, width: 16.w, height: 16.w),
            SizedBox(width: 4.w),
            Text(strTip, style: textStyle),
            const Spacer(),
            InkWell(
              onTap: onPressDetail,
              child: RotateAnimation(
                animate: false,
                controller: aniController,
                child: Container(
                    padding: const EdgeInsets.all(3),
                    width: 26,
                    height: 26,
                    child:
                        Image.asset(strRightIcon, width: 16.w, height: 16.w)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  SearchDefaultContentController createController() {
    return SearchDefaultContentController();
  }

  Widget buildDelBlock() {
    return GetBuilder<SearchDefaultContentController>(
        id: "updateSearchLog",
        tag: widget.tag,
        builder: (con) {
          if (controller.listSearchHistory.isNotEmpty) {
            ///有搜索历史有内容的时候
            return hasSearchBlock();
          } else {
            return const SizedBox.shrink();
          }
        });
  }

  Container hasSearchBlock() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      margin: EdgeInsets.only(bottom: 16.h),
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 10.h, bottom: 10.h),
            height: 24.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "搜索历史",
                  style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xff333333)),
                ),
                SizedBox(
                  width: 4.w,
                ),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    controller.setDelSearchHistory();
                  },
                  child: Container(
                    // decoration: const BoxDecoration(
                    //   color: Colors.red,
                    // ),
                    alignment: Alignment.centerRight,
                    height: 32.h,
                    width: 32.h,
                    child: ImageLoadView(
                      AssetsRes.iconShanChu,
                      width: 16.w,
                      height: 16.w,
                    ),
                  ),
                ),
              ],
            ),
          ),
          updateSearchLogBlock(),
        ],
      ),
    );
  }

  Widget updateSearchLogBlock() {
    return GetBuilder<SearchDefaultContentController>(
        id: "updateSearchLog",
        tag: widget.tag,
        builder: (con) {
          return searchLogList();
        });
  }

  Widget searchLogList() {
    return Container(
      child: Wrap(
        alignment: WrapAlignment.start,
        children: searchLogItemBlock(),
      ),
    );
  }

  List<Widget> searchLogItemBlock() {
    List<Widget> list = [];
    for (var item in controller.listSearchHistory ?? []) {
      list.add(searchLogItemInfoDetail(item));
    }
    return list;
  }

  Widget searchLogItemInfoDetail(String str) {
    return Container(
      margin: EdgeInsets.all(5.w),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
      decoration: const BoxDecoration(
          color: BizColors.rgb100fafafa,
          borderRadius: BorderRadius.all(Radius.circular(16))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: () {
              commonEventBus.fire(SearchKeyHistoryEvent(str));
            },
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 130.w),
              child: Text(
                maxLines: 1,
                str,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    height: 1.2,
                    overflow: TextOverflow.ellipsis,
                    color: BizColors.rgb1000c1018,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RotateAnimation extends StatefulWidget {
  final AnimationController? controller;
  final Duration? duration;
  final bool animate;
  final Widget child;

  const RotateAnimation({
    super.key,
    this.controller,
    this.duration,
    this.animate = true,
    required this.child,
  });

  @override
  State<RotateAnimation> createState() => _RotateAnimationState();
}

class _RotateAnimationState extends State<RotateAnimation>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _rotation;
  late final bool _useExternalController;

  @override
  void initState() {
    super.initState();
    _useExternalController = widget.controller != null;
    _controller = widget.controller ??
        AnimationController(
          vsync: this,
          duration: widget.duration ?? const Duration(seconds: 1),
        );

    _rotation = Tween<double>(begin: 0, end: 1).animate(_controller);
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _controller.reset();
      }
    });
    if (widget.animate) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(RotateAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_useExternalController) {
      if (widget.animate && !_controller.isAnimating) {
        _controller.repeat();
      } else if (!widget.animate && _controller.isAnimating) {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    if (!_useExternalController) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _rotation,
      child: widget.child,
    );
  }
}
