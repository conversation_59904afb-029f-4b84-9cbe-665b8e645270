import 'dart:async';
import 'dart:convert';

import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';

class SearchKeyWordMainController extends CommonBaseController{
  late int nInitIndex;
  late int curTabIndex;
  String strKeyWord = "";
  StreamSubscription? eventKeyWordChange;
  List<VideoData> listSearchData = [];
  double maxWidthLimit = 264;
  SearchKeyWordMainController({this.strKeyWord = ""});

  int nCurIndexPage = 1;

  @override
  void onInit() {
    nInitIndex = 0;
    curTabIndex = 0;
    eventKeyWordChange = commonEventBus.on<SearchKeyWorkChangeEvent>().listen(_eventKeyWordChange);
    getContentByKeyword(strKeyWord);
    super.onInit();
  }

  Future<void> restoreSearchHistory(String strKeyword) async {
    List<String> listHist = [];
    String strHis = StorageUtil.get(SPKey.searchHistory, defaultValue: "");
    if (strHis.isEmpty) {
      listHist.insert(0, strKeyword);
    } else {
      listHist = json.decode(strHis).cast<String>();
      listHist.insert(0, strKeyword);
    }

    listHist = listHist.toSet().toList();
    listHist.take(BizValues.searchLogMaxCnt);
    ///转换一下存起来
    String strDes = json.encode(listHist);
    StorageUtil.set(SPKey.searchHistory, strDes);
  }

  Future<void> getContentByKeyword(String strKeyword,{int nListDataType = ListRefreshAct.refreshTypeInit,int nCurIndex = 1,int nCntPerPage = BizValues.maxSizePerPage}) async {
    restoreSearchHistory(strKeyword);
    Map<String, dynamic> mapResult = await ApiReqInterface.searchContent(strKeyword,nCurIndex: nCurIndex,nCntPerPage: nCntPerPage);
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      _listStatusErrorUpdate(nListDataType: nListDataType);
      update();
      return;
    }

    List<VideoData> listSearchDataTemp = [];
    var listData = mapResult["data"] ?? [];
    for (var item in listData) {
      VideoData dataItemInfo = VideoData.fromJson(item);
      listSearchDataTemp.add(dataItemInfo);
    }

    _listStatusSuccessUpdate(nListDataType: nListDataType, result: listSearchDataTemp,nPagesPerPage: nCntPerPage);
    nCurIndexPage = nCurIndexPage + 1;
    update();
  }

  Future<void> _eventKeyWordChange(SearchKeyWorkChangeEvent event) async {
    String strKeywordItem = event.strKeyWord;
    strKeyWord = strKeywordItem;
    getContentByKeyword(strKeywordItem);
  }

  @override
  Future<void> onPullUpLoadMore() async {
    getContentByKeyword(strKeyWord, nCurIndex: nCurIndexPage, nListDataType: ListRefreshAct.refreshTypePullUpMore);
  }
  ///下拉刷新
  @override
  Future<void> onPullDownReload() async {

  }


  ///加载失败时的处理
  void _listStatusErrorUpdate({int nListDataType = ListRefreshAct.refreshTypeInit}) {
    if (nListDataType == ListRefreshAct.refreshTypeInit) {
      dataLoadState.loadState = LoaderState.stateNoData;
    } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
      refreshController.refreshFailed();
    } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
      refreshController.loadNoData();
    }
  }

  void _listStatusSuccessUpdate({int nListDataType = ListRefreshAct.refreshTypeInit,List<VideoData> result = const [],int nPagesPerPage = BizValues.maxSizePerPage}) {
    if (result.isEmpty) {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        dataLoadState.loadState = LoaderState.stateNoData;
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        refreshController.refreshFailed();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        refreshController.loadNoData();
      }
    } else {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        listSearchData = [];
        listSearchData.addAll(result);
        dataLoadState.loadState = LoaderState.stateSucceed;
        refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        listSearchData = [];
        listSearchData.addAll(result);
        refreshController.refreshCompleted();
        refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        listSearchData.addAll(result);
        // if (result.length < nPagesPerPage) {
        //   refreshController.loadNoData();
        // } else {
        refreshController.loadComplete();
        // }
      }
    }

  }


  @override
  void onClose() {
    eventKeyWordChange?.cancel();
    super.onClose();
  }
}
