import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_main_view/search_keyword_main_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_pub.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class SearchKeyWordMainView extends CommonBaseView<SearchKeyWordMainController> {
  const SearchKeyWordMainView(
      {super.key,
      required super.isGlobal,
      super.tag,
      super.mapParam,
      super.enablePullUp});

  @override
  SearchKeyWordMainViewState createState() => SearchKeyWordMainViewState();
}

class SearchKeyWordMainViewState extends CommonBaseViewState<SearchKeyWordMainController> {
  Widget bodyContentWidget() {
    final dataList = controller.listSearchData;
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      itemCount: dataList.length,
      itemExtent: 140.h,
      cacheExtent: 500.h,
      itemBuilder: (context, index) {
        return SearchVideoItem(
          key: ValueKey(index),
          data: dataList[index],
          keyword: controller.strKeyWord,
          maxWidth: controller.maxWidthLimit,
        );
      },
    );
  }

  @override
  SearchKeyWordMainController createController() {
    return SearchKeyWordMainController(strKeyWord: widget.mapParam?["keyWord"] ?? "");
  }

  @override
  Widget buildBody() {
    return bodyContentWidget();
  }
}


class SearchVideoItem extends StatelessWidget {
  final VideoData data;
  final String keyword;
  final double maxWidth;

  const SearchVideoItem({
    super.key,
    required this.data,
    required this.keyword,
    required this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(child: searchItemInfo(data, keyword, maxWidth));
  }
}

