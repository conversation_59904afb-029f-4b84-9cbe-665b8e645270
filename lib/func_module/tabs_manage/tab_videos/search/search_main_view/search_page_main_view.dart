import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/search/search_info.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_main_view/default_search_body_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_main_view/search_keyword_main_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_main_view/search_page_main_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SearchPageMainView extends GetView<SearchPageMainController> with CommonWidgetPub {
  SearchInfo searchInfo;
  double? topMargin;
  bool? hideBack ;

  SearchPageMainView(
      {Key? key,
      required this.searchInfo,
      this.topMargin = 44,
      this.hideBack = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(SearchPageMainController(searchInfo: searchInfo));
    final double bottomPadding = MediaQuery.of(Get.context!).padding.bottom;
    return GetBuilder<SearchPageMainController>(builder: (controller) {
      return SafeArea(
          top: false,
          bottom: false,
          child: Column(
            children: <Widget>[
              _buildSearchTop(context),
              Expanded(
                child: Listener(
                    onPointerDown: (event) => FocusScope.of(context).requestFocus(FocusNode()),
                    child: _buildSearchBody()),
              ),

              SizedBox(width: 1.sw,height: bottomPadding.h,),

            ],
          ));
    });
  }

  Widget _buildSearchBody() {
    return GetBuilder<SearchPageMainController>(
        id: "searchMainBody",
        builder: (controller) {
          Widget widget = Container();
          ///搜索关键词为空
          if (controller.strSearchKeyWord.isEmpty) {
            switch (controller.searchInfo.searchType) {
              case SearchType.eDefaultPage:
                {
                  widget = SearchDefaultContentView(isGlobal: true, tag: controller.strGuid);
                }
                break;
              default:
                widget = Container();
                break;
            }
          } else {
            switch (controller.searchInfo.searchType) {
              case SearchType.eDefaultPage:
                {
                  widget = SearchKeyWordMainView(
                      isGlobal: false,
                      tag: controller.strKeywordGuid,
                      enablePullUp: true,
                      mapParam: {"keyWord": controller.strSearchKeyWord});
                }
                break;
              default:
                widget = Container();
                break;
            }
          }

          return widget;
        });
  }

  ///顶部的搜索框
  Widget _buildSearchTop(BuildContext context) {
    return GetBuilder<SearchPageMainController>(
        id: "searchEditSearchBtn",
        builder: (controllerMain) {
          return customWidgetAppbar(widChild: _getTextField(context));
        });
    //});
  }

  Widget _getTextField(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      height: 32.h,
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          TextFormField(
            cursorColor: BizColors.mainColorGreen,
            style: TextStyle(fontSize: 14.sp, color: BizColors.rgb100333333),
            textInputAction: TextInputAction.search,
            controller: controller.editController,
            textAlignVertical: TextAlignVertical.center,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(top: 0, bottom: 0, left: 12.w),
              filled: true,
              fillColor: BizColors.rgb30d1d8e1,
              hintText: controller.searchInfo.hintText,
              hintStyle: TextStyle(fontSize: 12.sp, color: BizColors.rgb100d1d8e1,fontWeight: FontWeight.w500),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(15), borderSide: BorderSide.none),
              // suffixIcon: _showClear ? _buildClearPhone() : null,
            ),
            onFieldSubmitted: (value) {
              controller.searchCallBack(value);
            },
            onChanged: (value) {
              controller.searchKeyWordChanged(value);
            },
          ),
          ///蓝色的搜索框
          Positioned(right: 12.w, child: InkWell(onTap: () {
            controller.searchCallBack(controller.editController.text);
          },
              child: Image.asset(AssetsRes.iconSearchBlue, width: 16.w, height: 16.w)))
        ],
      ),
    );
  }
}
