import 'dart:async';

import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/search/search_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:get/get.dart';

class SearchPageMainController extends GetxController {
  Widget? resultWidget;
  Widget? defaultWidget;

  String? hintText = "";
  late TextEditingController editController;
  late FocusNode focusNode;
  SearchInfo searchInfo;
  SearchPageMainController({required this.searchInfo});
  StreamSubscription? eventSearch;
  late String strSearchKeyWord;

  String strGuid = ToolsUtil().genUuId();
  String strKeywordGuid = ToolsUtil().genUuId();

  @override
  void onInit() {
    strSearchKeyWord = "";
    editController = TextEditingController();
    focusNode = FocusNode();
    _initEvent();
    super.onInit();
  }

  void _initEvent() {
    eventSearch = commonEventBus.on<SearchKeyHistoryEvent>().listen((event) async {
      setSearchText(event.strKeyWord  ?? "");

    });
  }

  Future<void> searchCallBack(String keyWord) async {
    strSearchKeyWord = keyWord;
    // // commonEventBus.fire(SearchKeyWorkChangeEvent(strKeyWord: strSearchKeyWord));
    if (strSearchKeyWord.isEmpty) {
      return;
    }
    update(["searchMainBody"]);
    commonEventBus.fire(SearchKeyWorkChangeEvent(strKeyWord: strSearchKeyWord));
  }

  Future<void> searchKeyWordChanged(String keyWord) async {
    strSearchKeyWord = keyWord;

    // update([BizStrings.updateSearchEditCloseBtn]);
    // if (strSearchKeyWord.isEmpty) {
    //   update([BizStrings.updateSearchMainBody,]);
    // }
  }

  Future<void> setSearchText(String str) async {
    editController.text = str;
    searchKeyWordChanged(str);
    searchCallBack(str);
  }

  Future<void> clearSearchKeyWord() async {
    editController.clear();
    searchKeyWordChanged("");
  }

  @override
  void onClose() {
    editController.dispose();
    focusNode.dispose();
    super.onClose();
  }
}
