// import 'dart:async';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:hh_party/app/models/event/event_bus.dart';
// import 'package:hh_party/app/models/event/room_event.dart';
// import 'package:hh_party/app/models/room_detail_info.dart';
// import 'package:hh_party/app/models/room_info.dart';
// import 'package:hh_party/app/models/search/search_param_model.dart';
// import 'package:hh_party/app/modules/home/<USER>/subitem_room.dart';
// import 'package:hh_party/app/res/api/api_service.dart';
// import 'package:hh_party/app/res/assets_res.dart';
// import 'package:hh_party/app/res/biz_colors.dart';
// import 'package:hh_party/app/res/language_strings.dart';
// import 'package:hh_party/app/service/screenAdapter.dart';
// import 'package:hh_party/app/util/single_page_route/single_route_manage.dart';
// import 'package:hh_party/app/util/userinfo/user_info_util.dart';
// import 'package:hh_party/app/widget/cached_networkimage/cached_network_image.dart';
// import 'package:hh_party/app/widget/image_load_view.dart';
// import 'package:hh_party/app/widget/loader.dart';
// import 'package:hh_party/app/widget/smartrefresh/smart_refresher_widget.dart';
// import 'package:pull_to_refresh/pull_to_refresh.dart';
//
// class SearchKeyWordComplexResult extends StatefulWidget {
//   String strKeyWord;
//   int tabIndexType;
//
//   SearchKeyWordComplexResult(
//       {Key? key, required this.strKeyWord, required this.tabIndexType})
//       : super(key: key);
//
//   @override
//   SearchKeyWordComplexResultState createState() =>
//       SearchKeyWordComplexResultState();
// }
//
// class SearchKeyWordComplexResultState extends State<SearchKeyWordComplexResult>
//     with AutomaticKeepAliveClientMixin {
//   late ViewLoaderState loaderState;
//   late RefreshController refreshController;
//   bool memberMore = false;
//   bool roomMore = false;
//
//   //是否为推荐项
//   bool recommendation = false;
//
//   late List<SearchRoomInfo> listRoomInfo;
//   late List<SearchDefaultUserInfo> memberList;
//
//   PageInfo? pageInfo;
//   late int nCurrentPage;
//
//   // RoomDataListInfo? roomListInfo;
//   StreamSubscription? eventKeyWordChange;
//
//   @override
//   void initState() {
//     loaderState = ViewLoaderState();
//     refreshController = RefreshController();
//     listRoomInfo = [];
//     memberList = [];
//     nCurrentPage = 1;
//     // roomListInfo = RoomDataListInfo(room: [], pageInfo: PageInfo());
//
//     eventKeyWordChange =
//         commonEventBus.on<SearchKeyWorkChangeEvent>().listen((event) {
//           debugPrint("sjl----eventKeyWordChange---${event.strKeyWord}");
//       _eventKeyWordChange(event);
//     });
//
//     if (widget.tabIndexType == SearchTabIndexType.searchTabIndexAll) {
//       _getTabAllDataInfo();
//       loaderState.loadState = LoaderState.stateSucceed;
//     } else {
//       _getOtherTabDataInfo();
//     }
//     super.initState();
//   }
//
//   Future<void> _eventKeyWordChange(SearchKeyWorkChangeEvent event) async {
//     widget.strKeyWord = event.strKeyWord ?? "";
//     if (widget.strKeyWord.isEmpty) {
//       return;
//     }
//     if (widget.tabIndexType == SearchTabIndexType.searchTabIndexAll) {
//       _getTabAllDataInfo();
//       loaderState.loadState = LoaderState.stateSucceed;
//     } else {
//       _getOtherTabDataInfo();
//     }
//   }
//
//   @override
//   void dispose() {
//     refreshController.dispose();
//     eventKeyWordChange?.cancel();
//     super.dispose();
//   }
//
//   Future<void> _getTabAllDataInfo(
//       {int nLoadType = ListRefreshAct.refreshTypeInit}) async {
//     debugPrint("sjl-----发搜索请求----${widget.strKeyWord}");
//     Map<String, dynamic> mapResult =
//         await ApiReqInterface.getSearchDefaultComplexTab(
//             searchKeyWord: widget.strKeyWord);
//
//     ///加载失败
//     if (mapResult['code'] != 200) {
//       if (nLoadType == ListRefreshAct.refreshTypeInit) {
//         loaderState.loadState = LoaderState.stateFailed;
//       } else if (nLoadType == ListRefreshAct.refreshTypePullDown) {
//         refreshController.refreshFailed();
//       } else if (nLoadType == ListRefreshAct.refreshTypePullUpMore) {
//         refreshController.loadFailed();
//       }
//       memberList = [];
//       if (mounted) {
//         setState(() {});
//       }
//       return;
//     }
//
//     ///分页信息
//     pageInfo = PageInfo.fromJson(mapResult["data"]["pageInfo"] ?? {});
//     nCurrentPage = pageInfo?.page ?? 1;
//
//     ///列表信息，搜索项
//     List<SearchRoomInfo>? room = [];
//     List info = mapResult['data']["room_list"] ?? [];
//     for (var element in info) {
//       SearchRoomInfo info = SearchRoomInfo.fromJson(element);
//       room.add(info);
//     }
//
//     listRoomInfo = [];
//     listRoomInfo.addAll(room);
//
//     ///用户信息
//     List<SearchDefaultUserInfo>? memberInfo = [];
//     List infoMember = mapResult['data']["member_list"] ?? [];
//     memberList = [];
//     for (var element in infoMember) {
//       SearchDefaultUserInfo infoMember =
//           SearchDefaultUserInfo.fromJson(element);
//       memberInfo.add(infoMember);
//     }
//     memberList.addAll(memberInfo);
//
//     if (memberList.isEmpty && listRoomInfo.isEmpty) {
//       loaderState.loadState = LoaderState.stateNoData;
//     } else {
//       loaderState.loadState = LoaderState.stateSucceed;
//     }
//     if (mounted) {
//       setState(() {});
//     }
//   }
//
//   Future<void> _getOtherTabDataInfo(
//       {int nLoadType = ListRefreshAct.refreshTypeInit,
//       int nCurPage = 1}) async {
//     Map<String, dynamic> mapResult =
//         await ApiReqInterface.getSearchOtherTabSearch(
//             searchKeyWord: widget.strKeyWord,
//             nCurrentPage: nCurPage,
//             nType: widget.tabIndexType);
//
//     ///加载失败
//     if (mapResult['code'] != 200) {
//       if (nLoadType == ListRefreshAct.refreshTypeInit) {
//         loaderState.loadState = LoaderState.stateFailed;
//       } else if (nLoadType == ListRefreshAct.refreshTypePullUpMore) {
//         refreshController.loadFailed();
//       }
//       if (mounted) {
//         setState(() {});
//       }
//       return;
//     }
//
//     ///分页信息
//     pageInfo = PageInfo.fromJson(mapResult["data"]["pageInfo"] ?? {});
//     nCurrentPage = pageInfo?.page ?? 1;
//
//     if (widget.tabIndexType == SearchTabIndexType.searchTabIndexRoom) {
//       ///列表信息
//       List<SearchRoomInfo>? room = [];
//       List info = mapResult['data']["room_list"] ?? [];
//       for (var element in info) {
//         SearchRoomInfo info = SearchRoomInfo.fromJson(element);
//         room.add(info);
//       }
//
//       if (room.isEmpty) {
//         if (nLoadType == ListRefreshAct.refreshTypeInit) {
//           loaderState.loadState = LoaderState.stateNoData;
//         } else if (nLoadType == ListRefreshAct.refreshTypePullUpMore) {
//           refreshController.loadComplete();
//         }
//       } else {
//         if (nLoadType == ListRefreshAct.refreshTypeInit) {
//           listRoomInfo = [];
//           listRoomInfo.addAll(room);
//           loaderState.loadState = LoaderState.stateSucceed;
//         } else if (nLoadType == ListRefreshAct.refreshTypePullUpMore) {
//           listRoomInfo.addAll(room);
//           if (room.length < 20) {
//             refreshController.loadNoData();
//           } else {
//             refreshController.loadComplete();
//           }
//         }
//       }
//     } else if (widget.tabIndexType == SearchTabIndexType.searchTabIndexUser) {
//       ///用户信息
//       List<SearchDefaultUserInfo>? memberListData = [];
//       List infoMember = mapResult['data']["member_list"] ?? [];
//       for (var element in infoMember) {
//         SearchDefaultUserInfo infoMember =
//             SearchDefaultUserInfo.fromJson(element);
//         memberListData.add(infoMember);
//       }
//
//       if (memberListData.isEmpty) {
//         if (nLoadType == ListRefreshAct.refreshTypeInit) {
//           loaderState.loadState = LoaderState.stateNoData;
//         } else if (nLoadType == ListRefreshAct.refreshTypePullUpMore) {
//           refreshController.loadComplete();
//         }
//       } else {
//         if (nLoadType == ListRefreshAct.refreshTypeInit) {
//           memberList = [];
//           memberList.addAll(memberListData);
//           loaderState.loadState = LoaderState.stateSucceed;
//         } else if (nLoadType == ListRefreshAct.refreshTypePullUpMore) {
//           memberList.addAll(memberListData);
//           if (memberListData.length < 20) {
//             refreshController.loadNoData();
//           } else {
//             refreshController.loadComplete();
//           }
//         }
//       }
//     }
//
//     if (mounted) {
//       setState(() {});
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       child: LoaderContainer(
//         contentView: SmartRefresherWidget(
//           _allTabContent(),
//           refreshController,
//           onPullUpLoading:
//               widget.tabIndexType == SearchTabIndexType.searchTabIndexAll
//                   ? null
//                   : _onPullLoadMore,
//         ),
//         loaderState: loaderState,
//       ),
//     );
//   }
//
//   void _onPullLoadMore() {
//     _getOtherTabDataInfo(
//         nLoadType: ListRefreshAct.refreshTypePullUpMore,
//         nCurPage: nCurrentPage + 1);
//   }
//
//   Widget _allTabContent() {
//     Widget wid = Container();
//     if (widget.tabIndexType == SearchTabIndexType.searchTabIndexUser) {
//       wid = _memberListInfo();
//     } else if (widget.tabIndexType == SearchTabIndexType.searchTabIndexRoom) {
//       wid = _roomListInfo();
//     } else {
//       wid = _allInfoList();
//     }
//     return wid;
//   }
//
//   Widget _allTabTextBanner(String text) {
//     return SliverToBoxAdapter(
//       child: Container(
//         padding: EdgeInsets.only(left: ScreenAdapter.width(16)),
//         margin: EdgeInsets.only(bottom: ScreenAdapter.height(10)),
//         child: Text(
//           text,
//           style: TextStyle(
//               color: const Color(0xff333333),
//               fontSize: ScreenAdapter.fontSize(16),
//               fontWeight: FontWeight.w500),
//         ),
//       ),
//     );
//   }
//
//   Widget _allInfoList() {
//     var listView = SliverFixedExtentList(
//       itemExtent: ScreenAdapter.height(120),
//       delegate: SliverChildBuilderDelegate(
//         (_, index) {
//           // return _subItemRoom(listRoomInfo[index]);
//           return SubItemRoom(listRoomInfo[index]);
//         },
//         childCount: listRoomInfo.length,
//       ),
//     );
//
//     var listViewMember = SliverFixedExtentList(
//       itemExtent: ScreenAdapter.height(66),
//       delegate: SliverChildBuilderDelegate(
//         (_, index) {
//           return _memberInfoItem(memberList[index]);
//         },
//         childCount: memberList.length,
//       ),
//     );
//
//     return CustomScrollView(
//       slivers: [
//         listRoomInfo.isNotEmpty
//             ? _allTabTextBanner(LanguageStrings.room_related_room.tr)
//             : SliverToBoxAdapter(child: Container()),
//         listRoomInfo.isNotEmpty
//             ? listView
//             : SliverToBoxAdapter(
//                 child: Container(),
//               ),
//         memberList.isNotEmpty
//             ? _allTabTextBanner(LanguageStrings.home_search_relate_user.tr)
//             : SliverToBoxAdapter(child: Container()),
//         memberList.isNotEmpty
//             ? listViewMember
//             : SliverToBoxAdapter(child: Container()),
//       ],
//     );
//   }
//
//   Widget _memberListInfo() {
//     return ListView.builder(
//         padding: const EdgeInsets.all(0),
//         itemCount: memberList.length,
//         itemBuilder: (BuildContext context, int index) {
//           return _memberInfoItem(memberList[index]);
//         });
//   }
//
//   ///用户id
//   Widget _userIdInfo({String userId = "", SearchDefaultUserInfo? info}) {
//     return info!.prettyBackUrl == ""
//         ? Container(
//             height: ScreenAdapter.height(24),
//             padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(6)),
//             constraints: const BoxConstraints(minWidth: 0),
//             decoration: BoxDecoration(
//                 color: const Color(0xFFFFE8EF),
//                 borderRadius:
//                     BorderRadius.all(Radius.circular(ScreenAdapter.width(11)))),
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Image.asset(
//                   Res.assetsImageMyId,
//                   width: ScreenAdapter.width(12),
//                   height: ScreenAdapter.height(9),
//                   fit: BoxFit.cover,
//                 ),
//                 SizedBox(
//                   width: ScreenAdapter.width(5),
//                 ),
//                 Container(
//                     constraints:
//                         BoxConstraints(maxWidth: ScreenAdapter.width(120)),
//                     child: Text(
//                       userId,
//                       style: TextStyle(
//                           color: const Color(0xFFFF5E96),
//                           fontSize: ScreenAdapter.fontSize(12),
//                           fontWeight: FontWeight.w400),
//                     )),
// /*          InkWell(
//               onTap: () {},
//               child: Image.asset(Res.assetsImageMyCopyId, width: ScreenAdapter.width(20), height: ScreenAdapter.width(20), fit: BoxFit.cover,)),*/
//               ],
//             ),
//           )
//         : MyCachedNetWorkImage(
//             strImgUrl: info.prettyBackUrl!,
//             imgHeight: 23.h,
//             imgWidth: 103.w,
//             widChild: Row(
//               children: [
//                 Container(
//                   margin: EdgeInsets.only(left: 30.w),
//                   constraints: BoxConstraints(maxWidth: ScreenAdapter.width(120)),
//                   child: Text(
//                     info.prettyId.toString(),
//                     style: TextStyle(color: BizColors.rgb_ffffffff, fontSize: ScreenAdapter.fontSize(12), fontWeight: FontWeight.w400),
//                   ),
//                 ),
//               ],
//             ),
//           );
//   }
//
//   Widget _memberInfoItem(SearchDefaultUserInfo memberInfo) {
//     return InkWell(
//         onTap: () {
//           SingleRoutePageManage.routeToUserProfileView(arguments: {"userId": memberInfo.id.toString()});
//         },
//         child: Container(
//           height: ScreenAdapter.height(66),
//           padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(16)),
//           child: Row(
//             children: [
//               ///头像
//               SizedBox(
//                 width: ScreenAdapter.width(50),
//                 height: ScreenAdapter.height(50),
//                 child: Stack(
//                   children: [
//                     ImageLoadView(
//                       memberInfo.avatar,
//                       width: ScreenAdapter.width(50),
//                       height: ScreenAdapter.width(50),
//                       shape: BoxShape.circle,
//                     ),
//                   ],
//                 ),
//               ),
//               SizedBox(
//                 width: ScreenAdapter.width(8),
//               ),
//
//               /// 昵称
//               Container(
//                 alignment: Alignment.center,
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       memberInfo.nickname ?? "",
//                       style: TextStyle(
//                           color: const Color(0xff292B30),
//                           fontSize: ScreenAdapter.fontSize(16),
//                           fontWeight: FontWeight.w500),
//                     ),
//                     SizedBox(
//                       height: ScreenAdapter.height(4),
//                     ),
//                     _userIdInfo(userId: '${memberInfo.id}', info: memberInfo),
//                   ],
//                 ),
//               ),
//               const Spacer(),
//
//               ///关注
//               (UserInfoUtil().isPatroller() || ('${memberInfo.id}' == (UserInfoUtil().userData?.userId ?? ""))) ? Container() : InkWell(
//                 onTap: () {
//                   _followUser(memberInfo);
//                 },
//                 child: Container(
//                     alignment: Alignment.center,
//                     width: ScreenAdapter.width(68),
//                     height: ScreenAdapter.height(28),
//                     child: Image.asset(
//                       memberInfo.follow == 0
//                           ? Res.comBtnFollow
//                           : Res.comBtnCancelFollow,
//                       width: ScreenAdapter.width(32),
//                       height: ScreenAdapter.width(32),
//                       // fit: BoxFit.cover,
//                     )),
//               ),
//             ],
//           ),
//         ));
//   }
//
//   Future<void> _followUser(SearchDefaultUserInfo memberInfo) async {
//     bool bRet = await ApiReqInterface.followUser(
//         nUserId: memberInfo.id ?? 0, nAction: memberInfo.follow == 0 ? 1 : 2);
//     if (!bRet) {
//       return;
//     }
//     int nIndex =
//         memberList.indexWhere((element) => element.id == memberInfo.id);
//     if (nIndex >= 0) {
//       memberList[nIndex].follow = memberInfo.follow == 0 ? 1 : 0;
//     }
//     if (mounted) {
//       setState(() {});
//     }
//   }
//
//   Widget _roomListInfo() {
//     return ListView.builder(
//         padding: const EdgeInsets.all(0),
//         itemCount: listRoomInfo.length,
//         itemBuilder: (BuildContext context, int index) {
//           return SubItemRoom(listRoomInfo[index] ?? RoomInfo());
//         });
//   }
//
//   @override
//   bool get wantKeepAlive => true;
// }
