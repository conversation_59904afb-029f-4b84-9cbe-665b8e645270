import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class SearchDefaultContentController extends NormalPageControllerBase {
  late List<String> listSearchHistory;
  late int nCurrentPage;

  static const int nShowMaxCnt = 3;

  int nHotPageIndex = 1;

  ///一页几个
  int nHotCntPerPage = 6;
  List<VideoData> listHot = [];
  List<VideoData> listHotSubList = [];

  int nGuessPageIndex = 1;

  ///一页几个
  int nGuessCntPerPage = 3;
  List<VideoData> listSearch = [];
  List<VideoData> listSearchSubList = [];
  late AnimationController aniController;
  late TickerProvider _tickerProvider;
  @override
  void onInit() {
    listSearchHistory = [];
    nCurrentPage = 0;

    ///获取搜索历史
    getSearchHistory();

    ///获取热搜榜
    getHotRankingList(bInit: true);

    ///获取想搜榜
    getYouMindRanking(bInit: true);

    _initAnimationController();
    super.onInit();
  }

  @override
  void onClose() {
    aniController.dispose();
    super.onClose();
  }

  ///猜你想搜热区点击
  Future<void> guessHotAreaClick() async {
    refreshSearch();
  }

  /// 热搜榜热区点击
  Future<void> hotSearchClick() async {
    toHotSearchList();
  }

  void _initAnimationController() {
    // 创建一个简单的TickerProvider
    _tickerProvider = const _AlwaysTickerProvider();
    aniController = AnimationController(
      vsync: _tickerProvider,
      duration: const Duration(seconds: 1),
    );
  }

  void cutList(List<VideoData> src, List<VideoData> des,
      {int nMaxLimit = nShowMaxCnt}) {
    des.clear();
    if (src.length <= nMaxLimit) {
      des.addAll(src);
    } else {
      des.addAll(src.sublist(0, nMaxLimit));
    }
  }

  Future<void> toHotSearchList() async {
    SingleRoutePageManage.routeToSearchHotList();
  }

  Future<void> refreshSearch() async {
    aniController.forward();
    getYouMindRanking(nPageIndex: nGuessPageIndex);
  }

  Future<void> getHotRankingList(
      {bool bInit = false, int nPageIndex = 1, int nCntPerPage = 6}) async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getRankingList(1,
        nPageIndex: nPageIndex, nCntPerPage: nCntPerPage);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      update(["hotList"]);
      return;
    }
    if (!bInit) {
      listHot.clear();
    }
    List items = mapResult["data"] ?? [];
    for (var item in items) {
      VideoData dataInfo = VideoData.fromJson(item);
      listHot.add(dataInfo);
    }
    cutList(listHot, listHotSubList, nMaxLimit: nHotCntPerPage);
    update(["hotList"]);
  }

  ///想搜榜
  Future<void> getYouMindRanking(
      {bool bInit = false, int nPageIndex = 1, int nCntPerPage = 3}) async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getRankingList(2,
        nPageIndex: nPageIndex, nCntPerPage: nCntPerPage);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      update(["searchList"]);
      return;
    }

    if (!bInit) {
      listSearch.clear();
    }

    nGuessPageIndex = nGuessPageIndex + 1;
    if (nGuessPageIndex >= 11) {
      nGuessPageIndex = 1;
    }
    List items = mapResult["data"] ?? [];
    for (var item in items) {
      VideoData dataInfo = VideoData.fromJson(item);
      listSearch.add(dataInfo);
    }
    cutList(listSearch, listSearchSubList, nMaxLimit: nGuessCntPerPage);
    update(["searchList"]);
  }

  ///删除搜索记录
  Future<void> setDelSearchHistory({String? searchString}) async {
    StorageUtil.remove(SPKey.searchHistory);
    getSearchHistory();
  }

  ///获取搜索历史记录信息
  Future<void> getSearchHistory() async {
    ///历史搜索信息
    listSearchHistory = [];
    String strSearchHistory =
        StorageUtil.get(SPKey.searchHistory, defaultValue: "");
    if (strSearchHistory.isEmpty) {
      update(["updateSearchLog"]);
      return;
    }
    listSearchHistory = json.decode(strSearchHistory).cast<String>();
    listSearchHistory.removeWhere((element) => element.isEmpty);
    update(["updateSearchLog"]);
  }
}

/// 简单的TickerProvider实现
class _AlwaysTickerProvider implements TickerProvider {
  const _AlwaysTickerProvider();

  @override
  Ticker createTicker(TickerCallback onTick) => Ticker(onTick);
}
