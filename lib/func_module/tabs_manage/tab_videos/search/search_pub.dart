import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/util/hight_light_keyword.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
int nIndex = 0;

// Widget searchItemInfo(VideoData itemInfo, String strKeyword, double maxWidthLimit,{int nIndex = -1}) {
//   return InkWell(
//     onTap: () {
//       SingleRoutePageManage.routeDramaDetailPageVideoData(itemInfo);
//     },
//     child: SizedBox(
//       width: 1.sw,
//       height: 136.h,
//       child: Row(
//         children: [
//           ///封面图
//           SizedBox(width: 66.w,height: 94.h,child: Stack(
//             alignment: Alignment.topCenter,
//             children: [
//               ImageLoadView(itemInfo.strCover, width: 66.w, height: 94.h, radius: 8.r),
//               Positioned(top: 0, left: 0, child: _iconInfoTopLeft(nIndex)),
//               Positioned(bottom: 6.h, right: 6.w, child: _iconInfoBottomRight(itemInfo)),
//               ],
//           ),),
//           SizedBox(width: 20.w),
//           ///内容
//           contentInfo(itemInfo, strKeyword, maxWidthLimit),
//         ],
//       ),
//     ),
//   );
// }

//
// Widget searchItemInfo(VideoData itemInfo, String strKeyword, double maxWidthLimit, {int nIndex = -1}) {
//   return InkWell(
//     onTap: () => SingleRoutePageManage.routeDramaDetailPageVideoData(itemInfo),
//     child: SizedBox(
//       width: 1.sw,
//       height: 136.h,
//       child: Row(
//         children: [
//           // 封面图
//           SizedBox(
//             width: 66.w,
//             height: 94.h,
//             child: Stack(
//               children: [
//                 // ImageLoadView(
//                 //   itemInfo.strCover,
//                 //   width: 66.w,
//                 //   height: 94.h,
//                 //   fit: BoxFit.cover,
//                 //   memCacheWidth: 66,
//                 //   memCacheHeight: 94,
//                 //   radius: 8.r,
//                 // ),
//                 if (nIndex >= 0) Positioned(top: 0, left: 0, child: _iconInfoTopLeft(nIndex)),
//                 if (itemInfo.bHot || itemInfo.bLatest)
//                   Positioned(bottom: 6.h, right: 6.w, child: _iconInfoBottomRight(itemInfo)),
//               ],
//             ),
//           ),
//           SizedBox(width: 20.w),
//           // 内容区域
//           Expanded(child: contentInfo(itemInfo, strKeyword, maxWidthLimit)),
//         ],
//       ),
//     ),
//   );
// }
//
//
//
// Widget _iconInfoBottomRight(VideoData itemInfo) {
//   List<Color> listBgColor = [Colors.transparent,Colors.transparent];
//   String strTextDesc = "";
//
//   if (itemInfo.bHot) {
//     strTextDesc = "爆剧";
//     listBgColor = [BizColors.rgb100ff4141, BizColors.rgb100ff4141];
//   } else if (itemInfo.bLatest) {
//     strTextDesc = "新剧";
//     listBgColor = [BizColors.mainColorGreen, BizColors.mainColorGreen];
//   } else {
//     return const SizedBox.shrink();
//   }
//
//   return Container(
//     width: 30.w,
//     height: 15.h,
//     alignment: Alignment.center,
//     decoration:
//     BoxDecoration(borderRadius: const BorderRadius.all(Radius.circular(4)),gradient: LinearGradient(colors: listBgColor)),
//     child: Text(strTextDesc,textAlign:TextAlign.center,style: TextStyle(height:1.2,fontSize: 9.sp,color: Colors.white,fontWeight: FontWeight.w500),),
//   );
// }
//
// Widget _iconInfoTopLeft(int nIndex ) {
//   if( nIndex < 0 ) {
//     return const SizedBox.shrink();
//   }
//
//   String strImg = 'assets/images/tab_manage/tab_video/top_more.png';
//   int nRealIndex = nIndex + 1;
//   if( nRealIndex <= 3 ) {
//     strImg = 'assets/images/tab_manage/tab_video/top_$nRealIndex.png';
//   }
//
//   return SizedBox(
//       width: 29.w,
//       height: 24.h,
//       child: Stack(
//         alignment: Alignment.center,
//         children: [
//           ImageLoadView(strImg, width: 29.w, height: 24.h),
//           Positioned(left:10.w,top:2.h,child: Text('$nRealIndex', style: TextStyle(fontSize: 13.sp, color: BizColors.rgb100ffffff, fontWeight: FontWeight.w600),)),
//         ],
//       ));
// }
//
// Widget contentInfo(VideoData itemInfo,String strKeyword,double maxWidthLimit) {
//   return ConstrainedBox(constraints: BoxConstraints(minHeight: 93.h,maxHeight: 93.h,maxWidth: maxWidthLimit.w),child: Column(
//     crossAxisAlignment: CrossAxisAlignment.start,
//     children: [
//       ///第一行名称
//       ConstrainedBox(
//         constraints: BoxConstraints(maxWidth: maxWidthLimit.w),
//         child: HighlightText(
//           text: itemInfo.strName,
//           keyword: strKeyword,
//           maxLines: 1,
//           overflow: TextOverflow.ellipsis,
//           normalStyle: TextStyle(
//               fontSize: 16.sp,
//               color: BizColors.rgb1000c1018,
//               fontWeight: FontWeight.w500),
//           highlightStyle: TextStyle(
//               fontSize: 16.sp,
//               color: BizColors.mainLineGradientFirst,
//               fontWeight: FontWeight.w500),
//         ),
//       ),
//       SizedBox(height: 3.h),
//
//       ///共多少集
//       Text("共${itemInfo.nTotalEpisode}集",style: TextStyle(fontSize: 14.sp,color: BizColors.rgb1000c1018),),
//       SizedBox(height: 3.h),
//       catItemTagInfo(itemInfo.videoCat.strName),
//       SizedBox(height: 3.h),
//       ConstrainedBox(constraints: BoxConstraints(maxWidth: maxWidthLimit.w,),child: Text(itemInfo.strDescription,style: TextStyle(overflow:TextOverflow.ellipsis,fontSize: 12.sp,color: BizColors.rgb100939eb3),),),
//     ],
//   ),);
// }
//
// Widget catItemTagInfo(String str) {
//   return Container(
//     padding: EdgeInsets.symmetric(horizontal: 12.w,vertical: 4.h),
//     decoration: const BoxDecoration(color: BizColors.rgb30d1d8e1, borderRadius: BorderRadius.all(Radius.circular(12))),
//     child: ConstrainedBox(
//       constraints: BoxConstraints(maxWidth: 130.w),
//       child: Text(
//         maxLines: 1,
//         str,
//         overflow: TextOverflow.ellipsis,
//         style: TextStyle(height:1.2,overflow: TextOverflow.ellipsis, fontSize: 10.sp,color:BizColors.rgb1006c6faf, fontWeight: FontWeight.w400),
//       ),
//     ),
//   );
// }




Widget searchItemInfo(VideoData itemInfo, String strKeyword, double maxWidthLimit, {int nIndex = -1}) {
  return InkWell(
    onTap: () => SingleRoutePageManage.routeDramaDetailPageVideoData(itemInfo),
    child: SizedBox(
      width: 1.sw,
      height: 136.h,
      child: Row(
        children: [
          // 封面图
          SizedBox(
            width: 66.w,
            height: 94.h,
            child: Stack(
              children: [
                ImageLoadView(
                  itemInfo.strCover,
                  width: 66.w,
                  height: 94.h,
                  fit: BoxFit.cover,
                  // memCacheWidth: 66,
                  // memCacheHeight: 94,
                  fadeInDuration: const Duration(milliseconds: 10),
                  fadeOutDuration: const Duration(milliseconds: 10),
                  radius: 8.r,
                ),
                if (nIndex >= 0) Positioned(top: -1, left: 0, child: _iconInfoTopLeft(nIndex)),
                if (itemInfo.bHot || itemInfo.bLatest)
                  Positioned(bottom: 6.h, right: 6.w, child: _iconInfoBottomRight(itemInfo)),
              ],
            ),
          ),
          SizedBox(width: 20.w),
          Expanded(child: contentInfo(itemInfo, strKeyword, maxWidthLimit)),
        ],
      ),
    ),
  );
}

Widget _iconInfoBottomRight(VideoData itemInfo) {
  if (!itemInfo.bHot && !itemInfo.bLatest) return const SizedBox.shrink();

  String strTextDesc = itemInfo.bHot ? "爆剧" : "新剧";
  Color bgColor = itemInfo.bHot ? BizColors.rgb100ff4141 : BizColors.mainColorGreen;

  return Container(
    width: 30.w,
    height: 15.h,
    alignment: Alignment.center,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(4.r),
      gradient: LinearGradient(colors: [bgColor, bgColor]),
    ),
    child: Text(
      strTextDesc,
      textAlign: TextAlign.center,
      style: TextStyle(
        height: 1.2,
        fontSize: 9.sp,
        color: Colors.white,
        fontWeight: FontWeight.w500,
      ),
    ),
  );
}

Widget _iconInfoTopLeft(int nIndex) {
  if (nIndex < 0) return const SizedBox.shrink();

  final nRealIndex = nIndex + 1;
  final strImg = nRealIndex <= 3
      ? 'assets/images/tab_manage/tab_video/top_$nRealIndex.png'
      : 'assets/images/tab_manage/tab_video/top_more.png';

  return SizedBox(
    width: 29.w,
    height: 24.h,
    child: Stack(
      alignment: Alignment.center,
      children: [
        ImageLoadView(strImg, width: 29.w, height: 24.h),
        Text(
          '$nRealIndex',
          style: TextStyle(
            fontSize: 13.sp,
            color: BizColors.rgb100ffffff,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    ),
  );
}

Widget contentInfo(VideoData itemInfo, String strKeyword, double maxWidthLimit) {
  final TextStyle normalStyle = TextStyle(
    fontSize: 16.sp,
    color: BizColors.rgb1000c1018,
    fontWeight: FontWeight.w500,
  );

  final TextStyle highlightStyle = TextStyle(
    fontSize: 16.sp,
    color: BizColors.mainLineGradientFirst,
    fontWeight: FontWeight.w500,
  );

  return ConstrainedBox(
    constraints: BoxConstraints(minHeight: 93.h, maxHeight: 93.h, maxWidth: maxWidthLimit.w),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 名称
        HighlightText(
          text: itemInfo.strName,
          keyword: strKeyword,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          normalStyle: normalStyle,
          highlightStyle: highlightStyle,
        ),
        SizedBox(height: 3.h),

        // 集数
        Text(
          "共${itemInfo.nTotalEpisode}集",
          style: TextStyle(fontSize: 14.sp, color: BizColors.rgb1000c1018),
        ),
        SizedBox(height: 3.h),

        // 分类标签
        catItemTagInfo(itemInfo.videoCat.strName),
        SizedBox(height: 3.h),

        // 简介
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: maxWidthLimit.w),
          child: Text(
            itemInfo.strDescription,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3),
          ),
        ),
      ],
    ),
  );
}

Widget catItemTagInfo(String str) {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
    decoration: const BoxDecoration(
      color: BizColors.rgb30d1d8e1,
      borderRadius: BorderRadius.all(Radius.circular(12)),
    ),
    child: ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 130.w),
      child: Text(
        str,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          height: 1.2,
          fontSize: 10.sp,
          color: BizColors.rgb1006c6faf,
          fontWeight: FontWeight.w400,
        ),
      ),
    ),
  );
}
