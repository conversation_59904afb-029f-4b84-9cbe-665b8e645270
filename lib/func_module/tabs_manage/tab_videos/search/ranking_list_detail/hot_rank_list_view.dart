import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/ranking_list_detail/hot_rank_list_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_pub.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_common_widget/smart_refresher_widget.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class HotRankListView extends StatefulWidget {
  const HotRankListView({super.key});

  @override
  HotRankListViewState createState() => HotRankListViewState();
}


class HotRankListViewState extends State<HotRankListView> with CommonWidgetPub {
  int nPageIndexHotList = 1;
  int nPagNumHotList = 15;
  List<VideoData> listVideoInfo = [];
  ViewLoaderState subLoadState  = ViewLoaderState();
  late RefreshController subRefreshController;

  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    subLoadState.loadState = LoaderState.stateSucceed;
    subRefreshController = RefreshController();
    getRankListVideos();
  }

  Future<void> getRankListVideos({int nAction = ListRefreshAct.refreshTypeInit,int nPageIndex = 1,int nCntPerPage = BizValues.maxSizePerPage}) async {
    if( isLoading ) {
      return;
    }
    isLoading = true;
    Map<String, dynamic> mapResult = await ApiReqInterface.getRankingList(1,nCntPerPage: nCntPerPage,nPageIndex: nPageIndex);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      _listStatusErrorUpdate(nListDataType: nAction);
      safeUpdate();
      isLoading = false;
      return;
    }

    nPageIndexHotList = nPageIndexHotList + 1;
    List<VideoData> listVideoInfoTemp = [];
    List items = mapResult['data'] ?? [];
    for( var item in items) {
      VideoData info = VideoData.fromJson(item);
      listVideoInfoTemp.add(info);
    }
    _listStatusSuccessUpdate(nListDataType: nAction,result: listVideoInfoTemp);
    safeUpdate();
    isLoading = false;

  }

  void safeUpdate() {
    if( mounted ) {
      setState(() {

      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _mainContent();
  }

  ///加载失败时的处理
  void _listStatusErrorUpdate({int nListDataType = ListRefreshAct.refreshTypeInit}) {
    if (nListDataType == ListRefreshAct.refreshTypeInit) {
      subLoadState.loadState = LoaderState.stateNoData;
    } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
      subRefreshController.refreshFailed();
    } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
      subRefreshController.loadNoData();
    }
  }

  void _listStatusSuccessUpdate({int nListDataType = ListRefreshAct.refreshTypeInit,List<VideoData> result = const [],int nPagesPerPage = BizValues.maxSizePerPage}) {
    if (result.isEmpty) {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        subLoadState.loadState = LoaderState.stateNoData;
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        subRefreshController.refreshFailed();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        subRefreshController.loadNoData();
      }
    } else {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        listVideoInfo = [];
        listVideoInfo.addAll(result);
        subLoadState.loadState = LoaderState.stateSucceed;
        subRefreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        listVideoInfo = [];
        listVideoInfo.addAll(result);
        subRefreshController.refreshCompleted();
        subRefreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        listVideoInfo.addAll(result);
        // if (result.length < nPagesPerPage) {
        //   refreshController.loadNoData();
        // } else {
        subRefreshController.loadComplete();
        // }
      }
    }
  }

  Widget _mainContent() {
    return SafeArea(
      top: false,
      bottom: false,
      child: Stack(
        children: [
          _whiteBg(),
          _bg(),
          _content(),
        ],
      ),
    );
  }

  Widget _content() {
    return Scaffold(
      extendBody: true,
      backgroundColor: Colors.transparent,
      appBar: customAppbar(
          // strTitleText: "热搜榜",
          centerTitle: _titleCenter(),
          onLeadingPress: () {
            Get.back();
          }),
      body: _mainBody(),
    );
  }

  Widget _titleCenter() {
    return SizedBox(
      width: 1.sw,
      height: 22.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(AssetsRes.iconSearchReSou, width: 16.w, height: 16.w),
          SizedBox(width: 4.w),
          Text("热搜榜", style: TextStyle(
              height: 1.2,
              fontSize: 16.sp,
              fontFamily: "ALiMaMa_Bold",
              fontWeight: FontWeight.w700,
              color: BizColors.rgb100ff4141)),
        ],
      ),
    );
  }

  Widget _gridViewVideos() {
    return ListView.builder(
        padding: EdgeInsets.only(left: 12.w,right: 12.w),
        physics: GetPlatform.isIOS  ? const BouncingScrollPhysics() : const ClampingScrollPhysics(),
        itemCount: listVideoInfo.length,
        cacheExtent: 200,
        itemBuilder: (BuildContext context, int index) {
          return KeyedSubtree(key: ValueKey(listVideoInfo[index].nId),child: _subItem(index));

        });
  }

  Widget _subItem(int nIndex) {
    VideoData info = listVideoInfo[nIndex];
    return RepaintBoundary(
      child: Column(
        children: [
          searchItemInfo(info, "", 264, nIndex: nIndex),
          if (nIndex != listVideoInfo.length - 1)
            Container(height: 0.5.h, color: BizColors.rgb30d1d8e1),
        ],
      ),
    );
  }

  Widget _mainBody() {
    return LoaderContainer(
      contentBuilder: () => SmartRefresherWidget(
        _gridViewVideos(),
        subRefreshController,
        onPullUpLoading: onPullUpLoadMore,
      ),
      loaderState: subLoadState,
      emptyView: noDataView(),
    );
  }

  Future<void> onPullUpLoadMore() async {
    getRankListVideos(nAction: ListRefreshAct.refreshTypePullUpMore, nPageIndex: nPageIndexHotList);
  }

  Widget _bg() {
    return Container(
      width: 1.sw,
      height: 260.h,
      decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(16.r)),gradient: BizValues.gradientPink),
    );
  }

  Widget _whiteBg() {
    return Container(
      width: 1.sw,
      height: 1.sh,
      decoration: const BoxDecoration(color: Colors.white),
    );
  }

  Widget noDataView() {
    return const ClassicalNoDataView(emptyTip: "暂无内容",);
  }
}