import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class HotRankListController extends CommonBaseController {
  ViewLoaderState historyDataLoadState = ViewLoaderState();

  int nTotal = 0;
  int nPageIndexHotList = 1;
  int nPagNumHotList = 15;
  List<VideoData> listVideoInfo = [];


  ViewLoaderState subLoadState  = ViewLoaderState();
  late RefreshController subRefreshController;

  @override
  void onInit() {
    super.onInit();
    dataLoadState.loadState = LoaderState.stateSucceed;
    subRefreshController = RefreshController();
    getRankListVideos();
  }

  @override
  Future<void> onPullUpLoadMore() async {
    getRankListVideos(nAction: ListRefreshAct.refreshTypePullUpMore, nPageIndex: nPageIndexHotList);
  }

  Future<void> getRankListVideos({int nAction = ListRefreshAct.refreshTypeInit,int nPageIndex = 1,int nCntPerPage = BizValues.maxSizePerPage}) async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getRankingList(1,nCntPerPage: nCntPerPage,nPageIndex: nPageIndex);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      _listStatusErrorUpdate(nListDataType: nAction);
      update();
      return;
    }

    nPageIndexHotList = nPageIndexHotList + 1;
    List<VideoData> listVideoInfoTemp = [];
    List items = mapResult['data'] ?? [];
    for( var item in items) {
      VideoData info = VideoData.fromJson(item);
      listVideoInfoTemp.add(info);
    }
    _listStatusSuccessUpdate(nListDataType: nAction,result: listVideoInfoTemp);
    update();
  }

  ///加载失败时的处理
  void _listStatusErrorUpdate({int nListDataType = ListRefreshAct.refreshTypeInit}) {
    if (nListDataType == ListRefreshAct.refreshTypeInit) {
      subLoadState.loadState = LoaderState.stateNoData;
    } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
      subRefreshController.refreshFailed();
    } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
      subRefreshController.loadNoData();
    }
  }

  void _listStatusSuccessUpdate({int nListDataType = ListRefreshAct.refreshTypeInit,List<VideoData> result = const [],int nPagesPerPage = BizValues.maxSizePerPage}) {
    if (result.isEmpty) {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        subLoadState.loadState = LoaderState.stateNoData;
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        subRefreshController.refreshFailed();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        subRefreshController.loadNoData();
      }
    } else {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        listVideoInfo = [];
        listVideoInfo.addAll(result);
        subLoadState.loadState = LoaderState.stateSucceed;
        subRefreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        listVideoInfo = [];
        listVideoInfo.addAll(result);
        subRefreshController.refreshCompleted();
        subRefreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        listVideoInfo.addAll(result);
        // if (result.length < nPagesPerPage) {
        //   refreshController.loadNoData();
        // } else {
        subRefreshController.loadComplete();
        // }
      }
    }

  }

}