import 'dart:async';

import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

typedef ItemClickCallback = void Function(CatIndexData, int);



class SearchPageIndexController extends NormalPageControllerBase {

  ///全部频道
  List<CatIndexData> listChannel = [];
  int nChannelSelIndex = 0;

  ///全部分类
  List<CatIndexData> listCat = [];
  int nCatSelIndex = 0;

  ///全部状态
  // List<CatIndexData> listStatus = [];
  // int nStatusSelIndex = 0;

  ///全部排行
  List<CatIndexData> listRange = [];
  int nRangeSelIndex = 0;

  List<VideoData> lstCatContent = [];

  Widget? resultWidget;
  Widget? defaultWidget;

  String? hintText = "";
  late TextEditingController editController;
  late FocusNode focusNode;

  // SearchInfo searchInfo;

  StreamSubscription? eventSearch;

  late String strSearchKeyWord;

  int nCurCatId = 0;

  late RefreshController refreshController;
  ///当前第几页
  int nCurIndexPage = 1;

  @override
  void onInit() {
    ///初始化分类
    _initCatData();
    strSearchKeyWord = "";
    editController = TextEditingController();
    focusNode = FocusNode();
    _initEvent();
    refreshController = RefreshController();

    super.onInit();
  }

  Future<void> onPullUpLoadMore() async {
    int nChannelId = listChannel[nChannelSelIndex].nId;
    int nFeed = listRange[nRangeSelIndex].nId;
    getVideosPages(
        nCatId: nCurCatId,
        nChannel: nChannelId,
        nFeed: nFeed,
        nListDataType: ListRefreshAct.refreshTypePullUpMore,
        nCurIndex: nCurIndexPage);
  }

  Future<void> toSearchMain() async {
    SingleRoutePageManage.routeToSearchMainPage();
  }

  Future<void> getVideosPages({int nCurIndex = 1,
    int nListDataType = ListRefreshAct.refreshTypeInit,
    int nPagesPerPage = BizValues.maxSizePerPage,
    int nCatId = 0,
    int nChannel = 0,
    int nFeed = 1,
  }) async {
    Map<String, dynamic> mapVideos = await ApiReqInterface.getVideosByCat(
        nCatId, nCurIndex,
        nChannel: nChannel, nFeedIndex: nFeed, nTotalPerPage: nPagesPerPage);
    if( mapVideos["code"] != ResponseCodeParse.codeSuccess) {
      _listStatusErrorUpdate();
      return;
    }

    List<VideoData> lstCatContentTemp = [];
    List items = mapVideos["data"] ?? [];
    for( var item in items)  {
      VideoData dataInfo = VideoData.fromJson(item);
      lstCatContentTemp.add(dataInfo);
    }
    _listStatusSuccessUpdate(nListDataType: nListDataType, result: lstCatContentTemp);
    nCurIndexPage = nCurIndexPage + 1;
    update(["contentInfo"]);
  }


  Future<void> getCatContents() async {
    Map<String, dynamic> mapRet = await ApiReqInterface.getVideoCats();
    if (mapRet["code"] != ResponseCodeParse.codeSuccess) {
      update(["catItem"]);
      return;
    }

    List catItems = mapRet["data"] ?? [];
    listCat.clear();
    listCat.add(CatIndexData(strName: "全部分类", nId: 0));

    for (var item in catItems) {
      CatIndexData indexData = CatIndexData(strName: item["name"] ?? "",nId: item["id"] ?? 0);
      if (indexData.nId == 0) {
        continue;
      }
      listCat.add(indexData);
    }
    update(["catItem"]);
    int nChannelId = listChannel[nChannelSelIndex].nId;
    int nFeed = listRange[nRangeSelIndex].nId;
    getVideosPages(nCatId: nCurCatId, nChannel: nChannelId, nFeed: nFeed);
  }


  ///频道
  void channelItemClick(CatIndexData itemInfo, int nIndexClick) {
    if (nIndexClick == nChannelSelIndex) {
      return;
    }
    nChannelSelIndex = nIndexClick;
    nCurIndexPage = 1;
    update(["catItem"]);

    int nChannelId = listChannel[nChannelSelIndex].nId;
    int nFeed = listRange[nRangeSelIndex].nId;
    getVideosPages(nCatId: nCurCatId, nChannel: nChannelId, nFeed: nFeed);
  }

  ///分类
  void catItemClick(CatIndexData itemInfo, int nIndexClick) {
    if (nIndexClick == nCatSelIndex) {
      return;
    }
    nCatSelIndex = nIndexClick;
    nCurCatId = listCat[nIndexClick].nId;
    nCurIndexPage = 1;
    update(["catItem"]);

    int nChannelId = listChannel[nChannelSelIndex].nId;
    int nFeed = listRange[nRangeSelIndex].nId;
    getVideosPages(nCatId: nCurCatId, nChannel: nChannelId, nFeed: nFeed,nCurIndex: nCurIndexPage);

  }
  // void statusItemClick(CatIndexData itemInfo, int nIndexClick) {
  //   nStatusSelIndex = nIndexClick;
  //   update(["catItem"]);
  // }

  ///排行
  void rankItemClick(CatIndexData itemInfo, int nIndexClick) {
    if( nIndexClick == nRangeSelIndex ) {
      return;
    }
    nRangeSelIndex = nIndexClick;
    nCurIndexPage = 1;
    update(["catItem"]);

    int nChannelId = listChannel[nChannelSelIndex].nId;
    int nFeed = listRange[nIndexClick].nId;
    getVideosPages(nCatId: nCurCatId, nChannel: nChannelId, nFeed: nFeed,nCurIndex: nCurIndexPage);
  }

  void _initCatData() {

    listChannel = [
      CatIndexData(strName: "全部频道", nId: 0),
      CatIndexData(strName: "男频", nId: 1),
      CatIndexData(strName: "女频", nId: 2),
    ];

    getCatContents();


    listCat = [
      CatIndexData(strName: "全部分类", nId: 0),
    ];


    // listStatus = [
    //   CatIndexData(strName: "全部状态", nId: 0),
    //   CatIndexData(strName: "已完结", nId: 1),
    //   CatIndexData(strName: "未完结", nId: 2),
    // ];


    listRange= [
      CatIndexData(strName: "全部排行", nId: 1),
      CatIndexData(strName: "最热", nId: 2),
      CatIndexData(strName: "最新", nId: 3),
    ];
  }

  void _initEvent() {
    // eventSearch = commonEventBus.on<SearchKeyWorkEvent>().listen((event) async {
    //   setSearchText(event.strKeyWord  ?? "");
    //
    //
    // });
  }

  Future<void> searchCallBack(String keyWord) async {
    // strSearchKeyWord = keyWord;
    // commonEventBus.fire(SearchKeyWorkChangeEvent(strKeyWord: strSearchKeyWord));
    // if (strSearchKeyWord.isNotEmpty) {
    //   update([BizStrings.updateSearchMainBody]);
    //   commonEventBus.fire(SearchKeyWorkChangeEvent(
    //       roomId: searchInfo.roomId, strKeyWord: strSearchKeyWord));
    // }
  }

  Future<void> searchKeyWordChanged(String keyWord) async {
    // strSearchKeyWord = keyWord;
    //
    // update([BizStrings.updateSearchEditCloseBtn]);
    // if (strSearchKeyWord.isEmpty) {
    //   update([
    //     BizStrings.updateSearchMainBody,
    //   ]);
    // }
  }

  ///加载失败时的处理
  void _listStatusErrorUpdate({int nListDataType = ListRefreshAct.refreshTypeInit}) {
    if (nListDataType == ListRefreshAct.refreshTypeInit) {
      dataLoadState.loadState = LoaderState.stateNoData;
    } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
      refreshController.refreshFailed();
    } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
      refreshController.loadNoData();
    }
  }

  void _listStatusSuccessUpdate({int nListDataType = ListRefreshAct.refreshTypeInit,List<VideoData> result = const [],int nPagesPerPage = BizValues.maxSizePerPage}) {
    if (result.isEmpty) {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        dataLoadState.loadState = LoaderState.stateNoData;
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        refreshController.refreshFailed();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        refreshController.loadNoData();
      }
    } else {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        lstCatContent = [];
        lstCatContent.addAll(result);
        dataLoadState.loadState = LoaderState.stateSucceed;
        refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        lstCatContent = [];
        lstCatContent.addAll(result);
        refreshController.refreshCompleted();
        refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        lstCatContent.addAll(result);
        // if (result.length < nPagesPerPage) {
        //   refreshController.loadNoData();
        // } else {
        refreshController.loadComplete();
        // }
      }
    }

  }



  Future<void> setSearchText(String str) async {
    editController.text = str;
    searchKeyWordChanged(str);
    searchCallBack(str);
  }

  Future<void> clearSearchKeyWord() async {
    editController.clear();
    searchKeyWordChanged("");
  }

  @override
  void onClose() {
    editController.dispose();
    focusNode.dispose();
    super.onClose();
  }
}
