import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_index/search_index_page_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/sliver_grid_view_fixed_size.dart';
import 'package:flutter_common_base/util/util_common_widget/smart_refresher_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';


class SearchPageIndexView extends NormalPageViewBase<SearchPageIndexController> {
  SearchPageIndexView({super.key, required super.isGlobal, super.tag, super.mapParam});
  @override
  SearchPageMainViewState createState() => SearchPageMainViewState();
}
class SearchPageMainViewState extends NormalPageBaseViewState<SearchPageIndexController>{
  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
      horizonRightPadding: 12.w,
        onLeadingPress: () {
          Get.back();
        },
        actions: [searchBox(onPress: controller.toSearchMain)]);
  }

  @override
  Widget mainBg() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFFFFF7FA),
            Color(0xFFFDECF3),
            Color(0xFFF5E6F5),
            Color(0xFFF0F2F5),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
    );
  }



  @override
  Widget bodyContentWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
      children: [
        _catAndType(),
        Expanded(child: _defaultIndexContent()),
      ],
          ),
    );
  }

  Widget _catAndTypeList(List<CatIndexData> listData, int nCurSelIndex,{ItemClickCallback? onItemClick}) {
    return SizedBox(width: 1.sw,height: 24.h,
    child: ListView.builder(scrollDirection:Axis.horizontal,itemCount:listData.length,itemBuilder: (BuildContext context, int index){
      CatIndexData dataItem = listData[index];
      return InkWell(onTap: () {
        onItemClick?.call(dataItem, index);
                },child: _defaultCatItem(dataItem.strName,isSelected: nCurSelIndex == index));
    }),);
  }

  Widget _defaultCatItem(String strText, {double maxWidth = 74, double horizonPadding = 12, double minWidth = 48, bool isSelected = false}) {
    return ConstrainedBox(
      constraints: BoxConstraints(
          minHeight: 24.h,
          maxHeight: 24.h,
          maxWidth: maxWidth.w,
          minWidth: minWidth.w),
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: horizonPadding.w),
        decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            gradient: LinearGradient(colors: isSelected ? BizColors.gradientColor: [Colors.transparent, Colors.transparent])),
        child: Text(strText, style: TextStyle(
            color: isSelected ? Colors.white : BizColors.rgb100939eb3,
            fontSize: 12.sp,
            height: 1.2,
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400),),
      ),
    );
  }

  Widget _catAndType() {
    return GetBuilder<SearchPageIndexController> (tag: widget.tag,
      id: "catItem",
      builder: (_){
      return SizedBox(
        width: 1.sw,
        height: 110.h,
        child: Column(
          children: [
            _catAndTypeList(controller.listChannel, controller.nChannelSelIndex, onItemClick: controller.channelItemClick),
            SizedBox(height: 16.h),
            _catAndTypeList(controller.listCat, controller.nCatSelIndex,onItemClick: controller.catItemClick),
            SizedBox(height: 16.h),
            // _catAndTypeList(controller.listStatus, controller.nStatusSelIndex,onItemClick: controller.statusItemClick),
            // SizedBox(height: 16.h),
            _catAndTypeList(controller.listRange, controller.nRangeSelIndex,onItemClick: controller.rankItemClick),
          ],
        ),
      );
    },);
  }

  Widget _defaultIndexContent() {
    return GetBuilder<SearchPageIndexController>(
      tag: widget.tag,
      id: "contentInfo",
      builder: (_) {
        return LoaderContainer(
          contentBuilder: () => SmartRefresherWidget(
            _mainGridVideos(), // 注意这里传的是可滚动内容
            controller.refreshController,
            onPullUpLoading: controller.onPullUpLoadMore,
            // onPullDownRefresh: onPullDownReload,
          ),
          loaderState: controller.dataLoadState,
        );
      },
    );
  }

  Widget _mainGridVideos() {
    return GridView.builder(
        physics: const BouncingScrollPhysics(),
        itemCount: controller.lstCatContent.length,
        gridDelegate: SliverGridDelegateWithFixedSize(BizValues.itemBlockWidth.w, BizValues.itemBlockHeight.h, mainAxisSpacing: BizValues.itemMainCross.w),
        itemBuilder: (BuildContext context, index) {
          VideoData videoItem = controller.lstCatContent[index];
          return gridItem(videoItem);
        });
  }

  @override
  SearchPageIndexController createController() {
    return SearchPageIndexController();
  }
}
