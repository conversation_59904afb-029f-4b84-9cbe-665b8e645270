import 'package:flutter/material.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/cat_detail_tab_view/tab_detail_controller_base.dart';
import 'package:get/get.dart';

abstract class BaseView<T extends BaseController> extends StatefulWidget {
  final bool isGlobal;
  final String? tag;
  final int nCatId;
  final Map<String,dynamic> arguments;
  /// 构造函数，支持全局和局部模式
  const BaseView({super.key, this.tag, this.isGlobal = false,this.nCatId = 0,this.arguments = const {}});
}

abstract class BaseViewState<T extends BaseController> extends State<BaseView<T>> {

  late T controller;

  T createController({Map<String, dynamic> arguments = const {}});

  @override
  void initState() {
    super.initState();
    /// 在视图初始化时，进行控制器的注册和初始化
    if (widget.isGlobal) {
      /// 全局模式：如果控制器没有注册过，就创建
      if (!Get.isRegistered<T>(tag: widget.tag)) {
        controller = Get.put<T>(createController(arguments: widget.arguments), tag: widget.tag);
      } else {
        controller = Get.find<T>(tag: widget.tag);
      }
    } else {
      /// 局部模式：每次都创建一个新的控制器
      controller = Get.put<T>(createController(arguments: widget.arguments), tag: widget.tag);
    }
  }

  ///下拉刷新
  Future<void> onPullUpLoadMore() async {

  }
  ///上位加载更多
  Future<void> onPullDownReload() async {

  }

  @override
  void dispose() {
    super.dispose();
    /// 局部模式下销毁 Controller
    if (!widget.isGlobal) {
      Get.delete<T>(tag: widget.tag); /// 删除局部控制器
    }
  }


  @override
  Widget build(BuildContext context) {
    return buildBody(context);
  }

  /// 子类需要实现这个方法来构建 UI
  Widget buildBody(BuildContext context) {
    return Container();
  }
}