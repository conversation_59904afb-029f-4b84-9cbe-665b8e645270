import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/cat_detail_tab_view/cat_detail_implement_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/cat_detail_tab_view/tab_detail_view_base.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/sliver_grid_view_fixed_size.dart';
import 'package:flutter_common_base/util/util_common_widget/smart_refresher_widget.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/state_manager.dart';

class VideosCatDetailView extends BaseView<HomeController> {
  final int nId;
  final Map<String, dynamic> mapArg;

  const VideosCatDetailView({
    super.key,
    this.nId = 0,
    this.mapArg = const {},
    super.isGlobal,
    String? strTag,
  }) : super(tag: strTag, arguments: mapArg);

  @override
  HomeViewState createState() => HomeViewState();
}

class HomeViewState extends BaseViewState<HomeController> with CommonWidgetPub {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget buildBody(BuildContext context) {
    return GetBuilder<HomeController>(
        tag: widget.tag,
        builder: (_) {
          return LoaderContainer(
            contentBuilder: () => SmartRefresherWidget(
              _mainContent(), // 注意这里传的是可滚动内容
              controller.refreshController,
              onPullUpLoading: controller.onPullUpLoadMore,
              // onPullDownRefresh: onPullDownReload,
            ),
            loaderState: controller.loaderState,
          );
        });
  }

  Widget _mainContent() {
    return CustomScrollView(
      slivers: [
        /// 轮播图
        SliverToBoxAdapter(child: _swiperImage(),),
        ///间隔
        SliverToBoxAdapter(child: SizedBox(height: 20.h,)),
        /// 网格列表
        _videosBlocks(),
      ],
    );
  }

  ///轮播图
  Widget _swiperImage() {
    return GetBuilder<HomeController>(
        tag: widget.tag,
        id: "swiperInfo",
        builder: (_) {
          return controller.listBannerInfo.isEmpty ? const SizedBox.shrink() : SizedBox(
            height: 150.h,
            width: 1.sw,
            child: Swiper(
              itemBuilder: (context, index) {
                final image = controller.listBannerInfo[index].strCover;
                return InkWell(
                        onTap: () {
                          controller.onBannerItemClick(controller.listBannerInfo[index]);
                        },
                        child: ClipRRect(borderRadius: const BorderRadius.all(Radius.circular(16)), child: ImageLoadView(image, fit: BoxFit.fill),
                  ),
                );
              },
              indicatorLayout: PageIndicatorLayout.COLOR,
              autoplay: true,
              itemCount: controller.listBannerInfo.length,
              pagination: const SwiperPagination(
                  builder: DotSwiperPaginationBuilder(
                      size: 6,
                      activeSize: 6,
                      activeColor: BizColors.mainLineGradientSecond)),
              // control: const SwiperControl(),
            ),
          );
        });
  }

  ///内容源块
  Widget _videosBlocks() {
    return SliverPadding(
      padding: EdgeInsets.zero,
      sliver: SliverGrid(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return _gridItem(index);
          },
          childCount: controller.videos.length,
        ),
        gridDelegate: SliverGridDelegateWithFixedSize(BizValues.itemBlockWidth.w, BizValues.itemBlockHeight.h, mainAxisSpacing: BizValues.itemMainCross.w),
      ),
    );
  }

  Widget _gridItem(int index) {
    VideoData info = controller.videos[index];
    return gridItem(info);
  }

  @override
  HomeController createController({Map<String, dynamic> arguments = const {}}) {
    return HomeController(nCatId: widget.nCatId, mapArguments: arguments);
  }
}
