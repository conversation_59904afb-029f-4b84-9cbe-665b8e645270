import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/data_model/tab_manage/tab_video/banner_list_data.dart';
import 'package:flutter_common_base/func_module/ad_union/drama_detail.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/cat_detail_tab_view/tab_detail_controller_base.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_webview/util_webview.dart';
import 'package:flutter_common_base/util/util_state_management/base_controller.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../common/biz_values/biz_key_store.dart';
import '../../../../util/util_storage/storage_util.dart';

class HomeController extends BaseController {
  late final RefreshController refreshController;
  ViewLoaderState loaderState = ViewLoaderState();

  ///暂时先用图片代替块
  List<BannerListData> listBannerInfo = [];
  Map<String, dynamic> mapArguments = {};
  int nCatId = 0;

  HomeController({this.nCatId = 0, this.mapArguments = const {}});

  ///当前第几页
  int nCurPageIndex = 1;

  ///短剧内容
  List<VideoData> videos = [];

  @override
  void onInit() {
    super.onInit();
    nCatId = mapArguments["catId"] ?? 0;
    refreshController = RefreshController(initialLoadStatus: LoadStatus.idle);
    getBannerListData();
    getVideosPages(nCurPage: nCurPageIndex);
  }

  Future<void> onItemClick(VideoData itemInfo) async {
    SingleRoutePageManage.routeDramaDetailPageVideoData(itemInfo);
  }

  Future<void> onBannerItemClick(BannerListData itemInfo) async {
    if (itemInfo.nType == 1) {
      SingleRoutePageManage.routeDramaDetailPageVideoData(
          VideoData(strSourceId: itemInfo.strRoute));
    } else if (itemInfo.nType == 2) {
      String strUrl = itemInfo.strRoute;
      if (strUrl.isEmpty) {
        return;
      }

      String strDeviceId = StorageUtil.get<String>(SPKey.keyDevId,
          defaultValue: ToolsUtil().genUuId());
      if (strDeviceId.isEmpty) {
        strDeviceId = ToolsUtil().genUuId();
      }

      String? strToken = StorageUtil.get<String>(SPKey.keyToken);

      // 判断是否已有参数
      String separator = strUrl.contains('?') ? '&' : '?';

      strUrl = '$strUrl${separator}d=$strDeviceId';
      if (strToken.isNotEmpty) {
        strUrl += '&t=$strToken';
      }

      // TODO: 需要从UI层传入context，或使用Navigator.of(context)
      // WebViewUtil.jumpToCommonWebPage(context, strUrl);
    }
  }

  Future<void> getVideosPages(
      {int nCurPage = 1,
      int nAction = ListRefreshAct.refreshTypeInit,
      int nPagesPerPage = BizValues.maxSizePerPage}) async {
    Map<String, dynamic> mapVideos =
        await ApiReqInterface.getVideosByCat(nCatId, nCurPage);
    if (mapVideos["code"] != ResponseCodeParse.codeSuccess) {
      _listStatusErrorUpdate(nListDataType: nAction);
      update();
      return;
    }

    nCurPageIndex = nCurPageIndex + 1;

    ///中间临时结果
    List<VideoData> resultTemp = [];
    List items = mapVideos["data"] ?? [];
    for (var item in items) {
      VideoData dataInfo = VideoData.fromJson(item);
      resultTemp.add(dataInfo);
    }
    _listStatusSuccessUpdate(
        nListDataType: nAction,
        nPagesPerPage: nPagesPerPage,
        result: resultTemp);
    update();
  }

  ///获取bannerList
  Future<void> getBannerListData() async {
    if (nCatId != BizValues.catHomePageId) {
      return;
    }
    Map<String, dynamic> mapResult = await ApiReqInterface.getBannerList();
    if (mapResult["code"] == ResponseCodeParse.codeSuccess) {
      List banners = mapResult["data"] ?? [];
      for (var item in banners) {
        BannerListData itemInfo = BannerListData.fromJson(item);
        listBannerInfo.add(itemInfo);
      }
    }
    update(["swiperInfo"]);
    loaderState.loadState = LoaderState.stateSucceed;
    update();
    // update(["bannerInfo"]);
    ///内容开始
  }

  ///下拉刷新
  Future<void> onPullUpLoadMore() async {
    getVideosPages(
        nCurPage: nCurPageIndex, nAction: ListRefreshAct.refreshTypePullUpMore);
  }

  ///加载失败时的处理
  void _listStatusErrorUpdate(
      {int nListDataType = ListRefreshAct.refreshTypeInit}) {
    if (nListDataType == ListRefreshAct.refreshTypeInit) {
      loaderState.loadState = LoaderState.stateNoData;
    } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
      refreshController.refreshFailed();
    } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
      refreshController.loadNoData();
    }
  }

  void _listStatusSuccessUpdate(
      {int nListDataType = ListRefreshAct.refreshTypeInit,
      List<VideoData> result = const [],
      int nPagesPerPage = BizValues.maxSizePerPage}) {
    if (result.isEmpty) {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        loaderState.loadState = LoaderState.stateNoData;
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        refreshController.refreshFailed();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        refreshController.loadNoData();
      }
    } else {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        videos = [];
        videos.addAll(result);
        loaderState.loadState = LoaderState.stateSucceed;
        refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        videos = [];
        videos.addAll(result);
        refreshController.refreshCompleted();
        refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        videos.addAll(result);
        // if (result.length < nPagesPerPage) {
        //   refreshController.loadNoData();
        // } else {
        refreshController.loadComplete();
        // }
      }
    }
  }
}
