import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/func_module/ad_union/feedflow/drama_theater_feed_flow.dart';
import 'package:flutter_common_base/func_module/ad_union/novel/novel_home_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/tab_fuli_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_messages/tab_message_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/tab_my_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_recommend/tab_recommend_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/tab_videos_view.dart';
import 'package:flutter_common_base/util/forground_service/forground_servcie.dart';
import 'package:flutter_common_base/util/uitl_login/login_util.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_csj/csj_union_util.dart';
import 'package:flutter_common_base/util/util_tab_dyn_info/dynamic_tab_info.dart';
import 'package:flutter_common_base/util/util_update/util_update.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_common_base/util/util_withdraw_data.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:get/get.dart';

class TabsManageController extends GetxController {
  int currentIndex = 0;
  late PageController pageController;

  ///初始化是显示青少年框还是正常状态
  var nInitModePage = 0.obs;

  final MethodChannel _channel = const MethodChannel('com.meteorsnow/method');

  ///这个和pages要严格匹配
  List<String> listTitles = [
    LanStrings.tabRecommend,
    LanStrings.tabShortVideo,
    LanStrings.tabFuLi,
    LanStrings.tabBook,
    LanStrings.tabMy
  ];

  final List<Widget> pages = [
    ///推荐
    const KeepAliveWrapper(child: TabRecommendPage()),
    ///剧场
    const KeepAliveWrapper(child: TabVideosPage()),
    ///福利
    const KeepAliveWrapper(child: TabFuLiPage()),
    ///书籍
    const KeepAliveWrapper(child: NovelHomePage()),
    ///我的
    const KeepAliveWrapper(child: TabMyView()),

  ];

  List<TabIndexData> listTab = [];
  StreamSubscription? _sub;

  TabsManageController({this.currentIndex = 0});

  StreamSubscription? eventYouthMode;
  ///是否在显示推荐页的tab
  bool bShowRecommend = false;
  ///当前接口返回的id
  int nIndexTabId = 1;
  @override
  void onInit() {
    super.onInit();
    ///动态构造tab
    _buildTabs();

    ///监听青少年模式开着的事件
    eventYouthMode = commonEventBus.on<YouthModeStateEvent>().listen(youthModeListen);
    ///初始化索引
    _initTabIndexData();
    ///获取参数
    if (Get.arguments != null) {
      currentIndex = Get.arguments["initialPage"] ?? 0;
      bool bOpen = Get.arguments["youthMode"] ?? false;
      nInitModePage.value = bOpen ? 1 : 0;
    }
    bShowRecommend = nIndexTabId == 1;
    pageController = PageController(initialPage: currentIndex);
    ForGroundServiceUtil.startService();
    ///检查升级
    _checkUpdate();
    ///预加载广告
    CsjUnionUtil.preloadFuLiAd();
    ///启动前台服务
    _startForegroundTask();

    ///获取提现提示和提现类型
    WithdrawInfoData().getTipText();

    ///获取三方登录方式
    LoginUtil().getLoginTypeInfo();

    update();
  }

  @override
  void onReady() {
    super.onReady();
    ///获取用户信息
    UserInfoUtil.getUserInfoDetail();
  }

  Future<void> _startForegroundTask() async {
    if (GetPlatform.isIOS) {
      return;
    }
    try {
      _channel.invokeMethod('startService', {"token": "Token"});
    } catch (e) {
      // TODO
    }
  }


  void _buildTabs() {
    int nLen = DynamicTabInfo.listNavTabInfo.length;
    if( nLen <= 0 ) {
      return;
    }

    nIndexTabId = DynamicTabInfo.listNavTabInfo[0].id;
    pages.clear();
    listTitles.clear();
    for (int nIndex = 0; nIndex < nLen; nIndex++) {
      BotNavItemInfo itemInfo = DynamicTabInfo.listNavTabInfo[nIndex];
      listTitles.add(itemInfo.view);
      switch(itemInfo.id) {
        case 1:
          {
            pages.add(const KeepAliveWrapper(child: TabRecommendPage()));
          }
          break;
        case 2:
          {
            pages.add(const KeepAliveWrapper(child: TabVideosPage()));
          }
          break;
        case 3:
          {
            pages.add(const KeepAliveWrapper(child: TabFuLiPage()));
          }
          break;
        case 4:
          {
            pages.add(const KeepAliveWrapper(child: NovelHomePage()));
          }
          break;
        case 5:
          {
            pages.add(const KeepAliveWrapper(child: TabMyView()));
          }
          break;
      }
    }

  }

  Future<void> _checkUpdate() async {
    Future.delayed(const Duration(milliseconds: 1500), () async {
      commonEventBus.fire(MainTabChange(strName: listTitles[currentIndex]));
      UpDateUtil.getUpdateInfo();
    });
  }

  Future<void> youthModeListen(YouthModeStateEvent event ) async {
    nInitModePage.value = event.bOpen ? 1 : 0;
  }

  void _initTabIndexData() {
    ///构造相应的数据结构
    for (int nIndex = 0; nIndex < listTitles.length; nIndex++) {
      listTab.add(TabIndexData(strName: listTitles[nIndex], nIndex: nIndex));
    }
  }

  void setCurrentIndex(index) {
    if (index == currentIndex) {
      return;
    }

    currentIndex = index;
    if (DynamicTabInfo.listNavTabInfo.isNotEmpty) {
      bShowRecommend = DynamicTabInfo.listNavTabInfo[currentIndex].id == 1;
    } else {
      bShowRecommend = currentIndex == 0;
    }

    commonEventBus.fire(MainTabChange(strName: listTitles[currentIndex]));
    pageController.jumpToPage(index);
    update(["bottomIndex"]);
  }

  void jumpToTabName(String strTabName) {
    int nIndex = listTitles.indexWhere((element) => element == strTabName);
    if (nIndex < 0) {
      return;
    }

    setCurrentIndex(nIndex);
  }

  @override
  void dispose() {
    _sub?.cancel();
    eventYouthMode?.cancel();
    super.dispose();
  }
}
