import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_messages/tab_message_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TabMessageView extends GetView<TabMessageController>
    with CommonWidgetPub {
  const TabMessageView({super.key});

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(child: <PERSON><PERSON><PERSON>(top: false, child: _mainContent()));
  }

  Widget _mainContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///状态栏
            SizedBox(height: 44.h),
            ///标题栏
            SizedBox(height: 44.h, child: _appBarContent()),
            SizedBox(height: 28.h),
            ///点赞 评论
            _commonItem(AssetsRes.itemDianZan, "点赞", "暂无点赞消息哦"),
            SizedBox(height: 24.h),
            _commonItem(AssetsRes.itemPingLun, "评论", "暂无评论哦"),
            SizedBox(height: 24.h),
            ColoredBox(color: BizColors.rgb100fafafa, child: SizedBox(width: 1.sw, height: 5.h)),
            SizedBox(height: 28.h),
            ///活动智能助理等
            _commonItem(AssetsRes.itemHuoDong, "热门活动", "好礼大放送"),
            SizedBox(height: 24.h),
            _commonItem(AssetsRes.itemZhiNeng, "智能助理", "VIP已到账"),
          ],
        ),
      ),
    );
  }

  ///标题栏
  Widget _appBarContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text("消息中心", style: TextStyle(fontSize: 18.sp, color: BizColors.rgb1000c1018),),
        const Spacer(),
        ImageLoadView(AssetsRes.itemQingChu, width: 14.w, height: 14.w),
        SizedBox(width: 4.w),
        Text("一键清除", style: TextStyle(fontSize: 14.sp, color: BizColors.rgb100939eb3),),
      ],
    );
  }
  ///通用条目
  Widget _commonItem(String strIconImg, String title, String contentText, {int nRedNum = 0}) {
    return SizedBox(
      width: 1.sw,
      height: 48.h,
      child: Row(
        children: [
          ///头像和红点
          SizedBox(
            width: 48.w,
            height: 48.w,
            child: Stack(
              alignment: Alignment.center,
              children: [
                ImageLoadView(strIconImg, width: 48.w, height: 48.w),
                Positioned(top: 0, right: 0, child: redPointNum(nRedNum)),
              ],
            ),
          ),
          ///间隔
          SizedBox(width: 12.w,),
          ///标题和内容列
          SizedBox(
            height: 48.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, color: BizColors.rgb1000c1018)),
                Text(contentText, style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3)),
              ],
            ),
          ),
          const Spacer(),
          ///右边的箭头
          ImageLoadView(AssetsRes.navSmallRightIcon,width: 16.w,height: 16.w,),
        ],
      ),
    );
  }
}
