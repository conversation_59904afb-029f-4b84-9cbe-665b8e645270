import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/edit_info/edit_info_main_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class EditInfoMainPage extends NormalPageViewBase<EditInfoMainController> {
  EditInfoMainPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  EditInfoMainPageState createState() => EditInfoMainPageState();
}

class EditInfoMainPageState extends NormalPageBaseViewState<EditInfoMainController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(onLeadingPress: () {
      Get.back();
    });
  }

  @override
  Widget bodyContentWidget() {
    return GetBuilder<EditInfoMainController>(
      id: "infoInit",
      tag: widget.tag,
      builder: (_) {
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16.h,),
                _imageHead(),
                SizedBox(height: 16.h),
                ///具体要编辑的条目
                ///星雪号
                SettingCommon(iconImg: AssetsRes.iconIdNumber, title: "星雪号", content: '${controller.userInfo.id}',navWidget: Container(margin:EdgeInsets.only(left: 5.w),width: 16.w,height: 16.w,),),
                SettingCommon(iconImg: AssetsRes.iconNickName, title: "昵称",   content: controller.userInfo.strNickName,onPressed: controller.onEditNickName,),
                SettingCommon(iconImg: AssetsRes.iconIdNumber, title: "个人描述", content: controller.userInfo.strDesc,contentMaxWidth: 180,onPressed: controller.onEditIntro,),
                SettingCommon(iconImg: AssetsRes.iconGender, title: "性别", content: controller.userInfo.nGender == 1 ? "男" : "女",onPressed: controller.onEditGender,),
                SettingCommon(iconImg: AssetsRes.iconBirthday, title: "生日", content: controller.userInfo.strBirthday,onPressed: controller.onEditBirthday,),
                SettingCommon(iconImg: AssetsRes.iconArea, title: "地区", content: UserInfoUtil.getUserInfoArea(controller.userInfo), contentMaxWidth: 210,onPressed: controller.onEditArea,),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _imageHead() {
    return InkWell(
      onTap: controller.onEditHeadPic,
      child: Center(
        child: SizedBox(
          width: 90.w,
          height: 90.w,
          child: Stack(
            alignment: Alignment.center,
            children: [
              ImageLoadView(controller.userInfo.strAvatar, width: 90.w, height: 90.w,shape: BoxShape.circle,),
              Container(
                width: 90.w,
                height: 90.w,
                decoration: const BoxDecoration(shape: BoxShape.circle,color: Colors.black38),
              ),
              // Image.asset(AssetsRes.iconEditBtn, width: 30.w, height: 30.w),
            ],
          ),
        ),
      ),
    );
  }

  @override
  EditInfoMainController createController() {
    return EditInfoMainController();
  }

}