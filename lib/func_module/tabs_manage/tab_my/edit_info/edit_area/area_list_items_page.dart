import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AreaInfoItemsList extends StatelessWidget {

  final int nCurAreaId;
  final List<AreaInfoUnion> areaItems;
  final bool bShowTailNav;
  const AreaInfoItemsList({super.key, this.areaItems = const [],this.nCurAreaId = -1,this.bShowTailNav = false,});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: areaItems.length,
        itemBuilder: (context, index) {
          return _areaItem(index);
        });
  }

  Widget _areaItem( int nIndex) {
     AreaInfoUnion areaInfoUnion = areaItems[nIndex];
     String strLetter = ToolsUtil.getPinyin(areaInfoUnion.strName, uppercase: true);
     return Stack(
       alignment: AlignmentDirectional.centerStart,
       children: [
       Text(strLetter,style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100939eb3),),
       SettingCommon(title: areaInfoUnion.strName,navWidget: bShowTailNav ? null : Container(),onPressed: () async {
         commonEventBus.fire(AreaItemClickEvent(itemInfo: areaInfoUnion,nLevel: areaInfoUnion.nLevel));
       },),
     ],);
  }

}