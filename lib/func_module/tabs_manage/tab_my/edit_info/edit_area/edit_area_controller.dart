import 'dart:async';

import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:get/get.dart';

class AreaInfoController extends NormalPageControllerBase {

  int nIndexNav = 1;
  List<String> navTip = ["省份/地区", "城市", "区/县"];
  bool bShowArrow = true;
  List<AreaInfoUnion> listAreaItems = [];

  final int maxLenArea = 3;

  ///点击条目的通知
  StreamSubscription? eventAreaItem;

  ///用户已经选择的数据压栈
  List<AreaInfoUnion> stackSel = [];

  String strLevelTextTop = "";
  String strLevelTextSec = "";
  String strLevelTextThd = "";
  
  @override
  void onInit() {
    super.onInit();
    eventAreaItem = commonEventBus.on<AreaItemClickEvent>().listen(onAreaItemClick);
    getAreaInfo();
  }

  Future<void> onAreaItemClick(AreaItemClickEvent event) async {
    int nIndexFind = stackSel.indexWhere((ele) => ele.nId == event.itemInfo.nId);
    if (nIndexFind >= 0) {
      return;
    }
    nIndexNav = event.itemInfo.nLevel;
    if (nIndexNav > navTip.length) {
      nIndexNav = navTip.length - 1;
    }
    bShowArrow = nIndexNav <= 0 ;
    if( nIndexNav <= maxLenArea - 1 ) {
      listAreaItems.clear();
    }
    if (stackSel.length >= maxLenArea) {
      stackSel[maxLenArea - 1] = event.itemInfo;
    } else {
      stackSel.add(event.itemInfo);
    }
    setTopTextByStack();
    update(["navTip", "areaSel"]);
    if (nIndexNav >= maxLenArea) {
      return;
    }
    await getAreaInfo(nParentId: event.itemInfo.nId);
  }

  void setTopTextByStack() {
    if (stackSel.length >= maxLenArea) {
      strLevelTextTop = stackSel[0].strName;
      strLevelTextSec = stackSel[1].strName;
      strLevelTextThd = stackSel[2].strName;
    } else if (stackSel.length == 2) {
      strLevelTextTop = stackSel[0].strName;
      strLevelTextSec = stackSel[1].strName;
      strLevelTextThd = "";
    } else if (stackSel.length == 1) {
      strLevelTextTop = stackSel[0].strName;
      strLevelTextSec = "";
      strLevelTextThd = "";
    }
  }

  Future<void> onLeadingPress() async {
    if (stackSel.isEmpty) {
      Get.back();
      return;
    }

    ///其它后退逻辑
    AreaInfoUnion intoLast = stackSel.last;
    stackSel.removeLast();
    setTopTextByStack();
    if (stackSel.isEmpty) {
      Get.back();
      return;
    }

    AreaInfoUnion intoPre = stackSel.last;
    listAreaItems.clear();
    nIndexNav = intoPre.nLevel;
    bShowArrow = nIndexNav <= 0 ;
    update(["navTip", "areaSel"]);
    getAreaInfo(nParentId: intoLast.nParentId);
  }

  Future<void> getAreaInfo({int nParentId = 0}) async {
    Map<String,dynamic> mapResult = await ApiReqInterface.getAreaInfo(parentId: nParentId);
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    List itemsInfo = mapResult["data"] ?? [];

    for(var item in itemsInfo ) {
      AreaInfoUnion info = AreaInfoUnion.fromJson(item);
      listAreaItems.add(info);
    }

    update(["areaInfoItems"]);
  }

  Future<void> onSave() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.modifyArea(stackSel[nIndexNav - 1].nId);
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    Get.back(result: 0);
  }


  @override
  void onClose() {
    eventAreaItem?.cancel();
    super.onClose();
  }
}