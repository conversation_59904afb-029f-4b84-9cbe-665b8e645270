import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/edit_info/edit_area/area_list_items_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/edit_info/edit_area/edit_area_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AreaInfoPage extends NormalPageViewBase<AreaInfoController> {
  AreaInfoPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  AreaInfoPageState createState() => AreaInfoPageState();
}

class AreaInfoPageState extends NormalPageBaseViewState<AreaInfoController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        strTitleText: "选择地区",
        onLeadingPress: () {
          controller.onLeadingPress();
        },
        actions: [saveBtn()]);
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return GetBuilder<AreaInfoController>(
      id: "infoMain",
      tag: widget.tag,
      builder: (_) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 12.h,),
              ///
              areaSelected(),
              SizedBox(height: 32.h,),
              _navTipInfo(),
              Expanded(child: _areaInfoItemsShow()),
            ],
          ),
        );
      },
    );
  }

  Widget _navTipInfo() {
    return GetBuilder<AreaInfoController>(
      tag: widget.tag,
      id: "navTip",
      builder: (_) {
        return navTitleTip(controller.navTip[controller.nIndexNav - 1]);
      },
    );
  }

  Widget _areaInfoItemsShow() {
    return GetBuilder<AreaInfoController>(
      tag: widget.tag,
      id: "areaInfoItems",
      builder: (_) {
        return controller.listAreaItems.isEmpty
            ? Container()
            : AreaInfoItemsList(areaItems: controller.listAreaItems,bShowTailNav: controller.bShowArrow,);
      },
    );
  }

  Widget areaSelected() {
    return GetBuilder<AreaInfoController>(
        id: "areaSel",
        tag: widget.tag,
        builder: (_) {
          return SizedBox(
              height: 48.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _areaBlockTip(controller.navTip[0], strSelArea: controller.strLevelTextTop),
                  SizedBox(width: 24.w),
                  _diverVertical(),
                  SizedBox(width: 24.w),
                  _areaBlockTip(controller.navTip[1], strSelArea: controller.strLevelTextSec),
                  SizedBox(width: 24.w),
                  _diverVertical(),
                  SizedBox(width: 24.w),
                  _areaBlockTip(controller.navTip[2], strSelArea: controller.strLevelTextThd),

                  ///导航提示
                ],
              ));
        });
  }

  Widget navTitleTip(String strAreaTip) {
    return Text("请选择 $strAreaTip",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100939eb3),);
  }

  Widget _diverVertical() {
    return Container(width: 0.5, height: 16.h, color: BizColors.rgb100d1d8e1);
  }

  Widget _areaBlockTip(String strTip,{String strSelArea = ""}) {
    return SizedBox(height: 48.h,width:60.w,child: Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
      Text(strTip,style: TextStyle(color: BizColors.rgb100939eb3,fontSize: 12.sp),),
      const Spacer(),
      ScrollableTextBox(
            height: 24,
            text: strSelArea.isEmpty ? "请选择" : strSelArea,
            textStyle: TextStyle(height: 1.05, color: strSelArea.isEmpty ? BizColors.rgb100d1d8e1 : BizColors.rgb1000c1018, fontSize: 13.sp),),
        ],),);
  }

  Widget saveBtn() {
    return TextButton(onPressed: controller.onSave, child: Text("保存",style: TextStyle(fontSize: 18.sp,color: BizColors.rgb10057b7fe),));
  }


  @override
  AreaInfoController createController() {
    return AreaInfoController();
  }

}

class ScrollableTextBox extends StatelessWidget {
  final String text;
  final double height;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;

  const ScrollableTextBox({
    Key? key,
    required this.text,
    required this.height,
    this.padding,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height, // 固定高度
      padding: padding ?? const EdgeInsets.all(0.0),
      child: Scrollbar( // 可选：显示滚动条
        thumbVisibility: true,
        child: SingleChildScrollView(
          child: Text(
            text,
            style: textStyle ?? const TextStyle(fontSize: 16),
            softWrap: true, // 自动换行
          ),
        ),
      ),
    );
  }
}
