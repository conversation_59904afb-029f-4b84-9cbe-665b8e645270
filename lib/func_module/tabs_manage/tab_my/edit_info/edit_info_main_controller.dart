import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/picker/single_picker.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:get/get.dart';

class EditInfoMainController extends NormalPageControllerBase {

  UserInfoDetail userInfo = UserInfoDetail();
  @override
  void onInit() {
    super.onInit();
    userInfo = Get.arguments?["userInfo"] ?? UserInfoDetail();
    update(["infoInit"]);
  }

  Future<void> onEditHeadPic() async {}

  Future<void> onEditNickName() async {
    ///1：编辑昵称  2：编辑简介
    dynamic ret = await SingleRoutePageManage.routeToEditTextPage(arguments: {"userInfo": userInfo, "editType": 1});
    if( ret == "${ResponseCodeParse.codeSuccess}") {
      getUserInfo();
    }
  }

  Future<void> onEditIntro() async {
    dynamic ret = await SingleRoutePageManage.routeToEditTextPage(arguments: {"userInfo": userInfo, "editType": 2});
    if( ret == "${ResponseCodeParse.codeSuccess}") {
      getUserInfo();
    }
  }

  ///获取用户详细信息
  Future<void> getUserInfo() async {
    Map<String, dynamic> mapResult = await UserInfoUtil.getUserInfoDetail();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    userInfo = mapResult["userInfo"] ?? UserInfoDetail();
    update(["infoInit"]);
  }

  Future<void> onEditGender() async {
    int nInitIndex = userInfo.nGender == 1 ? 0 : 1;
    showCupertinoPickerModal(
        context: Get.context!,
        items: ["男", "女"],
        initialIndex: nInitIndex,
        onConfirm: (int nIndex) {
          if (nIndex == nInitIndex) {
            return;
          }
          onModifyGenderResp(nIndex);
        });
  }

  Future<void> onModifyGenderResp(int nGenSelIndex) async {
    int nGen = nGenSelIndex == 0 ? 1 : 2;
    Map<String, dynamic> mapResult = await ApiReqInterface.modifyGender(nGen);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    getUserInfo();
  }

  Future<void> onEditBirthday() async {
    List<String>? parts = (userInfo.strBirthday).split(RegExp(r'\-'));
    String result = await SingleRoutePageManage.routeDateTimePickerPage(parts: parts);
    if (result.isEmpty) {
      return;
    }

    Map<String, dynamic> mapResult = await ApiReqInterface.modifyBirthday(result);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    getUserInfo();
  }

  Future<void> onEditArea() async {
    int nRet = await SingleRoutePageManage.routeToAreaInfo();
    if (nRet != ResponseCodeParse.codeSuccess) {
      return;
    }

    getUserInfo();
  }
}