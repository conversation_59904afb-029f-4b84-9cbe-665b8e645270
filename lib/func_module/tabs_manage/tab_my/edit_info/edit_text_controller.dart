import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:get/get.dart';

class EditTextController extends NormalPageControllerBase {

  UserInfoDetail userInfo = UserInfoDetail();
  int nEditType = 0;

  late TextEditingController editTextController;

  final RegExp _allowedChars = RegExp(r'^[\u4e00-\u9fa5a-zA-Z0-9]+$');

  int nNickNameLenMax = 24;
  int nIntroMaxLen = 300;

  @override
  void onInit() {
    super.onInit();
    userInfo = Get.arguments?["userInfo"] ?? UserInfoDetail();
    nEditType = Get.arguments?["editType"] ?? 0;
    ///昵称或简介
    String strText = nEditType == 1 ? userInfo.strNickName : userInfo.strDesc;
    if( nEditType == 1 ) {
      /// 初始化时过滤非法字符并截取前24个字符
      String filtered = strText.characters.where((char) => _allowedChars.hasMatch(char)).join();

      if (filtered.length > nNickNameLenMax) {
        filtered = filtered.substring(0, nNickNameLenMax);
      }
    } else {
      if( strText.length > nIntroMaxLen ) {
        strText = strText.substring(0, nIntroMaxLen);
      }
    }
    editTextController = TextEditingController(text: strText);
    update(["infoInit"]);
  }

  Future<void> onSave() async {
    Map<String, dynamic> mapResult = {};
    if (nEditType == 1) {
      mapResult = await ApiReqInterface.modifyNickName(editTextController.text);
    } else {
      mapResult = await ApiReqInterface.modifyIntro(editTextController.text);
    }
    int nRetCode = mapResult["code"] ?? -1;
    if (nRetCode == ResponseCodeParse.codeSuccess) {
      ToastUtil.showToast("保存成功");
    } else {
      return;
    }
    Get.back(result: '$nRetCode');
  }

  Future<void> onNickNameValueChanged(String strValue) async {
      String filtered = strValue.characters.where((char) => _allowedChars.hasMatch(char)).join();

      if (filtered.length > nNickNameLenMax) {
        filtered = filtered.substring(0, nNickNameLenMax);
      }

      if (filtered != strValue) {
        editTextController.value = TextEditingValue(text: filtered, selection: TextSelection.collapsed(offset: filtered.length),);
      }
      update(["infoMain"]);
  }

  // Future<void> onEditHeadPic() async {}
  //
  // Future<void> onEditNickName() async {}
  //
  // Future<void> onEditIntro() async {}
  //
  // Future<void> onEditGender() async {}
  //
  // Future<void> onEditBirthday() async {}
  //
  // Future<void> onEditArea() async {}
}