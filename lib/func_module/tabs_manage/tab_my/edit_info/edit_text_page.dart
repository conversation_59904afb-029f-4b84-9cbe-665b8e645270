import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/edit_info/edit_text_controller.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class EditTextPage extends NormalPageViewBase<EditTextController> {
  EditTextPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  EditTextPageState createState() => EditTextPageState();
}

class EditTextPageState extends NormalPageBaseViewState<EditTextController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        strTitleText: controller.nEditType == 1 ? "编辑昵称" : "编辑个人描述",
        onLeadingPress: () {
          Get.back();
        },
        actions: [saveBtn()]);
  }

  @override
  Widget bodyContentWidget() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          ToolsUtil.hideKeyboard();
        },
        child: _buildWidgetByType(controller.nEditType));
  }

  Widget _buildWidgetByType(int nType) {
    return GetBuilder<EditTextController>(
      id: "infoMain",
      tag: widget.tag,
      builder: (_) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              controller.nEditType == 1 ? _editNickName() : _editIntro(),
              SizedBox(height: 12.h),
              controller.nEditType == 1 ? _editNickNameTip() : _editIntroTip(),
            ],
          ),
        );
      },
    );
  }
  
  Widget saveBtn() {
    return TextButton(onPressed: controller.onSave, child: Text("保存",style: TextStyle(fontSize: 18.sp,color: BizColors.rgb10057b7fe),));
  }

  Widget _editNickName() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: TextField(
          controller: controller.editTextController,
          maxLength: controller.nNickNameLenMax, // 系统上限
          onChanged: controller.onNickNameValueChanged,
          decoration: const InputDecoration(
            border: InputBorder.none,
            counterText: '', // 隐藏计数字符
            hintText: '请输入内容',
          ),
          inputFormatters: [
            FilteringTextInputFormatter.deny(RegExp(r'[^\u4e00-\u9fa5a-zA-Z0-9]')), // 第一层过滤
          ],
        ),
      ),
    );
  }

  Widget _editNickNameTip() {
    return Text("请输入您的昵称，不超过24个字符，不含特殊符号",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100939eb3),);
  }

  Widget _editIntro() {
    return Container(
      height: 140.h,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: controller.editTextController,
        maxLength: controller.nIntroMaxLen,
        maxLines: null, // 自动换行
        expands: true,  // 填满容器高度
        textAlignVertical: TextAlignVertical.top,
        decoration: const InputDecoration(
          border: InputBorder.none,
          // counterText: '', // 不显示默认的字数提示
          hintText: '请输入...',
        ),
      ),
    );
  }

  Widget _editIntroTip() {
    return Text("7日内可以修改3次",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100939eb3),);
  }

  @override
  EditTextController createController() {
    return EditTextController();
  }

}