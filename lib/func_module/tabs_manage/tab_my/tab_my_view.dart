
import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/tab_my_controller.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TabMyView extends GetView<TabMyController> with CommonWidgetPub {
  const TabMyView({super.key});

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(child: SafeArea(top:false,child: Stack(alignment: Alignment.topCenter,children: [
      ///背景
      _bg(),
      ///主要内容
      _mainContent(),
    ],)),);
  }

  Widget _mainContent() {
    return Column(
      children: [
        SizedBox(height: 44.h),
        ///设置和主题
        _actions(),
        loginStateSwitch(),
        ///历史 关注 收藏
        _followLikeInfo(),
      ],
    );
  }

  Widget loginStateSwitch() {
    return GetBuilder<TabMyController>(
      id: "loginState",
      builder: (_) {
        return UserInfoUtil.isLogin() ? _logStateInfo() : _unLogState();
      },
    );
  }

  Widget _unLogState() {
    return SizedBox(
      // color: Colors.red,
      height: 180.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 20.h),

          ///未登录的头像
          InkWell(onTap: controller.toLogin,child: ImageLoadView(AssetsRes.iconUnLogin, width: 60.w, height: 60.w, shape: BoxShape.circle)),
          SizedBox(height: 4.h),

          InkWell(onTap: controller.toLogin,child: SizedBox(height: 34.h, child: Text('登录/注册',style: TextStyle(fontSize: 20.sp,color: BizColors.rgb100fafafa),))),
          SizedBox(height: 4.h),
          SizedBox(height: 18.h, child: Text('登录同步历史记录，开启精彩短剧之旅',style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100f9faff),)),

        ],
      ),
    );
  }


  Widget _logStateInfo() {
    return SizedBox(
      // color: Colors.red,
      height: 190.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 20.h),
        ///头像和昵称
        _nickAndHeadInfo(),
        SizedBox(height: 8.h,),

        ///性别年龄等信息
        SizedBox(height: 28.h, child: _genderAgeInfo()),
        SizedBox(height: 8.h,),
        SizedBox(height: 60.h,child: _descInfo(),),
      ],
    ),);
  }

  Widget _descInfo() {
    return GetBuilder<TabMyController>(
      id: "infoDetail",
      builder: (_) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          height: 60.h,
          width: 1.sw,
          child: Text(
            controller.userInfoDetail.strDesc,
            style: TextStyle(fontSize: 12.sp, color: Colors.white),
          ),
        );
      },
    );
  }

  ///性别年龄等信息
  Widget _genderAgeInfo() {
    return GetBuilder<TabMyController>(id: "infoDetail",builder: (_){
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: SizedBox(height: 28.h,
          child: Row(children: [
            _labelItemInfo(Image.asset(controller.userInfoDetail.nGender == 1 ? AssetsRes.iconGenderNan : AssetsRes.iconGenderNv,width: 10.w,height: 10.w,)),
            SizedBox(width: 8.w,),
            _labelItemInfo(Text("${ToolsUtil.calculateAge(controller.userInfoDetail.strBirthday)}岁",style: TextStyle(fontSize: 10.sp,color:BizColors.rgb1006c6faf),)),
            SizedBox(width: 8.w,),
            _labelItemInfo(Text(controller.calcStrAreaInfoDetail(),overflow:TextOverflow.ellipsis,style: TextStyle(fontSize: 10.sp,color:BizColors.rgb1006c6faf),)),
            SizedBox(width: 8.w,),
            const Spacer(),
            _editProfileBtn(),
          ],),),
      );
    });
  }

  ///编辑资料按钮
  Widget _editProfileBtn() {
    Widget item = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(AssetsRes.iconEdit,width: 12.w,height: 12.w,),
        SizedBox(width: 2.w,),
        Text("编辑资料",style: TextStyle(fontSize: 10.sp,color: BizColors.rgb1000c1018),)
      ],
    );
    return InkWell(onTap: controller.toEditInfoPage, child: _labelItemInfoEditBtn(item));
  }

  Widget _labelItemInfoEditBtn(Widget child,{double maxWidth = 96,double horizonPadding = 10}) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: 24.h, maxWidth: maxWidth.w),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: horizonPadding.w),
        decoration: BoxDecoration(color: BizColors.rgb40ffffff, border:Border.all(color: BizColors.rgb60ffffff,width: 0.8),borderRadius: const BorderRadius.all(Radius.circular(4))),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            child
          ],
        ),
      ),
    );
  }

  Widget _labelItemInfo(Widget child,{double maxWidth = 100,double horizonPadding = 10}) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: 20.h, maxHeight:28,maxWidth: maxWidth.w),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: horizonPadding.w),
        decoration: BoxDecoration(color: BizColors.rgb40ffffff, borderRadius: const BorderRadius.all(Radius.circular(12))),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            child
          ],
        ),
      ),
    );
  }

  Widget _followLikeInfo() {
    return Expanded(
      child: Container(
        width: 1.sw,
        padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 16.h),
        decoration: BoxDecoration(color: BizColors.rgb100ffffff, borderRadius: BorderRadius.only(topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _likeFollowTabSwitch(),
            Expanded(
              child: _catTabBarView(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _catTabBarView() {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: controller.catTabsController,
      children: controller.listCatDetailView,
    );
  }

  Widget _likeFollowTabSwitch() {
    return GetBuilder<TabMyController>(
      id: "togIndex",
      builder: (_) {
        return AnimatedToggleSwitch<int>.size(
          current: controller.nCurrentIndex,
          values: controller.listTab.indexed.map((e) => e.$1).toList(),
          borderWidth: 6,
          height: 40.h,
          iconOpacity: 1.0,
          selectedIconScale: 1.0,
          indicatorSize: const Size.fromWidth(351),
          customIconBuilder: (context, local, global) {
            return _indexedItem(local.value, controller.nCurrentIndex);
          },
          style: ToggleStyle(borderColor: Colors.transparent,borderRadius:BorderRadius.all(Radius.circular(8.r)),indicatorBorderRadius:BorderRadius.all(Radius.circular(8.r)),backgroundColor: BizColors.rgb100fafafa),
          styleBuilder: (value) => const ToggleStyle(indicatorColor: BizColors.rgb100ffffff),
          onChanged: controller.onIndexChanged,
        );
      },
    );
  }

  Widget _indexedItem(int nIndexNo, int nCurIndex) {
    bool bCurSel = nIndexNo == nCurIndex;
    String strIcon = bCurSel ? controller.listTab[nIndexNo].strIconActive : controller.listTab[nIndexNo].strIcon;
    String strText = controller.listTab[nIndexNo].strName;
    Color colorText = bCurSel ? BizColors.rgb1000c1018 : BizColors.rgb100939eb3;
    FontWeight ftWeight = bCurSel ? FontWeight.w400 : FontWeight.w400;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(strIcon, width: 16.w, height: 16.w),
        SizedBox(width: 8.w),
        Text(strText, textAlign: TextAlign.center, style: TextStyle(fontSize: 12.sp, color: colorText, fontWeight: ftWeight))
      ],
    );
  }

  Widget _nickAndHeadInfo() {
    return GetBuilder<TabMyController>(id:"infoDetail",builder: (_){
      return Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          child: SizedBox(
              height: 62.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start, children: [

                ///头像
                SizedBox(
                  width: 60.w,
                  height: 60.w,
                  child: ImageLoadView(controller.userInfoDetail.strAvatar, width: 56.w, height: 56.w, shape: BoxShape.circle,/*borderWidth: 2,borderColor: BizColors.rgb100ffffff,*/),),
                SizedBox(width: 12.w),
                ///昵称属地等
                _nickIpAndId(controller.userInfoDetail),
                SizedBox(width: 12.w),

              ],))
      );
    },);

  }

  Widget _nickIpAndId(UserInfoDetail infoItem) {
    return SizedBox(
      height: 62.h,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,

        children: [
         /// 昵称
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 200.w),
            child: Text(controller.userInfoDetail.strNickName, style: TextStyle(fontSize: 16.sp, color: BizColors.rgb100ffffff, fontWeight: FontWeight.w500),),
          ),
          ///星雪号：
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 200.w),
            child: Text('星雪号: ${infoItem.id}',style: TextStyle(fontSize: 12.sp,color: BizColors.rgb60ffffff),)),
          ///属地
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 200.w),
            child: Text('IP属地: ${infoItem.strIpLocation}', style: TextStyle(fontSize: 12.sp, color: BizColors.rgb60ffffff),),
          ),
        ],
      ),
    );
  }

  Widget _actions() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: SizedBox(
        height: 44.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // InkWell(
            //   onTap: controller.toTheme,
            //   child: Image.asset(AssetsRes.iconThemeChange, width: 28.w, height: 28.w),
            // ),
            SizedBox(width: 16.w),
            InkWell(
              onTap: controller.toSettingPage,
              child: Image.asset(AssetsRes.iconSettings, width: 28.w, height: 28.w),
            ),
          ],
        ),
      ),
    );
  }

  Widget _bg() {
    return Container(
      width: 1.sw,
      height: 336.h,
      decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.tabVideoHeadBg),fit: BoxFit.fill),),
    );
  }
}