import 'dart:async';

import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/feed_back_page.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/simple_alert_dialog.dart';
import 'package:flutter_common_base/util/util_file/file_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_common_base/util/util_webview/util_webview.dart';
import 'package:get/get.dart';

import 'younger_mode/young_mode_start/youth_mode_start_main_page.dart';

class SettingPageController extends CommonBaseController {
  ViewLoaderState historyDataLoadState = ViewLoaderState();

  bool bSysThemeMod = false;
  bool bPlayMod = false;

  int nCacheSize = 0;
  ///邮箱号
  ///
  StreamSubscription? eventLogin;

  @override
  void onInit() {
    super.onInit();
    dataLoadState.loadState = LoaderState.stateSucceed;
    eventLogin = commonEventBus.on<LoginStatusEvent>().listen(loginStatusChange);
    initCacheSize();
  }

  Future<void> loginStatusChange(LoginStatusEvent event) async {

  }

  Future<void> initCacheSize() async {
    nCacheSize = await FileUtil.getInstance().calCacheSize();
    update(["cacheInfo"]);
  }

  Future<void> onRetBack() async {
    Get.back();
  }

  ///账号与安全
  Future<void> accountSafety() async {
    SingleRoutePageManage.routeToAccountSafePage(arguments: {"userInfo": UserInfoUtil.getUserInfo()});
  }

  ///钱包
  Future<void> walletPage() async {
    SingleRoutePageManage.routeToWallerPage();
  }

  ///跳转到青少年模式
  Future<void> toYoungMode() async {
    Get.to(() => YouthModeStartMainPage(isGlobal: false,tag: ToolsUtil().genUuId(),));
  }

  ///联系我们跳转
  Future<void> toContactUs() async {
    WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.contactUs);
    // ToolsUtil.launchMyUrl(BizStrings.homeMainAddress);
  }

  Future<void> toFeedBack() async {
    Get.to(() => FeedBackView());
  }

  /// 字体大小设置
  Future<void> toFontSizePage() async {
    SingleRoutePageManage.routeToFontSizeSettingPage();
  }

  ///关于我们
  Future<void> onAboutUs() async {
    SingleRoutePageManage.routeToAboutUs();
  }

  ///退出登录
  Future<void> onLogOut() async {
    int nRet = await SimpleAlertDialog.show(Get.context!, "确定要退出登录吗？", "取消", "退出",contentCenter: true,strTitle: "提示");
    if( nRet == SimpleAlertDialog.alertCancel) {
      return;
    }
    
    Map<String,dynamic> mapResult = await ApiReqInterface.logOut();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess ) {
      return;
    }

    ///真的要退出
    StorageUtil.remove(SPKey.keyToken);
    UserInfoUtil.setUserInfo(UserInfoDetail(), isLogin: false);
    SingleRoutePageManage.routeMainPage(LanStrings.tabMy);
  }
  ///清理缓存
  Future<void> onClearCache() async {
    await FileUtil.getInstance().clearCache();
    initCacheSize();
  }

  Future<void> onSysThemeSwitch() async {
    bSysThemeMod = !bSysThemeMod;
    update(["switchState"]);
  }

  Future<void> onPlayModeSwitch() async {
    bPlayMod = !bPlayMod;
    update(["switchState"]);
  }
}