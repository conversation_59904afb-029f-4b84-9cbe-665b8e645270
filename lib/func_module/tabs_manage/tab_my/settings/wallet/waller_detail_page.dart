import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/account_safe_page_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/wallet/waller_detail_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class WallerDetailPage extends NormalPageViewBase<WallerDetailController> {
  WallerDetailPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  WallerDetailPageState createState() => WallerDetailPageState();
}

class WallerDetailPageState extends NormalPageBaseViewState<WallerDetailController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(onLeadingPress: () {
      Get.back();
    },strTitleText: "钱包");
  }

  @override
  Widget bodyContentWidget() {
    return GetBuilder<WallerDetailController>(
      id: "infoInit",
      tag: widget.tag,
      builder: (_) {
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16.h),

                ///具体要编辑的条目
                SettingCommon(
                  title: "余额",
                  content: controller.userInfo.strPhone,
                  onPressed: controller.balance,
                ),

                SettingCommon(
                  title: "账单",
                  onPressed: controller.billList,
                ),
              ],
            ),
          ),
        );
      },
    );
  }


  @override
  WallerDetailController createController() {
    return WallerDetailController();
  }

}