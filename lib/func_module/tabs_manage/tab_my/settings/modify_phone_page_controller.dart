import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/data_model/user_info/login_type_info.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:get/get.dart';

class ModifyPhonePageController extends NormalPageControllerBase {
  UserInfoDetail userInfo = UserInfoDetail();

  String strTextCodeDesc = "获取验证码";
  String strTextCodeDescDefault = "获取验证码";
  final int nCountSeconds = 60;
  int nTimerCountShow = 60;
  int nIntervalMicroSeconds = 1000;

  ///电话号码
  late TextEditingController editPhoneController;
  late TextEditingController editCodeController;

  ///倒计时
  Timer? timerCount;
  bool bTimeUp = true;

  ///页面来源
  String strFrom = "";

  ///返回键是否可以返回
  bool bCanRet = true;

  @override
  void onInit() {
    super.onInit();
    editPhoneController = TextEditingController();
    editCodeController = TextEditingController();
    userInfo = Get.arguments?["userInfo"] ?? UserInfoDetail();

    ///页面来源
    strFrom = Get.arguments?["fromSrc"] ?? "";

    ///是否能返回
    bCanRet = strFrom != PageFromReason.fromReasonBind;
  }

  void _startTimerCount() {
    timerCount?.cancel();
    timerCount = Timer.periodic(
        Duration(milliseconds: nIntervalMicroSeconds), _timerCallback);
  }

  Future<void> _timerCallback(Timer value) async {
    nTimerCountShow = nTimerCountShow - 1;
    if (nTimerCountShow <= 0) {
      bTimeUp = true;
      strTextCodeDesc = strTextCodeDescDefault;
      timerCount?.cancel();
      timerCount = null;
    } else {
      strTextCodeDesc = '${nTimerCountShow}S 后重新获取';
      bTimeUp = false;
    }
    update(["codeCountDown"]);
  }

  Future<void> getVerifyCodeBtn() async {
    if (!bTimeUp) {
      return;
    }

    if (!ToolsUtil.isValidPhone(editPhoneController.text)) {
      ToastUtil.showToast("请输入正确的手机号");
      return;
    }

    sendVerifyCode(editPhoneController.text);
  }

  Future<void> sendVerifyCode(String strPhoneNumber) async {
    Map<String, dynamic> mapResult =
        await ApiReqInterface.sendChangePhoneCode(strPhone: strPhoneNumber);
    bool nRet = ((mapResult["code"] ?? -1) == ResponseCodeParse.codeSuccess);
    if (!nRet) {
      ToastUtil.showToast(mapResult["msg"] ?? "");
      return;
    }

    _startTimerCount();
  }

  Future<void> onModifyPhoneNumber() async {
    String strPhone = editPhoneController.text;
    if (!ToolsUtil.isValidPhone(strPhone)) {
      ToastUtil.showToast("请输入正确的手机号");
      return;
    }

    String strCode = editCodeController.text;
    if (strCode.isEmpty) {
      ToastUtil.showToast("验证码不能为空");
      return;
    }

    ///发请求
    Map<String, dynamic> mapResult =
        await ApiReqInterface.modifyPhoneNumber(strPhone, strCode);
    int nRet = mapResult["code"] ?? -1;
    if (nRet != ResponseCodeParse.codeSuccess) {
      return;
    }

    ///跳转到账号与安全页面
    Get.back();
    if (strFrom == PageFromReason.fromReasonBind) {
      UserInfoUtil.getUserInfoDetail();
    }
  }

  @override
  void onClose() {
    editPhoneController.dispose();
    editCodeController.dispose();
    timerCount?.cancel();
    super.onClose();
  }
}
