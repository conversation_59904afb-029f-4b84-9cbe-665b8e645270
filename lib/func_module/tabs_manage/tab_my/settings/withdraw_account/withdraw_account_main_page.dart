import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/withdraw_account/withdraw_account_main_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class WithdrawAccountMainPage extends NormalPageViewBase<WithdrawAccountMainController> {
  WithdrawAccountMainPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  WithdrawAccountMainPageState createState() => WithdrawAccountMainPageState();
}

class WithdrawAccountMainPageState extends NormalPageBaseViewState<WithdrawAccountMainController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "注销须知"
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 12.h),
          _modeSwitch(),
          SizedBox(height: 12.h),
          mainContentTip(),
          const Spacer(),
          _buildPrivacyAgreement(),
          SizedBox(height: 6.h),
          _submitBtn(),
          SizedBox(height: BizValues.bottomMargin)
        ],
      ),
    );
  }

  Widget _submitBtn() {
    return GetBuilder<WithdrawAccountMainController>(
      tag: widget.tag,
      id: "submitCounter",
      builder: (_) {
        return CustomButtonView(
          text: controller.strBtnText,
          borderRadius: 12.r,
          isEnabled: controller.bTimeUp,
          onTap: controller.onWithdraw,
        );
      },
    );
  }

  /// 抽离的隐私协议部分
  Widget _buildPrivacyAgreement() {
    return GetBuilder<WithdrawAccountMainController>(
      id: "provState",
      tag: widget.tag,
      builder: (_) {
        return GestureDetector(
            onTap: () {
              controller.onRulePress();
            },
            child: SizedBox(
                width: 1.sw,
                height: 20.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(onTap: () {
                      controller.onRulePress();
                    },child: controller.bAgreed ? const Icon(Icons.check_circle,color: Colors.blue,size: 16,) : Icon(Icons.radio_button_unchecked,color: Colors.blue,size: 16,)),
                    SizedBox(width: 8.w,),
                    // Text("同意", style: TextStyle(fontSize: 10.sp, color: BizColors.rgb10063666a)),
                    InkWell(
                        onTap: () {
                          controller.onRulePress();
                        },
                        child: Text("阅读并同意以上注销须知",
                            style: TextStyle(
                              height: 1.1,
                              fontSize: 12.sp,
                              color: BizColors.rgb100999999,
                              decorationColor: BizColors.rgb10057b7fe,
                            ))),
                  ],
                )));
      },);
// 隐私协议的文本部分
  }

  Widget mainContentTip() {
    return SizedBox(
        height: 280.h,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          children: [
            textRuleTitle("1、账号内财产相关"),
            textRule("注销后，账号内看点、金币等财产将被清零，请您务必知晓并确认，没有待结算资金问题"),
            SizedBox(height: 12.h),
            textRuleTitle("2、账号相关信息将被清空"),
            textRule("个人资料和历史信息(包括头像、昵称、观看历史等)将被清空"),
            SizedBox(height: 12.h),
            textRuleTitle("3、已订购产品将视为自动放弃"),
            textRule("该账号已购买的会员等产品将视为自动放弃"),
            SizedBox(height: 12.h),
            textRuleTitle("4、账号注销后无法找回"),
            textRule("账号注销后，即使您使用相同的手机号码或苹果账号再次注册，依旧无法找回之前的账号信息，会以新的用户身份进行登录"),
          ],
        ));
  }

  Widget textRule(String text) {
    return ConstrainedBox(constraints: BoxConstraints(minHeight: 18.h,maxWidth: 1.sw),child: Text(text,style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100999999),),);
  }

  Widget textRuleTitle(String text) {
    return ConstrainedBox(constraints: BoxConstraints(minHeight: 18.h,maxWidth: 1.sw),child: Text(text,style: TextStyle(fontSize: 14.sp,color: BizColors.rgb1000c1018),),);
  }

  Widget _modeLogo() {
    return SizedBox(
      height: 201.w,
      child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
        SizedBox(width: 201.w, height: 201.w, child: Image.asset(AssetsRes.iconYouthModeMain, width: 201.w, height: 201.w, fit: BoxFit.fill))
      ]),
    );
  }

  Widget _modeSwitch() {
    return Text("注销账号是不可恢复的操作，在您提交注销申请前，请先确认以下信息",
        style: TextStyle(fontSize: 14.sp, color: BizColors.rgb1000c1018));
  }

  @override
  WithdrawAccountMainController createController() {
    return WithdrawAccountMainController();
  }

}