import 'dart:async';

import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/simple_alert_dialog.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:get/get.dart';

class WithdrawAccountMainController extends NormalPageControllerBase {
  bool bAgreed = false;
  Timer? _timer;
  int nDurationSeconds = 30;
  int nMaxCount = 30;
  bool bTimeUp = false;
  String strBtnTextSrc = "注销账号";
  String strBtnText = "注销账号";
  @override
  void onInit() {
    super.onInit();
    _startTimerCount();
  }

  void _startTimerCount() {
    strBtnText = '$strBtnTextSrc(${nMaxCount}S)';
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      nMaxCount = nMaxCount - 1;
      if( nMaxCount <= 0 ) {
        timer.cancel();
        bTimeUp = true;
        strBtnText = strBtnTextSrc;
      } else {
        strBtnText = '$strBtnTextSrc(${nMaxCount}S)';
      }
      update(["submitCounter"]);

    });
  }

  Future<void> onRulePress() async {
    bAgreed = !bAgreed;
    update(["provState"]);
  }

  Future<void> onWithdraw() async {
    if(!bAgreed ) {
      ToastUtil.showToast("请阅读并同意注销须知");
      return;
    }
    int nResult = await SimpleAlertDialog.show(Get.context!, strTitle: "账号注销",contentCenter: true,"账号注销将无法找回", "再想想", "注销");
    if (nResult != SimpleAlertDialog.alertOk) {
      return;
    }
    ///进行注销
    Map<String, dynamic> mapResult = await ApiReqInterface.withdrawAccount();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    ToastUtil.showToast("操作成功");

    ///真的要退出
    UserInfoUtil.setUserInfo(UserInfoDetail());
    StorageUtil.remove(SPKey.keyToken);
    SingleRoutePageManage.routeMainPage(LanStrings.tabMy);

  }


  @override
  void onClose() {
    super.onClose();
  }
}