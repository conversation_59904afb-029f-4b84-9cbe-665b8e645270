import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/account_safe_page_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AccountSafePageView extends NormalPageViewBase<AccountSafePageController> {
  AccountSafePageView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  AccountSafePageState createState() => AccountSafePageState();
}

class AccountSafePageState extends NormalPageBaseViewState<AccountSafePageController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(onLeadingPress: () {
      Get.back();
    },strTitleText: "账号与安全");
  }

  @override
  Widget bodyContentWidget() {
    return GetBuilder<AccountSafePageController>(
      id: "infoInit",
      tag: widget.tag,
      builder: (_) {
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16.h),

                ///具体要编辑的条目
                SettingCommon(
                  title: "手机号",
                  content: controller.userInfo.strPhone,
                  onPressed: controller.onVerifyPhoneCode,
                ),

                SettingCommon(
                  title: "账号注销",
                  onPressed: controller.withdrawAccount,
                ),
              ],
            ),
          ),
        );
      },
    );
  }


  @override
  AccountSafePageController createController() {
    return AccountSafePageController();
  }

}