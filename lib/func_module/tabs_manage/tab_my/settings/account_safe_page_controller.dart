import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:get/get.dart';

class AccountSafePageController extends NormalPageControllerBase {

  UserInfoDetail userInfo = UserInfoDetail();
  String strPhoneNumber = "";
  @override
  void onInit() {
    super.onInit();
    userInfo = Get.arguments?["userInfo"] ?? UserInfoDetail();

    update(["infoInit"]);
  }

  ///获取用户详细信息
  Future<void> getUserInfo() async {
    Map<String, dynamic> mapResult = await UserInfoUtil.getUserInfoDetail();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    userInfo = mapResult["userInfo"] ?? UserInfoDetail();
    update(["infoInit"]);
  }

  Future<void> onVerifyPhoneCode() async {
    await SingleRoutePageManage.routeToModifyPhoneVerifyCode();
    getUserInfo();
  }

  Future<void> withdrawAccount() async {
    SingleRoutePageManage.routeToAccountWithdraw();
  }

}