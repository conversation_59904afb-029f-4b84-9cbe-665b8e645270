import 'package:flutter/material.dart';

class FontPreviewPage extends StatefulWidget {
  @override
  _FontPreviewPageState createState() => _FontPreviewPageState();
}

class _FontPreviewPageState extends State<FontPreviewPage> {
  double _fontSize = 16.0;
  FontWeight _fontWeight = FontWeight.normal;
  String _selectedFontFamily = 'Roboto';

  final List<String> _fontFamilies = ['Roboto', 'Courier', 'Georgia', 'Times New Roman', 'Arial'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('字体设置预览')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 字体选择
            Row(
              children: [
                Text("字体: "),
                SizedBox(width: 10),
                DropdownButton<String>(
                  value: _selectedFontFamily,
                  items: _fontFamilies.map((font) {
                    return DropdownMenuItem(
                      value: font,
                      child: Text(font, style: TextStyle(fontFamily: font)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFontFamily = value!;
                    });
                  },
                ),
              ],
            ),
            SizedBox(height: 20),

            // 字号选择
            Row(
              children: [
                Text("字号: ${_fontSize.toStringAsFixed(0)}"),
                Expanded(
                  child: Slider(
                    min: 10,
                    max: 50,
                    value: _fontSize,
                    onChanged: (value) {
                      setState(() {
                        _fontSize = value;
                      });
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            // 粗细选择
            Row(
              children: [
                Text("粗细: "),
                DropdownButton<FontWeight>(
                  value: _fontWeight,
                  items: [
                    FontWeight.w100,
                    FontWeight.w300,
                    FontWeight.normal,
                    FontWeight.w500,
                    FontWeight.bold,
                    FontWeight.w900,
                  ].map((weight) {
                    return DropdownMenuItem(
                      value: weight,
                      child: Text(weight.toString().split('.').last),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _fontWeight = value!;
                    });
                  },
                ),
              ],
            ),
            Divider(height: 40),

            // 预览区域
            Expanded(
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                color: Colors.grey[100],
                child: Text(
                  '这是字体预览文本。\nThis is a font preview text.',
                  style: TextStyle(
                    fontSize: _fontSize,
                    fontWeight: _fontWeight,
                    fontFamily: _selectedFontFamily,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
