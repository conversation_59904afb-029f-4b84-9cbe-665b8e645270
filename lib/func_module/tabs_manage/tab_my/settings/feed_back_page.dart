import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class FeedBackView extends StatefulWidget {
  bool resizeToAvoidBottomInset = false;
  FeedBackView({super.key,});

  @override
  FeedBackPageState createState() => FeedBackPageState();
}

class FeedBackPageState extends  State<FeedBackView> with CommonWidgetPub{

  late TextEditingController editTextController;
  final int maxLen = 400;
  @override
  void initState() {
    super.initState();
    editTextController = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return SafeArea(top: false, bottom:false,child: mainBody());
  }

  /// 子类需要实现这个方法来构建 UI
  Widget mainBody() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        mainBg(),
        mainContent(),
      ],
    );
  }

  ///背景
  Widget mainBg() {
    return Container(width: 1.sw, height: 1.sh, color: Colors.white);
  }

  Widget mainContent() {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
      appBar: appbar(),
      body: bodyContentWidget(),
    );
  }
  PreferredSizeWidget appbar() {
    return customAppbar(strTitleText: "意见反馈", onLeadingPress: () {Get.back();});
  }

  Widget bodyContentWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        ToolsUtil.hideKeyboard();
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 24.h),
            Text("我们重视每一条反馈，感谢您为我们提供改进的方向",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb1000c1018),),
            SizedBox(height: 8.h),
            _editIntro(),
            const Spacer(),
            CustomButtonView(text: "提交反馈", onTap: commitFeedBack),
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  Future<void> commitFeedBack() async {
    if( editTextController.text.isEmpty ) {
      ToastUtil.showToast("请填写反馈内容");
      return;
    }

    Map<String, dynamic> mapResult = await ApiReqInterface.submitFeedback(editTextController.text);
    if (mapResult["code"] == ResponseCodeParse.codeSuccess) {
      ToastUtil.showToast("感谢你的反馈");
      Get.back();
    }
  }

  Widget _editIntro() {
    return Container(
      height: 250.h,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        textInputAction: TextInputAction.done,
        controller: editTextController,
        maxLength: maxLen,
        maxLines: null, // 自动换行
        expands: true,  // 填满容器高度
        textAlignVertical: TextAlignVertical.top,
        style: TextStyle(fontSize: 14.sp,color: BizColors.rgb1000c1018),
        decoration: InputDecoration(
          counterStyle: TextStyle(fontSize: 12.sp,color: BizColors.rgb100939eb3),
          border: InputBorder.none,
          // counterText: '', // 不显示默认的字数提示
          hintText: '请描述您的问题...',
          hintStyle: TextStyle(fontSize: 14.sp,color: BizColors.rgb100939eb3)
        ),
      ),
    );
  }

}