import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/modify_phone_page_controller.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ModifyPhonePageView extends NormalPageViewBase<ModifyPhonePageController> {
  ModifyPhonePageView({super.key, required super.isGlobal, super.tag, super.mapParam});
  @override
  ModifyPhonePageState createState() => ModifyPhonePageState();
}

class ModifyPhonePageState extends NormalPageBaseViewState<ModifyPhonePageController>{
  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
      bShowLeading: controller.bCanRet,
      onLeadingPress: () {
        if (!controller.bCanRet) {
          return;
        }
        Get.back();
      },
    );
  }

  @override
  Widget bodyContentWidget() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          ToolsUtil.hideKeyboard();
        },
        child: _buildWidgetByType());
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 40.h),
          Text("请输入新手机号",style: TextStyle(fontSize: 24.sp,color: BizColors.rgb1000c1018),),
          SizedBox(height: 16.h),
          Text("验证后可换绑",style: TextStyle(fontSize: 16.sp,color: BizColors.rgb100d1d8e1),),
          SizedBox(height: 48.h),
          _editInfoBlock(),
        ],
      ),
    );
  }


  Widget _editInfoBlock() {
    return GetBuilder<ModifyPhonePageController>(id: "phoneInfo", tag:widget.tag,builder: (_) {
      return Column(children: [
        phoneEdit(editController: controller.editPhoneController),
        SizedBox(height: 28.h,),
        verifyCodeEdit(editController: controller.editCodeController),
        SizedBox(height: 48.h,),
        CustomButtonView(text: LanStrings.commonConfirm.tr,onTap: controller.onModifyPhoneNumber,),
      ],);
    });
  }

  ///电话号码编辑框
  Widget phoneEdit({TextEditingController? editController}) {
    return Stack(
      alignment: Alignment.centerLeft,
      children: <Widget>[
        Container(
          alignment: Alignment.center,
          height: 48.h,
          width: 1.sw,
          child: TextFormField(
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              LengthLimitingTextInputFormatter(11) ///限制长度
            ],
            cursorColor: BizColors.mainLineGradientSecond,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
            controller: editController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 64.w),
              filled: true,
              fillColor: BizColors.rgb100fafafa,
              hintText: LanStrings.tipInputPhoneNumber.tr,
/*                suffixIcon: phoneSuffixIcon(controller.accountEditController.text.isNotEmpty, () {
                controller.clearPhoneEdit();
              }),*/
              hintStyle: TextStyle(fontSize: 16.sp, color: BizColors.rgb100d1d8e1),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(29), borderSide: BorderSide.none),),
            onChanged: (value) {
              // controller.onPhoneEditContextChanged(value);
            },
            onSaved: (value) {
              // strUserPhone = value;
            },
          ),
        ),
        Positioned(
          left: 12.w,
          child: InkWell(
            onTap: () {
              // controller.countryCodePage();
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("+86", style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
                ),
                SizedBox(width: 4.w,),
                Image.asset(AssetsRes.loginCodeArrow, width: 12.w, height: 12.w, fit: BoxFit.cover,),
                SizedBox(width: 12.w,),
              ],
            ),
          ),
        ),
        // Positioned(right:1.w,child: phoneSuffixIcon(controller.bShowPhoneSuffix, () {
        //   // controller.clearPhoneEdit();
        // }))
      ],
    );
  }

  ///电话号码编辑框
  Widget verifyCodeEdit({TextEditingController? editController}) {
    return Stack(
      alignment: Alignment.centerLeft,
      children: <Widget>[
        Container(
          alignment: Alignment.center,
          height: 48.h,
          width: 1.sw,
          child: TextFormField(
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              LengthLimitingTextInputFormatter(6) ///限制长度
            ],
            cursorColor: BizColors.mainLineGradientSecond,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
            controller: editController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 12.w),
              filled: true,
              fillColor: BizColors.rgb100fafafa,
              hintText: LanStrings.tipInputVerifyCode.tr,
              /*                suffixIcon: phoneSuffixIcon(controller.accountEditController.text.isNotEmpty, () {
                controller.clearPhoneEdit();
              }),*/
              hintStyle: TextStyle(fontSize: 16.sp, color: BizColors.rgb100d1d8e1),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(29), borderSide: BorderSide.none),),
            onChanged: (value) {
              // controller.onPhoneEditContextChanged(value);
            },
            onSaved: (value) {
              // strUserPhone = value;
            },
          ),
        ),
        Positioned(right:40.w,child: _getVerifyCodeBtn()),
      ],
    );
  }

  ///获取验证码按钮
  Widget _getVerifyCodeBtn() {
    return GetBuilder<ModifyPhonePageController>(
      id: "codeCountDown",
      tag: widget.tag,
      builder: (_) {
        return TextButton(
            onPressed: controller.getVerifyCodeBtn,
            child: Text(controller.strTextCodeDesc,
                style: TextStyle(color: controller.bTimeUp ? BizColors.rgb10057b7fe : Colors.black12, fontSize: 16.sp)));
      },
    );
  }

  @override
  ModifyPhonePageController createController() {
    return ModifyPhonePageController();
  }

}