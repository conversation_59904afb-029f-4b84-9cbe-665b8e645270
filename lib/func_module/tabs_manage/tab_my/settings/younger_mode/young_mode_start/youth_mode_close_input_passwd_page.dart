import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_close_input_passwd_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class YouthModeCloseInputPasswdPage extends NormalPageViewBase<YouthModeCloseInputPasswdController> {
  YouthModeCloseInputPasswdPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  YouthModeCloseInputPasswdPageState createState() => YouthModeCloseInputPasswdPageState();
}

class YouthModeCloseInputPasswdPageState extends NormalPageBaseViewState<YouthModeCloseInputPasswdController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "输入密码"
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Spacer(),
          verifyCodePin(length: 4, editController: controller.pwdEditController, callback:controller.codeCompleteCallBack,valueChanged: controller.onValueChg),
          _tipInfo(),
          const Spacer(),
          SizedBox(height: 4.h),
          Obx(() => CustomButtonView(text: "确认", borderRadius: 12.r, isEnabled: controller.bCanSubmit.value,onTap: controller.onSubmit)),
          SizedBox(height: BizValues.bottomMargin)
        ],
      ),
    );
  }

  Widget _tipInfo() {
    return SizedBox(height: 36.h,child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
      Text("忘记密码？",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb1000c1018),),
      InkWell(onTap:() {
        controller.onFeedBack();
      },child: Text("点击申诉",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb10057b7fe),)),

    ],),);
  }
  Widget mainContentTip() {
    return SizedBox(
        height: 280.h,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          children: [
          ],
        ));
  }



  @override
  YouthModeCloseInputPasswdController createController() {
    return YouthModeCloseInputPasswdController();
  }

}