import 'dart:async';

import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_set_pwd_page.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:get/get.dart';

class YouthModeStartMainController extends NormalPageControllerBase {

  bool bAgreed = false;
  @override
  void onInit() {
    super.onInit();
  }

  void _startTimerCount() {
  }

  Future<void> onRulePress() async {
    bAgreed = !bAgreed;
    update(["provState"]);
  }

  Future<void> onToStartYouthMode() async {
    if(!bAgreed ) {
      ToastUtil.showToast("请先选择同意《儿童个人信息保护规则》");
      return;
    }
    Get.to(() => YouthModeSetPwdPage(isGlobal: false,tag: ToolsUtil().genUuId(),));
  }


  @override
  void onClose() {
    super.onClose();
  }
}