import 'dart:async';

import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_close_main_page.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:get/get.dart';

class YouthModeOpenedMainController extends NormalPageControllerBase {

  bool bAgreed = false;
  @override
  void onInit() {
    super.onInit();
  }

  Future<void> onRulePress() async {
    bAgreed = !bAgreed;
    update(["provState"]);
  }

  Future<void> onToCloseYouthMode() async {
    Get.to(() => YouthModeCloseMainPage(isGlobal: false,tag: ToolsUtil().genUuId(),));
  }


  @override
  void onClose() {
    super.onClose();
  }
}