import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class YouthModeAppealView extends StatefulWidget {
  bool resizeToAvoidBottomInset = false;
  YouthModeAppealView({super.key,});

  @override
  YouthModeAppealViewState createState() => YouthModeAppealViewState();
}

class YouthModeAppealViewState extends  State<YouthModeAppealView> with CommonWidgetPub{

  late TextEditingController editTextController;
  late TextEditingController editPhoneController;
  final int maxLen = 400;
  @override
  void initState() {
    super.initState();
    editTextController = TextEditingController();
    editPhoneController = TextEditingController();
  }

  @override
  void dispose() {
    editTextController.dispose();
    editPhoneController.dispose();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return SafeArea(top: false, bottom:false,child: mainBody());
  }

  /// 子类需要实现这个方法来构建 UI
  Widget mainBody() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        mainBg(),
        mainContent(),
      ],
    );
  }

  ///背景
  Widget mainBg() {
    return Container(width: 1.sw, height: 1.sh, color: Colors.white);
  }

  Widget mainContent() {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
      appBar: appbar(),
      body: bodyContentWidget(),
    );
  }
  PreferredSizeWidget appbar() {
    return customAppbar(strTitleText: "申诉", onLeadingPress: () {Get.back();});
  }

  Widget bodyContentWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 24.h),
          Text("请详细输入您申诉的理由，以便我们进行核实",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb1000c1018),),
          SizedBox(height: 8.h),
          _editIntro(),
          SizedBox(height: 16.h),
          phoneEdit(editController: editPhoneController),
          const Spacer(),
          CustomButtonView(text: "申诉", onTap: commitFeedBack),
          SizedBox(height: BizValues.bottomMargin),
        ],
      ),
    );
  }

  ///电话号码编辑框
  Widget phoneEdit({TextEditingController? editController}) {
    return SizedBox(
      height: 36.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text("手机号码",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb1000c1018,fontWeight: FontWeight.w600),),
          SizedBox(width: 8.w,height: 36,),
          const Spacer(),
          SizedBox(
            height: 36.h,
            width: 280.w,
            child: TextFormField(
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                LengthLimitingTextInputFormatter(11) ///限制长度
              ],
              cursorColor: BizColors.mainLineGradientSecond,
              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
              controller: editController,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                contentPadding: EdgeInsets.only(left: 12.w),
                filled: true,
                fillColor: BizColors.rgb100fafafa,
                hintText: "请输入内容",
      /*                suffixIcon: phoneSuffixIcon(controller.accountEditController.text.isNotEmpty, () {
                  controller.clearPhoneEdit();
                }),*/
                hintStyle: TextStyle(fontSize: 14.sp, color: BizColors.rgb100939eb3),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(2), borderSide: BorderSide.none),),
              onChanged: (value) {
                // controller.onPhoneEditContextChanged(value);
              },
              onSaved: (value) {
                // strUserPhone = value;
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> commitFeedBack() async {
    if( editTextController.text.isEmpty ) {
      ToastUtil.showToast("请填写申诉内容");
      return;
    }

    if (!ToolsUtil.isValidPhone(editPhoneController.text)) {
      ToastUtil.showToast("请输入正确的手机号");
      return;
    }

    Map<String, dynamic> mapResult = await ApiReqInterface.submitAppeal(editTextController.text, editPhoneController.text);
    if (mapResult["code"] == ResponseCodeParse.codeSuccess) {
      ToastUtil.showToast("操作完成");
      Get.back();
    }
  }

  Widget _editIntro() {
    return Container(
      height: 250.h,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: editTextController,
        maxLength: maxLen,
        maxLines: null, // 自动换行
        expands: true,  // 填满容器高度
        textAlignVertical: TextAlignVertical.top,
        style: TextStyle(fontSize: 14.sp,color: BizColors.rgb1000c1018),
        decoration: InputDecoration(
            counterStyle: TextStyle(fontSize: 12.sp,color: BizColors.rgb100939eb3),
            border: InputBorder.none,
            // counterText: '', // 不显示默认的字数提示
            hintText: '请输入...',
            hintStyle: TextStyle(fontSize: 14.sp,color: BizColors.rgb100939eb3)
        ),
      ),
    );
  }

}