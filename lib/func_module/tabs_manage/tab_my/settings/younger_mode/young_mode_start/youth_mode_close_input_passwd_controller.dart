import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_start_feedback_page.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:get/get.dart';

class YouthModeCloseInputPasswdController extends NormalPageControllerBase {

  var bCanSubmit = false.obs;

  late TextEditingController pwdEditController;
  String strPrePwd = "";
  int maxLen = 4;
  @override
  void onInit() {
    super.onInit();
    pwdEditController = TextEditingController();
    strPrePwd = Get.arguments?["pwd"] ?? "";
  }

  Future<void> codeCompleteCallBack(String strCode) async {
    // bCanSubmit.value = strCode == strPrePwd;
  }

  Future<void> onFeedBack() async {
    Get.to(() => YouthModeStartFeedbackPage(isGlobal: false,tag: ToolsUtil().genUuId(),));
  }

  Future<void> onValueChg(String strCode) async {
    if (strCode.length < maxLen) {
      bCanSubmit.value = false;
    } else {
      bCanSubmit.value = true;
    }
  }

  Future<void> onSubmit() async {

    Map<String,dynamic> mapResult = await ApiReqInterface.disableYouthMode(pwdEditController.text);
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    SingleRoutePageManage.routeMainPage(LanStrings.tabMy);
    ///开启青少年模式
    commonEventBus.fire(YouthModeStateEvent(bOpen: false));

  }




  @override
  void onClose() {
    super.onClose();
  }
}