import 'dart:async';

import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_appeal.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:get/get.dart';

class YouthModeStartFeedbackController extends NormalPageControllerBase {

  bool bAgreed = false;
  @override
  void onInit() {
    super.onInit();
  }

  Future<void> onToStartFeedback() async {
    Get.to(() => YouthModeAppealView());
  }


  @override
  void onClose() {
    super.onClose();
  }
}