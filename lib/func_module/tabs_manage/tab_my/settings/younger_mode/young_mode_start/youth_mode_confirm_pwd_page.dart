import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_confirm_pwd_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class YouthModeConfirmPwdPage extends NormalPageViewBase<YouthModeConfirmPwdController> {
  YouthModeConfirmPwdPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  YouthModeConfirmPwdPageState createState() => YouthModeConfirmPwdPageState();
}

class YouthModeConfirmPwdPageState extends NormalPageBaseViewState<YouthModeConfirmPwdController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "确认密码"
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Spacer(),
          verifyCodePin(length: 4, editController: controller.pwdEditController, callback:controller.codeCompleteCallBack,valueChanged: controller.onValueChg),
          _tipInfo(),
          const Spacer(),
          SizedBox(height: 4.h),
          Obx(() => CustomButtonView(text: "确认", borderRadius: 12.r, isEnabled: controller.bCanSubmit.value,onTap: controller.onSubmit)),
          SizedBox(height: BizValues.bottomMargin,)
        ],
      ),
    );
  }

  Widget _tipInfo() {
    return SizedBox(height: 36.h,child: Column(children: [
      Text("请再次确认密码",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100939eb3),),
    ],),);
  }
  Widget mainContentTip() {
    return SizedBox(
        height: 280.h,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          children: [
          ],
        ));
  }



  @override
  YouthModeConfirmPwdController createController() {
    return YouthModeConfirmPwdController();
  }

}