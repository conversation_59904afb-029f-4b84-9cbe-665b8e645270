import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_opened_main_controller.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_webview/util_webview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class YouthModeOpenedMainPage extends NormalPageViewBase<YouthModeOpenedMainController> {
  YouthModeOpenedMainPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  YouthModeOpenedMainPageState createState() => YouthModeOpenedMainPageState();
}

class YouthModeOpenedMainPageState extends NormalPageBaseViewState<YouthModeOpenedMainController>{
  @override
  PreferredSizeWidget appbar() {
    return customAppbar(bShowLeading: false, strTitleText: "青少年专区");
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 12.h),
          _modeLogo(),
          SizedBox(height: 22.h),
          _modeSwitch(),
          // SizedBox(height: 12.h),
          // mainContentTip(),
          const Spacer(),
          // _buildPrivacyAgreement(),
          // SizedBox(height: 4.h),
          CustomButtonView(text: "退出青少年模式",borderRadius: 12.r,onTap: controller.onToCloseYouthMode,),
          SizedBox(height: BizValues.bottomMargin)
        ],
      ),
    );
  }

  /// 抽离的隐私协议部分
  Widget _buildPrivacyAgreement() {
    return GetBuilder<YouthModeOpenedMainController>(
      id: "provState",
      tag: widget.tag,
      builder: (_) {
        return GestureDetector(
            onTap: () {
              controller.onRulePress();
            },
            child: SizedBox(
                width: 1.sw,
                height: 20.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(onTap: () {
                      controller.onRulePress();
                    },child: controller.bAgreed ? const Icon(Icons.check_circle,color: Colors.blue,size: 14,) : Icon(Icons.radio_button_unchecked,color: Colors.blue,size: 14,)),
                    SizedBox(width: 8.w,),
                    Text("同意", style: TextStyle(fontSize: 10.sp, color: BizColors.rgb10063666a)),
                    InkWell(
                        onTap: () {
                          WebViewUtil.jumpToCommonWebPage(context, ApiReqUrl.childInfoPro);
                        },
                        child: Text("《儿童个人信息保护规划》", style: TextStyle(fontSize: 10.sp, color: BizColors.rgb10063666a))),
                  ],
                )));
      },);
// 隐私协议的文本部分
  }

  Widget mainContentTip() {
    return SizedBox(
        height: 280.h,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          children: [
            textRule(BizStrings.youthRuleLineOne),
            SizedBox(height: 12.h),
            textRule(BizStrings.youthRuleLineSec),
            SizedBox(height: 12.h),
            textRule(BizStrings.youthRuleLineThd),
            SizedBox(height: 12.h),
            textRule(BizStrings.youthRuleLineFor),
            SizedBox(height: 12.h),
            textRule(BizStrings.youthRuleLineFiv),
          ],
        ));
  }

  Widget textRule(String text) {
     return ConstrainedBox(constraints: BoxConstraints(minHeight: 18.h,maxWidth: 1.sw),child: Text(text,style: TextStyle(fontSize: 12.sp,color: BizColors.rgb1000c1018),),);
  }

  Widget _modeLogo() {
    return SizedBox(
      height: 201.w,
      child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
        SizedBox(width: 201.w, height: 201.w, child: Image.asset(AssetsRes.iconYouthModeOpened, width: 201.w, height: 201.w, fit: BoxFit.fill))
      ]),
    );
  }

  Widget _modeSwitch() {
    return Text("暂无内容",
        style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3));
  }

  @override
  YouthModeOpenedMainController createController() {
    return YouthModeOpenedMainController();
  }

}