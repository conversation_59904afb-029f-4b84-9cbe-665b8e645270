import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_confirm_pwd_page.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:get/get.dart';

class YouthModeSetPwdController extends NormalPageControllerBase {

  bool bAgreed = false;
  late TextEditingController pwdEditController;
  var bCanNext = true.obs;
  int nMaxLen = 4;
  @override
  void onInit() {
    super.onInit();
    pwdEditController = TextEditingController();
  }

  Future<void> codeCompleteCallBack(String strCode) async {
    bCanNext.value = true;
  }

  Future<void>onValueChanged(String value) async {
    // if( value.length < nMaxLen ) {
    //   bCanNext.value = false;
    // }else {
    //   bCanNext.value = true;
    // }
  }

  Future<void> onNextStep() async {
    Get.to(() => YouthModeConfirmPwdPage(isGlobal: false, tag: ToolsUtil().genUuId()),arguments: {"pwd": pwdEditController.text});
  }


  @override
  void onClose() {
    super.onClose();
  }
}