import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_start_feedback_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class YouthModeStartFeedbackPage extends NormalPageViewBase<YouthModeStartFeedbackController> {
  YouthModeStartFeedbackPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  YouthModeStartFeedbackPageState createState() => YouthModeStartFeedbackPageState();
}

class YouthModeStartFeedbackPageState extends NormalPageBaseViewState<YouthModeStartFeedbackController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
      onLeadingPress: () {
        Get.back();
      },
      strTitleText: "忘记密码"
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 12.h),
          _resetTip(),
          SizedBox(height: 12.h),
          _modeSwitch(),
          SizedBox(height: 20.h),
          _resetPwdTip(),
          SizedBox(height: 12.h),
          mainContentTip(),
          const Spacer(),
          CustomButtonView(text: "申诉",borderRadius: 12.r,onTap: controller.onToStartFeedback,),
          SizedBox(height: ScreenUtil().bottomBarHeight,)
        ],
      ),
    );
  }

  Widget mainContentTip() {
    return SizedBox(
        height: 340.h,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          children: [
            textRule(BizStrings.youthResetPwdTipFir),
            SizedBox(height: 12.h),
            textRule(BizStrings.youthResetPwdTipSec),
            SizedBox(height: 12.h),
            textRule(BizStrings.youthResetPwdTipThd),
            SizedBox(height: 12.h),
            textRule(BizStrings.youthResetPwdTipFor),
          ],
        ));
  }

  Widget textRule(String text) {
     return ConstrainedBox(constraints: BoxConstraints(minHeight: 18.h,maxWidth: 1.sw),child: Text(text,style: TextStyle(fontSize: 12.sp,color: BizColors.rgb1000c1018),),);
  }

  Widget _resetTip() {
    return Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      Text("青少年模式重置密码流程",style: TextStyle(fontSize: 18.sp,color: BizColors.rgb1000c1018),),
    ]);
  }

  Widget _resetPwdTip() {
    return Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      Text("青少年模式密码重置",style: TextStyle(fontSize: 18.sp,color: BizColors.rgb1000c1018),),
    ]);
  }

  Widget _modeSwitch() {
    return Text("若您需要重置青少年模式的密码请您点击下方【申诉】并输入内容",
        style: TextStyle(fontSize: 12.sp, color: BizColors.rgb1000c1018));
  }

  @override
  YouthModeStartFeedbackController createController() {
    return YouthModeStartFeedbackController();
  }

}