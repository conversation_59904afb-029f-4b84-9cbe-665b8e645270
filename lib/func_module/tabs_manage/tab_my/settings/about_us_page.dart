import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_update/util_update.dart';
import 'package:flutter_common_base/util/util_webview/util_webview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AboutUsPage extends StatelessWidget with CommonWidgetPub {
  AboutUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: customAppbar(strTitleText: "关于我们",onLeadingPress: () {
        Get.back();
      }),
      body: bodyContentWidget(),
    );
  }

  Widget bodyContentWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 20.w),
            Center(
              child: ClipRRect(
                child: ImageLoadView(AssetsRes.iconAboutUsLogo, width: 120.w, height: 126.h, fit: BoxFit.fill),
              ),
            ),
            SizedBox(height: 16.w),
            Text(
              '${LanStrings.versionName.tr}  ${StorageUtil.get<String>(SPKey.keyVersion)}',
              style: TextStyle(color: BizColors.rgb100939eb3, fontSize: 14.sp, fontWeight: FontWeight.w400),
            ),
            SizedBox(height: 66.w),
            SettingCommon(
              title: "用户协议",
              onPressed: () {
                WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.userPolicy);
              },
            ),
            SettingCommon(
              title: "隐私政策",
              onPressed: () {
                WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.userPrivacy);
              },
            ),
            SettingCommon(
              title: "个人信息收集清单",
              onPressed: () {
                WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.userInfoCollect);
              },
            ),
            SettingCommon(
              title: "第三方信息共享清单",
              onPressed: () {
                WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.thirdInfoList);
              },
            ),

            SettingCommon(
              title: "儿童个人信息保护规则",
              onPressed: () {
                WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.childInfoPro);
              },
            ),

            SettingCommon(
              title: "青少年文明公约",
              onPressed: () {
                WebViewUtil.jumpToCommonWebPage(Get.context!, ApiReqUrl.youngRule);
              },
            ),


            SettingCommon(
              title: "检查更新",
              onPressed: () {
                UpDateUtil.getUpdateInfo(strSrc: CheckUpdateSrc.updateSetting);
              },
            ),
          ],
        ),
      ),
    );
  }
}
