import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/setting_page_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_file/file_util.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SettingPageView extends CommonBaseView<SettingPageController> {
  const SettingPageView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  SettingPageViewState createState() => SettingPageViewState();
}


class SettingPageViewState extends CommonBaseViewState<SettingPageController> with CommonWidgetPub {

  @override
  SettingPageController createController() {
    return SettingPageController();
  }

  @override
  Widget buildBody() {
    return KeepAliveWrapper(child: _mainContent());
  }

  Widget _mainContent() {
    return Material(
        child: SingleChildScrollView(
            child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      customAppbar(strTitleText: "设置", onLeadingPress: controller.onRetBack),
                      SizedBox(height: 16.h,),
                      _mainBlockContents(),
                    ],
                  )));
  }

  Widget _mainBlockContents() {
    return GetBuilder<SettingPageController>(
      id: "mainInfo",
      tag: widget.tag,
      builder: (_) {
        return Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(8.r))),
        child: ListView(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          children: [
            SizedBox(height: 20.h,),
            _buildCatBlock(_subItemsAccount()),
            SizedBox(height: 20.h,),
            _buildCatBlock(_subItemsClearCache()),
            SizedBox(height: 20.h,),
            _buildCatBlock(_subItemsAboutUs()),
            SizedBox(height: 20.h,),
            UserInfoUtil.isLogin() ? _buildCatBlock(_subItemsLogOut()) : const SizedBox.shrink(),
          ],
        ),
      );
    },);

  }

  ///各个分类的块
  Widget _buildCatBlock(List<Widget> subItem, {bool bBlockShow = true, String catTitle = ""}) {
    if (!bBlockShow) {
      return Container();
    }

    Widget listSubParent = Container(
        decoration: BoxDecoration(color: BizColors.rgb100ffffff, borderRadius: BorderRadius.all(Radius.circular(8.r))),
        child: Column(
          children: subItem,
        ));

    return Column(
      children: [
        listSubParent,
      ],
    );
  }


  ///分类
  List<Widget> _subItemsClearCache() {
    List<Widget> listOthers = [
      GetBuilder<SettingPageController>(
        id: "cacheInfo",
        tag: widget.tag,
        builder: (_) {
          return SettingCommon(
            iconImg: AssetsRes.iconItemClearCache,
            paddingLeft: 16,
            paddingRight: 16,
            title: "清理缓存",
            content: FileUtil.getInstance().renderSize(controller.nCacheSize),
            arrowMarginRight: 8,
            onPressed: controller.onClearCache,
            navWidget: Container(),
          );
        },
      ),
    ];
    return listOthers;
  }

  ///分类
  List<Widget> _subItemsAccount() {
    List<Widget> listOthers = [
      UserInfoUtil.isLogin()
          ? SettingCommon(
              iconImg: AssetsRes.iconItemAccount,
              title: "账号与安全",
              onPressed: controller.accountSafety,
            )
          : const SizedBox.shrink(),

      ///钱包
      UserInfoUtil.isLogin()
          ? SettingCommon(
              iconImg: AssetsRes.iconWallet,
              title: "钱包",
              onPressed: controller.walletPage
            )
          : const SizedBox.shrink(),

      SettingCommon(
        iconImg: AssetsRes.iconItemYoung,
        title: "青少年模式",
        onPressed: controller.toYoungMode,
      ),

      // SettingCommon(
      //   iconImg: AssetsRes.iconItemAccount,
      //   title: "字体大小",
      //   onPressed: controller.toFontSizePage,
      // ),
      // SettingCommon(
      //   iconImg: AssetsRes.iconItemSysMode,
      //   title: "夜间模式跟随系统",
      //   navWidget: _playModeSwitch(),
      // ),
      // SettingCommon(
      //   iconImg: AssetsRes.iconItemTinyWindow,
      //   title: "退出应用后开启小窗播放",
      //   navWidget: _sysModeSwitch(),
      // ),
    ];
    return listOthers;
  }

  ///分类
  List<Widget> _subItemsAboutUs() {
    List<Widget> listOthers = [
      SettingCommon(
          iconImg: AssetsRes.iconItemContactUs,
          title: "联系我们",
          content: BizStrings.myMailService,
          onPressed: controller.toContactUs
      ),

      SettingCommon(
        iconImg: AssetsRes.iconItemThirdInfo,
        title: "意见反馈",
        onPressed: controller.toFeedBack,
      ),
      SettingCommon(
        iconImg: AssetsRes.iconItemAboutUs,
        title: "关于我们",
        onPressed: controller.onAboutUs,
      ),

    ];
    return listOthers;
  }

  ///分类 退出登录
  List<Widget> _subItemsLogOut() {
    List<Widget> listOthers = [
      SettingCommon(
        iconImg: AssetsRes.iconItemLogout,
        title: "退出登录",
        onPressed: controller.onLogOut,
      ),
    ];
    return listOthers;
  }

  Widget _sysModeSwitch() {
    return GetBuilder<SettingPageController>(
      tag: widget.tag,
      id: "switchState",
      builder: (_) {
        return InkWell(onTap:controller.onSysThemeSwitch,child: switchIcon(controller.bSysThemeMod));
      },
    );
  }

  Widget _playModeSwitch() {
    return GetBuilder<SettingPageController>(
      tag: widget.tag,
      id: "switchState",
      builder: (_) {
        return InkWell(
            onTap: controller.onPlayModeSwitch,
            child: switchIcon(controller.bPlayMod));
      },
    );
  }
}