import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/cat_content/cat_like/cat_like_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/get_state_manager.dart';

class CatLikeView extends CommonBaseView<CatLikeController> {
  const CatLikeView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  CatHistoryViewState createState() => CatHistoryViewState();
}


class CatHistoryViewState extends CommonBaseViewState<CatLikeController> with CommonWidgetPub {

  @override
  CatLikeController createController() {
    return CatLikeController();
  }

  @override
  Widget buildBody() {
    return _mainContent();
  }

  Widget _mainContent() {
    return GetBuilder<CatLikeController>(
        id: "mainList",
        tag: widget.tag,
        builder: (_) {
          return ListView.builder(
            itemBuilder: actorItem,
            itemCount: controller.listActors.length,
            padding: EdgeInsets.zero,
          );
        });
  }

  Widget actorItem(BuildContext context, int index) {
    ActorsInfo actorsInfo = controller.listActors[index];
    return Container(
      width: 1.sw,
      height: 112.h,
      padding: EdgeInsets.only(top: 16.h,),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ImageLoadView(actorsInfo.businessData.strHeadImg, width: 48.w, height: 48.w, shape: BoxShape.circle),
              SizedBox(width: 12.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    actorsInfo.businessData.strName,
                    style: TextStyle(fontSize: 14.sp, color: BizColors.rgb1000c1018),
                  ),
                  SizedBox(height: 8.h),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 240.w),
                    child: Text(
                      actorsInfo.businessData.strDesc,
                      style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3),
                    ),
                  ),
                ],
              ),
            ],
          ),
          ///分隔线
          Container(width: 1.sw,height: 0.5.h,color: Color(0xffd1d8e1),)
        ],
      ),
    );
  }

  @override
  Widget noDataView() {
    return const ClassicalNoDataView(emptyTip: "您尚未邂逅星光，星雪短剧期待与您同框",);
  }
}