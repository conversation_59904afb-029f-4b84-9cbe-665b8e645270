import 'dart:async';

import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';

class CatLikeController extends CommonBaseController {
  ViewLoaderState historyDataLoadState = ViewLoaderState();

  int nTotal = 0;
  int nPageIndex = 1;
  int nPagNum = 0;
  List<ActorsInfo> listActors = [];
  StreamSubscription? eventLogin;

  @override
  void onInit() {
    super.onInit();
    eventLogin = commonEventBus.on<LoginStatusEvent>().listen(loginStatusChange);
    getLikeActors();
  }

  Future<void> loginStatusChange(LoginStatusEvent event) async {
    if (event.loginStatus != LogStatus.loginStatusSucceed) {
      dataLoadState.loadState = LoaderState.stateNoData;
      listActors = [];
      update();
      return;
    }
    getLikeActors();
  }

  Future<void> getLikeActors() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.myPageLikeOrActors(4, 2);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      dataLoadState.loadState = LoaderState.stateNoData;
      update();
      return;
    }

    List items = mapResult['data'] ?? [];
    for (var item in items) {
      ActorsInfo info = ActorsInfo.fromJson(item);
      listActors.add(info);
    }

    if (listActors.isEmpty) {
      dataLoadState.loadState = LoaderState.stateNoData;
    } else {
      dataLoadState.loadState = LoaderState.stateSucceed;
    }

    update();
  }
}
