import 'dart:async';

import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';

class CatHistoryController extends CommonBaseController {
  int nCurIndex = 0;
  List<VideoData> historyInfo = [];

  StreamSubscription? eventLogin;
  StreamSubscription? eventTabIndex;

  @override
  void onInit() {
    super.onInit();
    eventLogin = commonEventBus.on<LoginStatusEvent>().listen(loginStatusChange);
    eventTabIndex = commonEventBus.on<MyHisOrColTabChgEvent>().listen(tabIndexChg);
    getHistoryData();
  }

  Future<void> loginStatusChange(LoginStatusEvent event) async {
    getHistoryData();
  }

  Future<void> tabIndexChg(MyHisOrColTabChgEvent event) async {
    if (event.nIndex != 0) {
      return;
    }

    getHistoryData();
  }

  Future<void> getHistoryData() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getHistories();
    if (mapResult ["code"] != ResponseCodeParse.codeSuccess) {
      dataLoadState.loadState = LoaderState.stateNoData;
      update();
      return;
    }

    historyInfo = [];
    var listItems = mapResult["data"] ?? [];
    for(var item in listItems) {
      historyInfo.add(VideoData.fromJson(item));
    }
    if (historyInfo.isEmpty) {
      dataLoadState.loadState = LoaderState.stateNoData;
    } else {
      dataLoadState.loadState = LoaderState.stateSucceed;
    }
    update();
  }
}