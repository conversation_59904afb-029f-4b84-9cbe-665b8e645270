import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/cat_content/cat_history/cat_history_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/sliver_grid_view_fixed_size.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CatHistoryView extends CommonBaseView<CatHistoryController> {
  const CatHistoryView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  CatHistoryViewState createState() => CatHistoryViewState();
}


class CatHistoryViewState extends CommonBaseViewState<CatHistoryController> with CommonWidgetPub {

  @override
  CatHistoryController createController() {
    return CatHistoryController();
  }

  @override
  Widget buildBody() {
    return _mainContent();
  }

  Widget _mainContent() {
    return GetBuilder<CatHistoryController>(
      tag: widget.tag,
      builder: (_) {
        return GridView.builder(
            padding: EdgeInsets.zero,
            physics: const BouncingScrollPhysics(),
            itemCount: controller.historyInfo.length,
            gridDelegate: SliverGridDelegateWithFixedSize(BizValues.itemBlockWidth.w, BizValues.itemBlockHeight.h,
                mainAxisSpacing: BizValues.itemMainCross.w),
            itemBuilder: (BuildContext context, index) {
              VideoData infoItem = controller.historyInfo[index];
              return gridItem(infoItem);
            });
      },
    );
  }

  @override
  Widget noDataView() {
    return const ClassicalNoDataView(emptyTip: "您尚未留下足迹，星雪短剧静候与您相遇",);
  }

}