import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/cat_content/cat_collect/cat_collect_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_view.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/sliver_grid_view_fixed_size.dart';
import 'package:flutter_common_base/util/util_common_widget/smart_refresher_widget.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CatCollectView extends GetView<CatCollectController> with CommonWidgetPub {
  const CatCollectView({super.key});



  @override
  Widget build(BuildContext context) {
    return _mainContent();
  }
  Widget _mainContent() {
    Get.put(CatCollectController());
    return GetBuilder<CatCollectController>(
      builder: (_) {
        return LoaderContainer(
          contentBuilder: () => SmartRefresherWidget(
            _mainListItems(), // 注意这里传的是可滚动内容
            controller.refreshCon,
            onPullUpLoading: controller.onPullUpLoadMore,
            onPullDownRefresh: null,
          ),
          loaderState: controller.historyDataLoadState,
        );


      },
    );
  }

  Widget _mainListItems() {
    return GridView.builder(
        physics: const ClampingScrollPhysics(),
        itemCount: controller.listVideoInfo.length,
        gridDelegate: SliverGridDelegateWithFixedSize(BizValues.itemBlockWidth.w, BizValues.itemBlockHeight.h, mainAxisSpacing: BizValues.itemMainCross.w),
        itemBuilder: (BuildContext context, index) {
          VideoData dataInfo = controller.listVideoInfo[index];
          return gridItem(dataInfo, bCollect: true);
        });
  }

  @override
  Widget noDataView() {
    return const ClassicalNoDataView(emptyTip: "您尚未收藏心动，星雪短剧邀您续写新梦",strDefImg: AssetsRes.defNoCollectIcon,);
  }

}