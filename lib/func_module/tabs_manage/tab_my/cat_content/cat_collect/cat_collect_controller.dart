import 'dart:async';

import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class CatCollectController extends GetxController {
  ViewLoaderState historyDataLoadState = ViewLoaderState();

  int nTotal = 0;
  int nCurPageIndex = 1;
  int nPagNum = 0;
  List<VideoData> listVideoInfo = [];
  late RefreshController refreshCon;
  StreamSubscription? eventTabIndex;
  ///等登录通知的
  StreamSubscription? eventLogin;

  @override
  void onInit() {
    super.onInit();
    refreshCon = RefreshController(initialLoadStatus: LoadStatus.idle);
    eventTabIndex = commonEventBus.on<MyHisOrColTabChgEvent>().listen(tabIndexChg);
    eventLogin = commonEventBus.on<LoginStatusEvent>().listen(loginStatusChange);
    if( !UserInfoUtil.isLogin()) {
      historyDataLoadState.loadState = LoaderState.stateNoData;
      update();
      return;
    }
    getFollowVideos();
  }

  Future<void> loginStatusChange(LoginStatusEvent event) async {
    if (event.loginStatus != LogStatus.loginStatusSucceed) {
      listVideoInfo.clear();
      historyDataLoadState.loadState = LoaderState.stateNoData;
      update();
      return;
    }
    nCurPageIndex = 1;
    getFollowVideos();
  }

  Future<void> tabIndexChg(MyHisOrColTabChgEvent event) async {
    if (event.nIndex != 1) {
      return;
    }
    if (!UserInfoUtil.isLogin()) {
      listVideoInfo.clear();
      historyDataLoadState.loadState = LoaderState.stateNoData;
      update();
      return;
    }

    if (event.strSourceId.isNotEmpty) {
      int nIndexFind = listVideoInfo.indexWhere((element) => element.strSourceId == event.strSourceId);
      if (nIndexFind >= 0) {
        return;
      }
    }

    nCurPageIndex = 1;
    getFollowVideos();
  }

  Future<void> getFollowVideos({int nAction = ListRefreshAct.refreshTypeInit,int nPageIndex = 1,int nCntPerPage = BizValues.maxSizePerPage}) async {
    if (UserInfoUtil.isLogin()) {
      Map<String, dynamic> mapResult = await ApiReqInterface.myPageLikeOrActors(2, 1, nPageIndex: nPageIndex);
      if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
        _listStatusErrorUpdate(nListDataType: nAction);
        update();
        return;
      }

      List<VideoData> listVideoInfoTemp = [];
      List items = mapResult['data'] ?? [];
      for (var item in items) {
        Map<String,dynamic> mapData= item["businessData"] ?? {};
        VideoData info = VideoData.fromJson(mapData);
        if (info.strSourceId.isEmpty) {
          info.strSourceId = '${item["businessId"] ?? ""}';
        }
        listVideoInfoTemp.add(info);
      }
      _listStatusSuccessUpdate(nListDataType: nAction, result: listVideoInfoTemp);
      nCurPageIndex = nCurPageIndex + 1;
      update();
    }
  }

  ///上下拉加载更多
  @override
  Future<void> onPullUpLoadMore() async {
    getFollowVideos(nAction: ListRefreshAct.refreshTypePullUpMore, nPageIndex: nCurPageIndex);
  }
  ///下拉刷新
  @override
  Future<void> onPullDownReload() async {
    nCurPageIndex = 1;
    getFollowVideos(nAction: ListRefreshAct.refreshTypePullDown, nPageIndex: nCurPageIndex);
  }

  ///加载失败时的处理
  void _listStatusErrorUpdate({int nListDataType = ListRefreshAct.refreshTypeInit}) {
    if (nListDataType == ListRefreshAct.refreshTypeInit) {
      historyDataLoadState.loadState = LoaderState.stateNoData;
    } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
      refreshCon.refreshFailed();
    } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
      refreshCon.loadNoData();
    }
  }

  void _listStatusSuccessUpdate({int nListDataType = ListRefreshAct.refreshTypeInit,List<VideoData> result = const [],int nPagesPerPage = BizValues.maxSizePerPage}) {
    if (result.isEmpty) {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        historyDataLoadState.loadState = LoaderState.stateNoData;
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        refreshCon.refreshFailed();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        refreshCon.loadNoData();
      }
    } else {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        listVideoInfo = [];
        listVideoInfo.addAll(result);
        historyDataLoadState.loadState = LoaderState.stateSucceed;
        refreshCon.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        listVideoInfo = [];
        listVideoInfo.addAll(result);
        refreshCon.refreshCompleted();
        refreshCon.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        listVideoInfo.addAll(result);
        // if (result.length < nPagesPerPage) {
        //   refreshController.loadNoData();
        // } else {
        refreshCon.loadComplete();
        // }
      }
    }

  }
}
