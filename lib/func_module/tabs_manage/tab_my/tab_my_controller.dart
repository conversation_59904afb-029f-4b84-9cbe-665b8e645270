import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/cat_content/cat_collect/cat_collect_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/cat_content/cat_history/cat_history_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/cat_content/cat_like/cat_like_view.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/keepPageStateWrapper.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:get/get.dart';

class TabMyController extends GetxController with GetTickerProviderStateMixin {
  int nCurrentIndex = 0;
  late TabController catTabsController;
  List<TabIndexData> listTab = [];
  List<Widget> listCatDetailView = [];

  ///用户信息
  UserInfoDetail userInfoDetail = UserInfoDetail();

  ///整体的滚动控制
  late ScrollController mainScrollController;

  ///等登录通知的
  StreamSubscription? eventLogin;

  @override
  void onInit() {
    super.onInit();
    eventLogin = commonEventBus.on<LoginStatusEvent>().listen(loginStatusChange);

    ///初始化标签
    listTab = [
      TabIndexData(
          strName: "历史",
          strIcon: AssetsRes.iconSubTabHistoryUnSel,
          strIconActive: AssetsRes.iconSubTabHistorySel,
          nIndex: 0),
      // TabIndexData(
      //     strName: "关注",
      //     strIcon: AssetsRes.iconSubTabLikeUnSel,
      //     strIconActive: AssetsRes.iconSubTabLikeSel,
      //     nIndex: 1),
      TabIndexData(
          strName: "收藏",
          strIcon: AssetsRes.iconSubTabCollectUnSel,
          strIconActive: AssetsRes.iconSubTabCollectSel,
          nIndex: 1),
    ];

    ///初始化控制器
    catTabsController = TabController(length: listTab.length, vsync: this, initialIndex: nCurrentIndex);
    mainScrollController = ScrollController();

    listCatDetailView = [
      KeepAliveWrapper(child: CatHistoryView(isGlobal: false, tag: ToolsUtil().genUuId())),
      // KeepAliveWrapper(child: CatLikeView(isGlobal: false, tag: ToolsUtil().genUuId())),
      const KeepAliveWrapper(child: CatCollectView()),
    ];

    userInfoDetail = UserInfoUtil.getUserInfo();
    update(["loginState","infoDetail"]);
  }

  Future<void> toLogin() async {
    SingleRoutePageManage.routeToLoginMain();
  }

  Future<void> loginStatusChange(LoginStatusEvent event) async {
    if (event.loginStatus != LogStatus.loginStatusSucceed) {
      update(["loginState"]);
      return;
    }
    userInfoDetail = UserInfoUtil.getUserInfo();
    update(["loginState", "infoDetail"]);
  }

  void onIndexChanged(int nIndex) {
    nCurrentIndex = nIndex;
    catTabsController.index = nCurrentIndex;
    commonEventBus.fire(MyHisOrColTabChgEvent(nIndex: nIndex));
    update(["togIndex"]);
  }

  String calcStrAreaInfoDetail() {
    String strInfo = "";
    List<AreaInfoUnion> listArea = [];
    listArea.addAll(userInfoDetail.listAreaInfo);
    listArea.sort((a, b) => a.nLevel.compareTo(b.nLevel));
    for (int nIndex = 0; nIndex < listArea.length; nIndex++) {
      strInfo = strInfo + listArea[nIndex].strName;
      if ((nIndex + 1) != listArea.length) {
        strInfo = '$strInfo·';
      }
    }
    return strInfo;
  }

  ///获取用户详细信息
  Future<void> getUserInfo() async {
    Map<String, dynamic> mapResult = await UserInfoUtil.getUserInfoDetail();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    userInfoDetail = mapResult["userInfo"] ?? UserInfoDetail();
    update(["infoDetail"]);
  }

  Future<void> toEditInfoPage() async {
    SingleRoutePageManage.routeToEditProfilePage(arguments: {"userInfo": userInfoDetail});
  }

  Future<void> toSettingPage() async {
    SingleRoutePageManage.routeToSettingPage();
  }

  Future<void> toTheme() async {
  }

  @override
  void onClose() {
    catTabsController.dispose();
    mainScrollController.dispose();
    super.dispose();
  }
}
