import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_recommend/tab_commend_controller.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TabRecommendPage extends GetView<TabRecommendController> {
  const TabRecommendPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: BizColors.rgb1000c1018,
      body: Stack(
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              // 获取 body 的大小
              var bodyWidth = constraints.maxWidth;
              var bodyHeight = constraints.maxHeight;
              return TheaterWidget(
                controller: controller.proVideoController,
                width: bodyWidth,
                height: bodyHeight,
                channelType: controller.nChannelType,
                hideTopInfo: true,
                setTopOffset: controller.nTopOffset,
                hideBottomInfo: true,
                setBottomOffset: 4,
                hideEnter: true,
                hideLikeButton: true,
                hideFavorButton: true,
                showChangeBtn: false,
                videoPlayListener: VideoPlayListener(),
                // selfDetailListener: SelfDetailListener(onOpenSelfDetail: controller.onOpenSelfDetail),
                theaterListener: TheaterListener(
                  onOpenDetail: controller.onOpenDetail,
                ),
              );
            },
          ),
          Positioned(
              top: BizValues.statusHeight.h, right: 12.w, child: _searchBtn()),
          // Positioned(bottom: 90, right: 16, child: _favBtn()),
        ],
      ),
    );
  }

  Widget _favBtn() {
    return InkWell(
        onTap: () {
          if (controller.bFav) {
            // setFavStateUnFav();
          } else {
            // setFavStateFav();
          }
        },
        child: SizedBox(
          width: 36.w,
          height: 36.w,
          child: Image.asset(
              controller.bFav ? AssetsRes.iconFav : AssetsRes.iconUnFav,
              width: 36.w,
              height: 36.w),
        ));
  }

  Widget _searchBtn() {
    return InkWell(
        onTap: () {
          SingleRoutePageManage.routeToSearchMainPage();
        },
        child: ColoredBox(
          color: Colors.transparent,
          child: SizedBox(
              width: 1.sw,
              height: 44.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ColoredBox(
                    color: Colors.transparent,
                    child: Opacity(
                      opacity: 0.7,
                      child: Image.asset(AssetsRes.iconSearchWhite,
                          width: 22.w, height: 22.w, fit: BoxFit.fill),
                    ),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                ],
              )),
        ));
  }
}
