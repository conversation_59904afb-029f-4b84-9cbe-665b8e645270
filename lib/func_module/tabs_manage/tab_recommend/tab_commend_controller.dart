import 'dart:async';

import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/forground_service/forground_servcie.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:flutter_common_base/util/util_routes/route_manager.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/router_report.dart';

class TabRecommendController extends FullLifeCycleController
    with FullLifeCycleMixin {
  late ProVideoController proVideoController;
  int currentIndex = 1;

  /// 当前已播放的集数
  int newIndex = 1;

  /// 当前最新的集数
  int nChannelType = 1;

  ///播放推荐类型
  int nTopOffset = 44;
  StreamSubscription? eventResponse;
  StreamSubscription? eventTabChange;
  String strTabName = "";
  bool bFav = false;
  @override
  void onInit() {
    super.onInit();
    eventResponse = commonEventBus.on<RouteChangeEvent>().listen(routeEvent);
    eventTabChange = commonEventBus.on<MainTabChange>().listen(tabEvent);
    proVideoController = ProVideoController();

    ///不让熄屏
    ForGroundServiceUtil.wakeLockOn();
  }

  Future<void> queryDramaFavState(int nSourceId) async {
    Map<String, dynamic> mapResult =
        await ApiReqInterface.queryDataState(2, nSourceId);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    List fav = mapResult["data"] ?? [];
    if (fav.isEmpty) {
      bFav = false;
    } else {
      bFav = true;
    }
  }

  Future<void> setFavStateFav(int nSourceId) async {
    Map<String, dynamic> mapResult =
        await ApiReqInterface.actionExeCute(2, 1, nSourceId);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    bFav = true;
  }

  Future<void> routeEvent(RouteChangeEvent event) async {
    if (RouteUtil.routeTrace.length > 1) {
      await proVideoController.pause();
      if (RouteUtil.routeTrace.contains(BizStrings.dramaDetailPageRoute)) {
        ForGroundServiceUtil.wakeLockOn();
      } else {
        ForGroundServiceUtil.wakeLockOff();
      }
    } else {
      if (strTabName == LanStrings.tabRecommend) {
        proVideoController.resume();
        ForGroundServiceUtil.wakeLockOn();
      } else {
        await proVideoController.pause();

        ForGroundServiceUtil.wakeLockOff();
      }
    }
  }

  Future<void> tabEvent(MainTabChange event) async {
    strTabName = event.strName;
    if (event.strName != LanStrings.tabRecommend) {
      await proVideoController.pause();
      ForGroundServiceUtil.wakeLockOff();
    } else {
      proVideoController.resume();
      ForGroundServiceUtil.wakeLockOn();
    }
  }

  Future<void> onOpenDetail(Drama drama) async {
    /// 暂停
    // proVideoController.pause();
    /// 打开短剧详情
    await SingleRoutePageManage.routeDramaDetailPage(drama);
    proVideoController.resume();
  }

  ///展示的时候不隐藏
  void _onShowStateNoPause() {
    if (RouteUtil.routeTrace.length > 1) {
      proVideoController.pause();
    } else {
      if (strTabName != LanStrings.tabRecommend) {
        proVideoController.pause();
      }
    }
  }

  Future<void> onOpenSelfDetail(Drama drama) async {
    // proVideoController.pause();
    /// 暂停
    await SingleRoutePageManage.routeToSelfDefineDramaDetail(drama);
    proVideoController.resume();
  }

  @override
  void onDetached() {
    // proVideoController.pause();
  }

  @override
  void onHidden() {
    _onShowStateNoPause();
  }

  @override
  void onInactive() {
    _onShowStateNoPause();
  }

  @override
  void onPaused() {
    _onShowStateNoPause();
  }

  @override
  void onResumed() {
    if (strTabName != LanStrings.tabRecommend ||
        (RouteUtil.routeTrace.length > 1)) {
      proVideoController.pause();
      if (RouteUtil.routeTrace.contains(BizStrings.dramaDetailPageRoute)) {
        ForGroundServiceUtil.wakeLockOn();
      }
      return;
    }
    proVideoController.resume();

    ///不让熄屏
    ForGroundServiceUtil.wakeLockOn();
  }
}
