import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/tab_fu_li.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/tab_fuli_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/placeholder_page.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TabFuLiPage extends GetView<TabFuLiController> with CommonWidgetPub {
  const TabFuLiPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(top: false, child: mainBody());
  }

  /// 子类需要实现这个方法来构建 UI
  Widget mainBody() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        _bg(),
        mainContent(),
        Obx(() => Visibility(visible: controller.bShowLoadingPage.value, child: const PlaceholderPage())),
      ],
    );
  }

  Widget mainContent() {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: false,
      appBar: appbar(),
      body: bodyContentWidget(),
    );
  }

  Widget bodyContentWidget() {
    return Column(
      children: [
        ///账单和余额
        _moneyRest(),
        ///日常福利
        _dailyFuLi(),
        GetBuilder<TabFuLiController>(
          id: "adList",
          builder: (TabFuLiController controller) {
            return Expanded(child: _adLists());
          },
        ),
        // ///日常任务
        // _dailyTask(),
        // ///每日广告任务
        // _dailyTaskAd(),
      ],
    );
  }

  Widget _adLists() {
    return ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: controller.listAds.length,
        itemBuilder: (BuildContext context, int index) {
          AdBannerData itemData = controller.listAds[index];
          return _adItem(itemData);
        });
  }

  ///每日任务
  Widget _adItem(AdBannerData itemInfo) {
    bool bCanClick = itemInfo.nClickAlready < itemInfo.nClickLimit;
    return Container(
      width: 1.sw,
      height: 80.h,
      // color: Colors.red,
      padding: EdgeInsets.only(left: 12.w, right: 12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 35.h,),
          SizedBox(
            height: 40.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.network(itemInfo.strIcon,width: 32.w,height: 32.w,fit: BoxFit.fill,),
                SizedBox(width: 12.w,),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(children: [
                      Text(itemInfo.strTitle, style: TextStyle(fontSize: 18.sp, color: BizColors.rgb1000c1018),),
                      SizedBox(width: 4.w,),
                      Text('(${itemInfo.nClickAlready}/${itemInfo.nClickLimit})', style: TextStyle(fontSize: 16.sp, color: BizColors.rgb100939eb3),),

                    ],),
                    Text(itemInfo.strDescription, style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3),),
                  ],
                ),
                const Spacer(),
                InkWell(
                  onTap: () {
                    controller.onDailyTask.call(itemInfo);
                  },
                  child: bCanClick ? _unFinishedBtnState() : _finishedBtnState(),
                ),
              ],
            ),
          ),
          SizedBox(height: 5.h,),
        ],
      ),
    );
  }

  ///余额和账单
  Widget _moneyRest() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: SizedBox(width: 1.sw,height: 98.h,child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _leftMoneyRest(),
          _payBill(),
        ],
      ),),
    );
  }

  ///日常福利
  Widget _dailyFuLi() {
    return Container(
      width: 1.sw,
      height: 70.h,
      decoration: const BoxDecoration(color:Colors.transparent,image: DecorationImage(image: AssetImage(AssetsRes.imgDailyFuLiBg), fit: BoxFit.fill)),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 16.w,top: 16.h),
            child: SizedBox(height: 70.h,child: Column(crossAxisAlignment: CrossAxisAlignment.start,children: [
              Text("每日福利",style: TextStyle(fontSize: 20.sp,fontFamily: BizStrings.aLiMaMaBold,color: BizColors.rgb1000c1018),),
              SizedBox(height: 4.h,),
              Text("看广告，赚现金，无门槛，秒提现！",style: TextStyle(fontSize: 16.sp,color: BizColors.rgb10063666a),)
            ],),),
          ),
          const Spacer(),
          Image.asset(AssetsRes.imgFuLiRedBag,width: 100.w,height: 70.h,fit: BoxFit.fill,),
        ],
      ),
    );
  }

  Widget _unFinishedBtnState() {
    return Container(
      width: 82.w,
      height: 32.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(8.r)), border: Border.all(width: 1, color: Color(0xffff6075))),
      child: Text("去完成",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100ff4141),),
    );
  }
  Widget _finishedBtnState() {
    return Container(
      width: 82.w,
      height: 27.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(8.r)),color: BizColors.rgb100ff4141),
      child: Text("已完成",style: TextStyle(fontSize: 12.sp,color: BizColors.rgb100ffffff),),
    );
  }

  Widget _lineText() {
    return ShaderMask(
      shaderCallback: (bounds) => const LinearGradient(
        colors: [ BizColors.rgb50F47CFF, BizColors.rgb0F585FF],
      ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
      blendMode: BlendMode.srcIn,
      child: Text(
        '渐变文字',
        style: TextStyle(
          fontSize: 40,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  ///余额
  Widget _leftMoneyRest() {
    return InkWell(
      onTap: controller.onBalanceDetail,
      child: SizedBox(
        height: 98.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///间隔
            SizedBox(height: 12.h,),
            ///余额
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 14.w, height: 14.w,child: Image.asset(AssetsRes.imgFuLiRestMoney,width: 14.w,height: 14.w,fit: BoxFit.fill,),),
                SizedBox(width: 4.w),
                Text("余额(元)", textAlign: TextAlign.center,style: TextStyle(height:1.1,fontSize: 14.sp, color: BizColors.rgb100ffffff),),
                SizedBox(width: 8.w),
                Obx(
                  () => !controller.isLogin.value
                      ? Text("登录后提现", textAlign: TextAlign.center, style: TextStyle(height: 1.1, fontSize: 14.sp, color: BizColors.rgb50ffffff),)
                      : const SizedBox.shrink(),
                ),
              ],
            ),
            SizedBox(height: 4.h,),
            ///余额的具体数值
            Obx(() => controller.isLogin.value ? _loginRestMoney() : _unLoginRestMoney()),
          ],
        ),
      ),
    );
  }

  ///登录时显示的状态（真实余额）
  Widget _loginRestMoney() {
    return InkWell(
      onTap: controller.onBalanceDetail,
      child: SizedBox(
        height: 46.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 18.w,),
            Text(
                "${controller.isLogin.value ? controller.dWallet.value : "登录查看"}",
                style: TextStyle(
                    fontSize: controller.isLogin.value ? 32.sp : 24.sp,
                    color: BizColors.rgb100ffffff,
                    fontWeight: controller.isLogin.value ? FontWeight.w400 : FontWeight.w700)),
            SizedBox(width: 4.w),
            Image.asset(AssetsRes.navRightIconWhite, width: 22.w, height: 22.w),
          ],
        ),
      ),
    );
  }

  ///未登录时显示的状态（登录提示）
  Widget _unLoginRestMoney() {
    return Container(
      width: 83.w,
      height: 32.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(16.r)),color: BizColors.rgb20ffffff,border: Border.all(width: 0.5,color: BizColors.rgb20ffffff)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text("一键登录",style: TextStyle(height:1.06,fontSize: 12.sp,color: BizColors.rgb100ffffff),),
          SizedBox(width: 8.w,),
          Image.asset(AssetsRes.navRightIconWhiteSmall, width: 3.w, height: 6.h),
        ],
      ),
    );
  }

  ///账单
Widget _payBill() {
    return InkWell(
      onTap: controller.onBillList,
      child: SizedBox(height: 98.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          ///间隔
          SizedBox(height: 12.h,),
          Row(
            children: [
              SizedBox(width: 14.w, height: 14.w,child: Image.asset(AssetsRes.imgFuLiZhangDan,width: 14.w,height: 14.w,fit: BoxFit.fill,),),
              SizedBox(width: 4.w,),
              Text("账单",style: TextStyle(fontSize: 14.sp,color: BizColors.rgb100ffffff),),
              SizedBox(width: 4.w,),
              Icon(Icons.navigate_next_rounded,size: 24.w,color: Colors.white,),
            ],
          ),
        ],
      ),),
    );
}




  PreferredSizeWidget appbar() {
    return customAppbar(
        strTitleText: "福利中心",
        bShowLeading: false,
      titleTextStyle: TextStyle(fontSize: 18.sp, color: BizColors.rgb100ffffff, fontFamily: "ALiMaMa_Bold"),
      onLeadingPress: () {

        },
        /*actions: [saveBtn()]*/);
  }

  Widget _bg() {
    return Container(
      width: 1.sw,
      height: 336.h,
      decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.tabFuLiBg),fit: BoxFit.fill),),
    );
  }
}
