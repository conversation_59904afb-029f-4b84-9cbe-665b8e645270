import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/with_draw/with_draw_type.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class WithDrawTypeSel extends StatefulWidget {
  final List<WithDrawTypeInfo> listType;
  final int nSelTypeIndex;

  const WithDrawTypeSel({super.key, this.nSelTypeIndex = 0, this.listType = const []});

  @override
  WithDrawTypeSelState createState() => WithDrawTypeSelState();
}

class WithDrawTypeSelState extends State<WithDrawTypeSel> {

  int nSel = 0;
  List<WithDrawTypeInfo> listTypeSel = [];
  @override
  void initState() {
    super.initState();
    nSel = widget.nSelTypeIndex;
    listTypeSel.add(widget.listType[nSel]);
  }

  @override
  Widget build(BuildContext context) {
   return Column(
     mainAxisAlignment: MainAxisAlignment.end,
     children: [
       Container(
         padding: EdgeInsets.only(left: 12.w,right: 12.w,top: 16.w),
          decoration: BoxDecoration(
            color: BizColors.rgb100ffffff,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r))),
          width: 1.sw,
          height: 309.h,
         child: Column(
           crossAxisAlignment: CrossAxisAlignment.start,
           children: [
             Text("选择-收款方式",style: TextStyle(fontSize: 18.sp,color: BizColors.rgb1000c1018,fontWeight: FontWeight.w600),),
             SizedBox(height: 16.h,),
             ///列表
             Expanded(child: _expandListType()),
           ],
         ),
        ),
      ],
   );
  }

  Widget _expandListType() {
    return ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: widget.listType.length,
        itemBuilder: (context, int index) {
          return _itemType(widget.listType[index], index);
        });
  }

  Widget _itemType(WithDrawTypeInfo itemInfo, int nIndex) {
    bool bSel = listTypeSel.contains(itemInfo);
    return InkWell(
      onTap: () {
        if (nIndex == nSel) {
          Get.back();
          return;
        }
        nSel = nIndex;
        listTypeSel.clear();
        listTypeSel.add(widget.listType[nSel]);
        commonEventBus.fire(PayTypeSelEvent(nIndex: nSel));
        if (mounted) {
          Get.back();
          // setState(() {});
        }
      },
      child: Container(
        width: 1.sw,
        height: 34.h,
        margin: EdgeInsets.only(bottom: 12.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Image.network(itemInfo.strImg, width: 34.w, height: 34.w),
            SizedBox(width: 12.w),
            Text(itemInfo.strTypeTip, style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600, color: BizColors.rgb1000c1018),),
            const Spacer(),
            Image.asset(bSel ? AssetsRes.checkedRound : AssetsRes.unCheckedRound, width: 21.w, height: 21.w),
          ],
        ),
      ),
    );
  }
}