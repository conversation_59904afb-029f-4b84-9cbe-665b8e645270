import 'dart:async';

import 'package:extended_image/extended_image.dart';
import 'package:flutter_common_base/data_model/tab_manage/with_draw/with_draw_type.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_set_pwd_page.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:get/get.dart';

class WithDrawApplicationResultController extends NormalPageControllerBase {



  WithDrawTypeInfo? itemInfo;
  // "typeInfo": listType[nSelTypeIndex],
  // "money": moneyTextController.text

  String strBalance = "0.0";
  @override
  void onInit() {
    super.onInit();
    itemInfo = Get.arguments["typeInfo"];
    strBalance = Get.arguments["money"] ?? "";
  }

  Future<void> onOKey () async {
    Get.back();
  }

  @override
  void onClose() {
    super.onClose();
  }
}