import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/with_draw/with_draw_type.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/withdraw_detail_controller.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class WithdrawDetailPage extends NormalPageViewBase<WithdrawDetailController> {
  WithdrawDetailPage({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  WithdrawDetailPageState createState() => WithdrawDetailPageState();
}

class WithdrawDetailPageState extends NormalPageBaseViewState<WithdrawDetailController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "提现"
    );
  }

  @override
  Widget bodyContentWidget() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent, // 点击空白区域也能触发
        onTap: () {
          ToolsUtil.hideKeyboard();
        },
        child: _buildWidgetByType());
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.h),
          _tip(strTip: "收款方式"),
          SizedBox(height: 16.h),
          _withDrayType(),
          ///七天内到账
          _timeToWithDraw(),
          SizedBox(height: 62.h),
          _tip(strTip: "提现金额"),
          SizedBox(height: 20.h),
          _moneyEdit(editController: controller.moneyTextController),
          SizedBox(height: 12.h),
          ///当前余额
          _nowRestMoney(),
          const Spacer(),
          ///提现按钮
          Row(mainAxisAlignment:MainAxisAlignment.center,children: [CustomButtonView(text: "提现",height: 41.h,width: 295.w,borderRadius: 12,onTap: controller.onWithDraw,)]),
          const SizedBox(height: BizValues.bottomMargin,)
        ],
      ),
    );
  }



  ///电话号码编辑框
  Widget _moneyEdit({TextEditingController? editController}) {
    return GetBuilder<WithdrawDetailController>(
      tag: widget.tag,
      id: "moneyEdit",
      builder: (_) {
        return Stack(
        alignment: Alignment.centerLeft,
        children: <Widget>[
          Container(
            alignment: Alignment.center,
            height: 48.h,
            width: 1.sw,
            child: TextFormField(
              textInputAction: TextInputAction.done,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: <TextInputFormatter>[
                MoneyInputFormatter(),
              ],
              cursorColor: BizColors.mainLineGradientSecond,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, color: BizColors.rgb1000c1018),
              controller: editController,
              decoration: InputDecoration(
                contentPadding: EdgeInsets.only(left: 36.w),
                filled: true,
                fillColor: BizColors.rgb100fafafa,
                hintText: "请输入提现金额",
/*                suffixIcon: phoneSuffixIcon(controller.accountEditController.text.isNotEmpty, () {
                controller.clearPhoneEdit();
              }),*/
                hintStyle: TextStyle(fontSize: 18.sp, color: BizColors.rgb100d1d8e1,fontWeight: FontWeight.w600),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(29), borderSide: BorderSide.none),),
              onChanged: (value) {
                // controller.onPhoneEditContextChanged(value);
              },
              onSaved: (value) {
                // strUserPhone = value;
              },
              onFieldSubmitted: (value) {
                ToolsUtil.hideKeyboard();
              },
            ),
          ),
          Positioned(
            left: 12.w,
            child: InkWell(
              onTap: () {
                // controller.countryCodePage();
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text("￥", style: TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w700, color: BizColors.rgb1000c1018,),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    },);

  }

  Widget _nowRestMoney() {
    return Obx(
      () => SizedBox(
        height: 36.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('当前余额${controller.dBalance}元'),
            TextButton(
                onPressed: controller.onWithDrawAllOperate,
                child: Text("全部提现", style: TextStyle(fontSize: 12.sp, color: BizColors.rgb10057b7fe, fontWeight: FontWeight.w600),))
          ],
        ),
      ),
    );
  }

  Widget _timeToWithDraw() {
    return SizedBox(
      height: 24.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('', style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3),),
        ],
      ),
    );
  }

  Widget textRule(String text) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: 18.h, maxWidth: 1.sw),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3),
      ),
    );
  }

  Widget _tip({String strTip = ""}) {
    return SizedBox(
      child: Text(strTip,style: TextStyle(fontSize: 18.sp,color: BizColors.rgb1000c1018,fontWeight: FontWeight.w600),),
    );
  }

  Widget _withDrayType() {
    return GetBuilder<WithdrawDetailController>(
        tag: widget.tag,
        id: "selType",
        builder: (_) {
          WithDrawTypeInfo info = controller.listType[controller.nSelTypeIndex];
          return InkWell(
            onTap: controller.onTypeSel,
            child: SizedBox(
              width: 1.sw,
              height: 34.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(width: 34.w, height: 34.w, child: Image.network(info.strImg, width: 34.w, height: 34.w, fit: BoxFit.fill),),
                  SizedBox(width: 12.w),
                  Text(info.strTypeTip, style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18.sp, color: BizColors.rgb1000c1018),),
                  const Spacer(),
                  InkWell(onTap: controller.onTypeSel, child: Image.asset(AssetsRes.navSmallRightIcon, width: 21.w, height: 21.w)),
                ],
              ),
            ),
          );
        });
  }

  @override
  WithdrawDetailController createController() {
    return WithdrawDetailController();
  }

}


class MoneyInputFormatter extends TextInputFormatter {
  static final _reg = RegExp(r'^\d+\.?\d{0,2}');

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;

    // 只保留合法格式（整数或带两位小数）
    if (text == '') return newValue;

    // 只保留数字和一个小数点
    String filtered = _sanitize(text);

    return TextEditingValue(
      text: filtered,
      selection: TextSelection.collapsed(offset: filtered.length),
    );
  }

  String _sanitize(String text) {
    // 移除所有非数字和小数点
    text = text.replaceAll(RegExp(r'[^\d.]'), '');

    // 保证只保留第一个小数点
    int dotIndex = text.indexOf('.');
    if (dotIndex != -1) {
      String intPart = text.substring(0, dotIndex);
      String decimalPart = text.substring(dotIndex + 1).replaceAll('.', '');
      if (decimalPart.length > 2) {
        decimalPart = decimalPart.substring(0, 2);
      }
      return '$intPart.$decimalPart';
    }

    return text;
  }
}
