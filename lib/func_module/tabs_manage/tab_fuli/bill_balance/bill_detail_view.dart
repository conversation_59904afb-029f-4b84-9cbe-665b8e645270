import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/bill_detail_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/bill_history.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class BillDetailView extends NormalPageViewBase<BillDetailController> {
  BillDetailView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  BillDetailViewState createState() => BillDetailViewState();
}

class BillDetailViewState extends NormalPageBaseViewState<BillDetailController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "账单"
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 4.h, width: 1.sw),
        _bigIcon(),
        SizedBox(height: 32.h),
        ///账单详情
        Expanded(child: TransactionTablePage()),
      ],
    );
  }

  Widget _detail() {
    return Container();
  }

  Widget _bigIcon() {
    return SizedBox(
      height: 167.w,
      width: 167.w,
      child: Image.asset(AssetsRes.imgFuLiBillDetail, width: 167.w, height: 167.w, fit: BoxFit.fill),
    );
  }

  @override
  BillDetailController createController() {
    return BillDetailController();
  }

}