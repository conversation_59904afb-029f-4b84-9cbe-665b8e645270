import 'dart:async';

import 'package:extended_image/extended_image.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/data_model/tab_manage/with_draw/with_draw_type.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_set_pwd_page.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:get/get.dart';

class BillDetailController extends NormalPageControllerBase {

  int nCurIndex = 1;
  List<TransactionData> listTrans = [];

  @override
  void onInit() {
    super.onInit();
    // getTransition();
  }

  // Future<void> getTransition({int nAction = ListRefreshAct.refreshTypeInit,int nPageIndex = 1,int nCntPerPage = BizValues.maxSizePerPage}) async {
  //   Map<String,dynamic> mapRest = await ApiReqInterface.getTransition(nCurIndex);
  //   if( mapRest["code"] != ResponseCodeParse.codeSuccess) {
  //     return;
  //   }
  //   List<TransactionData> listTransTemp = [];
  //   List items = mapRest["data"] ?? [];
  //   for (var item in items) {
  //     TransactionData dataInfo = TransactionData.fromJson(item);
  //     listTransTemp.add(dataInfo);
  //   }
  //
  //
  //   int nDebug = 0;
  // }


  Future<void> onOKey () async {

  }

  @override
  void onClose() {
    super.onClose();
  }
}