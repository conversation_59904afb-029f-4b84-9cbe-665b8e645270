import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/list_refresh_config.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/smart_refresher_widget.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class TransactionTablePage extends StatefulWidget {
  const TransactionTablePage({super.key});

  @override
  TransactionTablePageState createState() => TransactionTablePageState();
}

class TransactionTablePageState extends State<TransactionTablePage> {
  List<TransactionData> data = [];
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  int _page = 1;

  ViewLoaderState billDataLoadState = ViewLoaderState();

  @override
  void initState() {
    super.initState();
    getTransition();
  }

  Future<void> getTransition({int nAction = ListRefreshAct.refreshTypeInit,int nPageIndex = 1,int nCntPerPage = BizValues.maxSizePerPage}) async {
    Map<String,dynamic> mapRest = await ApiReqInterface.getTransition(_page);
    if( mapRest["code"] != ResponseCodeParse.codeSuccess) {
      _listStatusErrorUpdate(nListDataType: nAction);
      safeUpdate();
      return;
    }
    List<TransactionData> listTransTemp = [];
    List items = mapRest["data"] ?? [];
    for (var item in items) {
      TransactionData dataInfo = TransactionData.fromJson(item);
      listTransTemp.add(dataInfo);
    }

    _listStatusSuccessUpdate(nListDataType: nAction,result: listTransTemp);
    _page = _page + 1;
    safeUpdate();
  }

  void safeUpdate() {
    if (mounted) {
      setState(() {});
    }
  }

  ///加载失败时的处理
  void _listStatusErrorUpdate({int nListDataType = ListRefreshAct.refreshTypeInit}) {
    if (nListDataType == ListRefreshAct.refreshTypeInit) {
      billDataLoadState.loadState = LoaderState.stateNoData;
    } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
      _refreshController.refreshFailed();
    } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
      _refreshController.loadNoData();
    }
  }

  void _listStatusSuccessUpdate({int nListDataType = ListRefreshAct.refreshTypeInit,List<TransactionData> result = const [],int nPagesPerPage = BizValues.maxSizePerPage}) {
    if (result.isEmpty) {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        billDataLoadState.loadState = LoaderState.stateNoData;
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        _refreshController.refreshFailed();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        _refreshController.loadNoData();
      }
    } else {
      if (nListDataType == ListRefreshAct.refreshTypeInit) {
        data = [];
        data.addAll(result);
        billDataLoadState.loadState = LoaderState.stateSucceed;
        _refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullDown) {
        data = [];
        data.addAll(result);
        _refreshController.refreshCompleted();
        _refreshController.resetNoData();
      } else if (nListDataType == ListRefreshAct.refreshTypePullUpMore) {
        data.addAll(result);
        // if (result.length < nPagesPerPage) {
        //   refreshController.loadNoData();
        // } else {
        _refreshController.loadComplete();
        // }
      }
    }
  }


  Future<void> _loadMore() async {
    getTransition(nAction: ListRefreshAct.refreshTypePullUpMore, nPageIndex: _page);
  }

  Widget _buildHeader() {
    return Table(
      border: TableBorder.all(color: const Color(0xffd1d8e1)),
      columnWidths: const {
        0: FlexColumnWidth(1.2),
        1: FlexColumnWidth(2.3),
        2: FlexColumnWidth(1.0),
        3: FlexColumnWidth(1.0),
      },
      children: [
        TableRow(
          children: [
            _buildTableCell("收支来源", isHeader: true,textColor: BizColors.rgb100939eb3),
            _buildTableCell("交易日期", isHeader: true,textColor: BizColors.rgb100939eb3),
            _buildTableCell("交易金额", isHeader: true,textColor: BizColors.rgb100939eb3),
            _buildTableCell("状态", isHeader: true,textColor: BizColors.rgb100939eb3),
          ],
        ),
      ],
    );
  }

  Widget _buildTableRow(TransactionData row, bool isLastRow) {
    Color amountColor = row.nAmount > 0  ? BizColors.rgb100ff4141: const Color(0xff00BEAB);
    Color statusColor = const Color(0xff00beab);

    return Table(
      border: TableBorder(
        left: const BorderSide(color: BizColors.rgb100d1d8e1),
        right: const BorderSide(color: BizColors.rgb100d1d8e1),
        bottom: const BorderSide(color: BizColors.rgb100d1d8e1),
        // top: isLastRow ? const BorderSide(color: BizColors.rgb100d1d8e1) : BorderSide.none,
        verticalInside: const BorderSide(color: BizColors.rgb100d1d8e1), // ✅ 加上列之间的边框线
      ),
      columnWidths: const {
        0: FlexColumnWidth(1.2),
        1: FlexColumnWidth(2.3),
        2: FlexColumnWidth(1.0),
        3: FlexColumnWidth(1.0),
      },
      children: [
        TableRow(
          children: [
            _buildTableCell(row.strBusinessName),
            _buildTableCell(row.strCreateTime),
            _buildTableCell('${row.nAmount > 0 ? '+${row.nAmount}' : row.nAmount}', textColor: amountColor),
            _buildTableCell('',
              child: Container(
                alignment: Alignment.center,
                margin: EdgeInsets.symmetric(horizontal: 4.w),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    "已完成",
                    style: TextStyle(fontSize: 12, height:1.4,color: Colors.white),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }


  Widget _buildTableCell(String text, {Color textColor = BizColors.rgb1000c1018, bool isHeader = false, Widget? child}) {
    return Container(
      alignment: Alignment.center,
      height: 29.h,
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
      color: isHeader ? BizColors.rgb100fafafa : Colors.white,
      child: child ??
          Text(
            text,
            style: TextStyle(
              color: textColor,
              fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
              fontSize: 12.sp,
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Padding(
        padding: EdgeInsets.only(left: 12.w,right: 12.w,bottom: MediaQuery.of(Get.context!).padding.bottom),
        child: Column(
          children: [
            _buildHeader(), // 固定表头
            Expanded(
              child: SmartRefresherWidget(
                ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: data.length,
                  itemBuilder: (_, index) {
                    final isLast = index == data.length - 1;
                    return _buildTableRow(data[index], isLast);
                  },
                ),
                _refreshController,
                onPullUpLoading: _loadMore,
                // onLoading: _loadMore,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TransactionRow {
  final String amountType;
  final String date;
  final String amount;
  final String status;

  TransactionRow({
    required this.amountType,
    required this.date,
    required this.amount,
    required this.status,
  });
}