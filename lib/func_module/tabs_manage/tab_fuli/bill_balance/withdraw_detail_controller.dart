import 'dart:async';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/with_draw/with_draw_type.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/with_draw_application_result_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/with_draw_type_sel.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_set_pwd_page.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/future_loading.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';

class WithdrawDetailController extends NormalPageControllerBase {

  RxDouble dBalance = 0.0.obs;
  List<WithDrawTypeInfo> listType = [];
  int nSelTypeIndex = 0;
  late TextEditingController moneyTextController;
  StreamSubscription? eventTypeSel;
  Fluwx fluWx = Fluwx();
  late WeChatResponseSubscriber listener;
  final String strTransId = "wechat_login_bind";
  @override
  void onInit() {
    super.onInit();
    listener = chatResponseCallback;
    fluWx.addSubscriber(listener);
    moneyTextController = TextEditingController();
    _initTypeInfo();
    eventTypeSel = commonEventBus.on<PayTypeSelEvent>().listen(typeSelEvent);
    getWallet();
  }

  Future<void> chatResponseCallback(WeChatResponse response) async {
    if (response is WeChatAuthResponse) {
      if (response.state != strTransId) {
        return;
      }
      loginByRetCodeAndCode(response.errCode ?? -1, response.code ?? "");
    } else if (response is WeChatOpenBusinessViewResponse) {
      _jumpToWithDrawApplicationResultView();
    }
  }


  Future<void> loginByRetCodeAndCode(int nRetCode ,String strOpenCode) async {
    if( nRetCode != 0 || strOpenCode.isEmpty) {
      return;
    }

    ///拿着这个去绑定
    Future fuData;
    Map<String, dynamic> mapResult = <String, dynamic>{};
    fuData = ApiReqInterface.bindThirdLogin(strOpenCode: strOpenCode);
    mapResult = await Get.dialog(FutureLoadingDialog(fuData));
    if (mapResult["code"] != ResponseCodeParse.codeSuccess ) {
      ToastUtil.showToast(mapResult["msg"] ?? "");
      return;
    }
    ///绑定成功
    SingleRoutePageManage.routeToBindSuccessTipPage();
  }

  @override
  void onResumed() {
    // getWallet();
  }


  Future<void> typeSelEvent(PayTypeSelEvent event) async {
    if (event.nIndex == nSelTypeIndex) {
      return;
    }
    nSelTypeIndex = event.nIndex;
    update(["selType"]);
  }

  void _initTypeInfo() {
    listType = Get.arguments?["types"] ?? [];
  }

  Future<void> getWallet() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getWallet();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess || mapResult["data"] == null) {
      return;
    }
    dBalance.value = mapResult["data"]["balance"] ?? 0.0;
  }

  Future<void> onWithDrawAllOperate() async {
     moneyTextController.text = '${dBalance.value}';
     update(["moneyEdit"]);
  }

  Future<void> onTypeSel() async {
    Get.dialog(WithDrawTypeSel(nSelTypeIndex: nSelTypeIndex, listType: listType,), useSafeArea: false);
  }

  Future<void> onWithDraw() async {
    if (listType.isEmpty) {
      return;
    }
    int nId = listType[nSelTypeIndex].nId;

    Future fuData;
    Map<String, dynamic> mapResult = <String, dynamic>{};
    fuData = ApiReqInterface.withdrawApplication(nId, '-${moneyTextController.text}');
    mapResult = await Get.dialog(FutureLoadingDialog(fuData));
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      if (mapResult["code"] == ResponseCodeParse.codeNeedThirdBind) {
        bool bRet = await fluWx.authBy(which: NormalAuth(scope: 'snsapi_userinfo', state: strTransId,));
        return;
      }
      ToastUtil.showToast(mapResult["msg"] ?? "");
      return;
    }

    List<dynamic> rawList = mapResult["data"] ?? [];
    List<Map<String, dynamic>> list = rawList.map((e) => Map<String, dynamic>.from(e)).toList();
    String strQuery = toQueryString(list);
    strQuery = "$strQuery&appId=${Uri.encodeComponent(BizStrings.wxSdkAppId)}";
    bool bRet = await fluWx.open(target: BusinessView(businessType: BizStrings.requestMerchantTransfer, query: strQuery));

    // _jumpToWithDrawApplicationResultView();
  }


  Future<void> _jumpToWithDrawApplicationResultView() async {
    await Get.to(
        () => WithDrawApplicationResultView(
              isGlobal: false,
              tag: ToolsUtil().genUuId(),
            ),
        arguments: {
          "typeInfo": listType[nSelTypeIndex],
          "money": moneyTextController.text
        });

    Get.until((route) => route.settings.name == BizStrings.yuEPageRoute);
    ///刷新余额
    getWallet();
  }


  String toQueryString(List<Map<String, dynamic>> list) {
    return list.map((item) {
      /// 取出值列表
      var values = item.values.toList();
      if (values.length < 2) return '';
      var paramName = values[0].toString();
      var paramValue = Uri.encodeComponent(values[1].toString());
      return '$paramName=$paramValue';
    })
        .where((e) => e.isNotEmpty) // 过滤掉空字符串
        .join('&');
  }

  @override
  void onClose() {
    moneyTextController.dispose();
    eventTypeSel?.cancel();
    fluWx.removeSubscriber(listener);
    super.onClose();
  }
}