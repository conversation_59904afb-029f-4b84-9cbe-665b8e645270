import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/with_draw_application_result_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/yu_money_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class WithDrawApplicationResultView extends NormalPageViewBase<WithDrawApplicationResultController> {
  WithDrawApplicationResultView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  WithDrawApplicationResultViewState createState() => WithDrawApplicationResultViewState();
}

class WithDrawApplicationResultViewState extends NormalPageBaseViewState<WithDrawApplicationResultController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "提现申请"
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 27.h),
          _bigIcon(),
          SizedBox(height: 24.h),
          _balanceMoney(),
          const Spacer(),
          SizedBox(height: 24.h),
          mainContentTip(),
          CustomButtonView(text: "完成",width:295.w,borderRadius: 12.r,onTap: controller.onOKey,),
          const SizedBox(height: BizValues.bottomMargin)
        ],
      ),
    );
  }

  Widget mainContentTip() {
    return SizedBox(
        height: 110.h,
        width: 1.sw,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          children: [
            textRule("提现金额", controller.strBalance),
            SizedBox(height: 4.h),
            textRule("收款账户", UserInfoUtil.getUserInfo().strPhone),
            SizedBox(height: 4.h),
            textRule("收款方式", controller.itemInfo?.strTypeTip ?? ""),
          ],
        ));
  }

  Widget textRule(String text,String strRight) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: 18.h, maxWidth: 1.sw),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
        Text(
          text,
          style: TextStyle(fontSize: 18.sp, color: BizColors.rgb100939eb3),
        ),

        Text(
          strRight,
          style: TextStyle(fontSize: 18.sp, color: BizColors.rgb1000c1018,fontWeight: FontWeight.w600),
        ),
      ],),
    );
  }

  Widget _bigIcon() {
    return SizedBox(
      height: 167.w,
      width: 167.w,
      child: Image.asset(AssetsRes.imgFuLiResult, width: 81.w, height: 81.w, fit: BoxFit.fill),
    );
  }

  Widget _balanceMoney() {
    return Text("申请提现-已提交",
        style: TextStyle(fontSize: 28.sp, color: BizColors.rgb1000c1018,fontWeight: FontWeight.w600));
  }

  @override
  WithDrawApplicationResultController createController() {
    return WithDrawApplicationResultController();
  }

}