import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/common/biz_values/biz_ad_config.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/tab_fu_li.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/reward_page.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_csj/csj_union_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_common_base/util/util_withdraw_data.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:get/get.dart';

class TabFuLiController extends FullLifeCycleController with FullLifeCycleMixin {

  RxDouble dWallet = 0.0.obs;
  RxBool isLogin = false.obs;
  ///等登录通知的
  StreamSubscription? eventLogin;
  ///广告播放完成回调通知
  StreamSubscription? eventAdNotice;
  ///广告列表
  List<AdBannerData> listAds = [];

  StreamSubscription? _adStream;

  ///设备id
  String strDevId = "";
  String stUid = "";

  ///广告的transId
  String strTransId = "";

  ///是否显示加载页面
  ///为广告加载前的点位处理
  RxBool bShowLoadingPage = false.obs;

  ///广告行为
  StreamSubscription? eventAdAction;

  Timer? _timerToHide;
  final int nSecondsTimeout = 5;
  ///广告的状态
  List<String> adLoadState = [AdEventAction.onAdError,AdEventAction.onAdLoaded,AdEventAction.onAdPresent,AdEventAction.onAdExposure];

  ///关闭的状态
  List<String> adLoadStateClose = [AdEventAction.onAdError,AdEventAction.onAdSkip,AdEventAction.onAdClosed,AdEventAction.onAdComplete];

  ///从自己服务器拿到的用户获取的奖励
  double dRewardShow = 0.0;

  void _startTimer() {
    _timerToHide?.cancel();
    _timerToHide = Timer(Duration(seconds: nSecondsTimeout), () {
      ///时间到了还显示的话给强制结束掉
      if( bShowLoadingPage.value ) {
        bShowLoadingPage.value = false;
      }
    });
  }

  void _cancelTimer() {
    _timerToHide?.cancel();
    debugPrint("定时器已提前取消");
  }

  @override
  void onInit() {
    super.onInit();
    _initDevIdAndUid();
    _initEventListen();
    if (UserInfoUtil.isLogin()) {
      getWallet();
    }

    ///不受登录状态的影响
    getAdLists();
  }

  void _resetClickCnt() {
    for (var item in listAds) {
      item.nClickAlready = 0;
    }
    update(['adList']);
  }

  void _initEventListen() {
    eventLogin = commonEventBus.on<LoginStatusEvent>().listen(loginStatusChange);
    eventAdNotice = commonEventBus.on<AdRewardReceivedEvent>().listen(adCompleteEvent);
    eventAdAction = commonEventBus.on<AdSelfActionEvent>().listen(adActionEvent);
  }

  void _initDevIdAndUid() {
    strDevId = StorageUtil.get<String>(SPKey.keyDevId, defaultValue: ToolsUtil().genUuId());
    stUid = '${UserInfoUtil.getUserInfo().id}';
  }

  ///广告本身的行为 如展示 加载 完成
  Future<void> adActionEvent(AdSelfActionEvent event) async {
    debugPrint("action----------+++ ${event.nAdId} ---- ${event.strAction}");

    int nIndexFind = listAds.indexWhere((ele) => ele.strSourceId == '${event.nAdId}') ?? -1;
    if (nIndexFind < 0) {
      return;
    }

    ///看状态
    if (adLoadState.contains(event.strAction)) {
      _setMaskState(false);
    }
    ///广告关闭了
    if( adLoadStateClose.contains(event.strAction)) {
      checkCntShow();
    }
  }

  Future<void> loginStatusChange(LoginStatusEvent event) async {
    if (event.loginStatus != LogStatus.loginStatusSucceed) {
      isLogin.value = false;
      dWallet.value = 0;
      _resetClickCnt();
      return;
    }
    getWallet();
    getAdClickCount();
  }

  Future<void> adCompleteEvent(AdRewardReceivedEvent event) async {
    if (event.strSrcFrom != BizAdConfig.strAdSrcFuLi) {
      return;
    }
    strTransId = event.strTransId;
    int nIndexFind = listAds.indexWhere((ele) => ele.strSourceId == '${event.nAdId}');
    if( nIndexFind < 0 ) {
      return;
    }
    int nFindId = listAds[nIndexFind].nId;
    Map<String,dynamic> mapResult = await ApiReqInterface.commitAdReward(nAdId: nFindId,strTransId: event.strTransId,strEcpm: event.strEcpm);
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    ///拿到对应的奖励金额
    dRewardShow = mapResult["data"] ?? 0.0;
    getWallet();
    getAdClickCount();
  }

  Future<void> getAdLists() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getFuLiPageAdLists();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    List jsonList = mapResult['data'] ?? [];
    List<AdBannerData> list = jsonList.map((e) => AdBannerData.fromJson(e)).toList();
    listAds.addAll(list);
    update(['adList']);

    ///未登录不获取
    if (!UserInfoUtil.isLogin()) {
      return;
    }

    ///获取次数
    getAdClickCount();
  }

  Future<void> getAdClickCount() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getAdClickCnt();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }

    try {
      var listGet = mapResult['data'] ?? [];
      if( listGet.isEmpty ) {
        return;
      }

      List<AdClickRecordData> listResult = [];
      for (var itemInfo in listGet) {
        AdClickRecordData clickItem = AdClickRecordData.fromJson(itemInfo);
        listResult.add(clickItem);
      }

      /// 先构建一个 Map，提高匹配效率（O(n) -> O(1)）
      final Map<int, AdBannerData> adMap = {
        for (var ad in listAds) ad.nId: ad
      };

      /// 用于同步点击数据
      for (final item in listResult) {
        final ad = adMap[item.nAdId];
        if (ad != null) {
          ad.nClickAlready = item.nCount;
        }
      }
    }  catch (e) {
      int nDebug = 0;
    }
    update(['adList']);
  }

  Future<void> getWallet() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getWallet();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess || mapResult["data"] == null) {
      isLogin.value = true;
      return;
    }
    dWallet.value = mapResult["data"]["balance"] ?? 0.0;
    isLogin.value = true;
  }

  ///余额页面
  Future<void> onBalanceDetail() async {
    if( !isLogin.value ) {
      SingleRoutePageManage.routeToLoginMain();
      return;
    }
    await SingleRoutePageManage.routeToBalanceDetailPage(nowBill: dWallet.value,listTips: WithdrawInfoData().listWithdrawTips);

    ///回来后重新刷新页面数据
    getWallet();
  }

  ///账单页
  Future<void> onBillList() async {
    if( !isLogin.value ) {
      SingleRoutePageManage.routeToLoginMain();
      return;
    }
    ///跳转到账单页面
    SingleRoutePageManage.routeBillDetailView();
  }

  ///每日任务
  Future<void> onDailyTask(AdBannerData itemInfo) async {
    if (!UserInfoUtil.isLogin()) {
      SingleRoutePageManage.routeToLoginMain();
      return;
    }

    if (itemInfo.nClickAlready >= itemInfo.nClickLimit) {
      ToastUtil.showToast("每日任务已完成");
      return;
    }

    ///穿山甲平台
    _loadCsjAd(itemInfo);
    // ToastUtil.showToast("敬请期待");
  }

  Future<void> _loadCsjAd(AdBannerData itemInfo) async {
    String strAdId = itemInfo.strSourceId;
    String strUid = '${UserInfoUtil.getUserInfo().id}';
    String strExtraData = '${BizAdConfig.strAdSrcFuLi}${BizStrings.adExtraDiv}${ToolsUtil.genTimeNowMilliseconds()}';
    bool bRet = await CsjUnionUtil.showRewardVideoAds(strAdId, userId: strUid, customData: strExtraData);
    if (!bRet) {
      bRet = await CsjUnionUtil.showRewardVideoAds(strAdId, userId: strUid, customData: strExtraData);
      if (bRet) {
        _setMaskState(true);
      } else {
        _setMaskState(false);
      }
    } else {
      _setMaskState(true);
    }
  }

  void _setMaskState(bool bShow) {
    if (bShow == bShowLoadingPage.value) {
      return;
    }
    debugPrint("bShow----------+++ $bShow");
    bShowLoadingPage.value = bShow;
    if (bShow) {
      _startTimer();
    } else {
      _cancelTimer();
    }
  }

  ///每日任务广告
  Future<void> onDailyAd() async {
    if( !isLogin.value ) {
      SingleRoutePageManage.routeToLoginMain();
      return;
    }
    ///
    ToastUtil.showToast("敬请期待");
  }

  @override
  void onClose() {
    eventLogin?.cancel();
    eventAdNotice?.cancel();
    super.onClose();
  }

  @override
  void onDetached() {
    // TODO: implement onDetached
  }

  @override
  void onHidden() {
    // TODO: implement onHidden
  }

  @override
  void onInactive() {
    // TODO: implement onInactive
  }

  @override
  void onPaused() {
    int nDebug = 0;
  }

  @override
  void onResumed() {
    // getCntShow();
  }

  Future<void> getCntShow() async {
    if (strTransId.isEmpty) {
      return;
    }
    Map<String, dynamic> mapResult = await ApiReqInterface.getAdRewardCnt(strTransId: strTransId);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    strTransId = "";
    double getCnt = mapResult["data"] ?? 0.0;
    Get.dialog(AdRewardPageCenter(moneyGet: getCnt));
  }

  Future<void> checkCntShow() async {
    if (dRewardShow <= 0 ) {
      return;
    }
    Get.dialog(AdRewardPageCenter(moneyGet: dRewardShow));
    dRewardShow = 0.0;
  }
}