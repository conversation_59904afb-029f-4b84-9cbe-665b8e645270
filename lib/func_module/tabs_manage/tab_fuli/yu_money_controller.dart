import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:get/get.dart';

class YuMoneyController extends NormalPageControllerBase {

  RxDouble dBalance = 0.0.obs;
  List<String> listTips = [];
  @override
  void onInit() {
    super.onInit();
    double nowBill  = Get.arguments["balance"] ?? 0.0;
    listTips = Get.arguments?["tips"] ?? [];
    dBalance.value = nowBill;
    _dealWithTips(listTips);
    getWallet();
  }

  void _dealWithTips(List<String> listTips) {
    update(["tipContext"]);
  }


  Future<void> getWallet() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getWallet();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess || mapResult["data"] == null) {
      return;
    }
    dBalance.value = mapResult["data"]["balance"] ?? 0.0;
  }

  Future<void> onWithDraw() async {
    await SingleRoutePageManage.routeToWithdrawDetail();
    getWallet();
  }

  @override
  void onClose() {
    super.onClose();
  }
}