import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/yu_money_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class YuMoneyView extends NormalPageViewBase<YuMoneyController> {
  YuMoneyView({super.key, required super.isGlobal, super.tag, super.mapParam});

  @override
  YuMoneyViewState createState() => YuMoneyViewState();
}

class YuMoneyViewState extends NormalPageBaseViewState<YuMoneyController>{

  @override
  PreferredSizeWidget appbar() {
    return customAppbar(
        onLeadingPress: () {
          Get.back();
        },
        strTitleText: "余额"
    );
  }

  @override
  Widget bodyContentWidget() {
    return _buildWidgetByType();
  }

  Widget _buildWidgetByType() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 28.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 88.h),
          _bigCoin(),
          SizedBox(height: 24.h),
          _balanceMoney(),
          const Spacer(),
          CustomButtonView(text: "提现",width:295.w,borderRadius: 12.r,onTap: controller.onWithDraw,),
          SizedBox(height: 24.h),
          mainContentTip(),
          const SizedBox(height: BizValues.bottomMargin)
        ],
      ),
    );
  }

  Widget mainContentTip() {
    return GetBuilder<YuMoneyController>(
      tag: widget.tag,
      id: "tipContext",
      builder: (_) {
        return SizedBox(
            height: 100.h,
            width: 1.sw,
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              children: [
                ...controller.listTips.map((item) => textRule(item)),
              ],
            ));
      },
    );
  }

  Widget textRule(String text) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: 18.h, maxWidth: 1.sw),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.sp, color: BizColors.rgb100939eb3),
      ),
    );
  }

  Widget _bigCoin() {
    return SizedBox(
      height: 81.w,
      width: 81.w,
      child: Image.asset(AssetsRes.imgFuLiCoinDetail, width: 81.w, height: 81.w, fit: BoxFit.fill),
    );
  }

  Widget _balanceMoney() {
    return Obx(
      () => Text("￥${controller.dBalance.value}",
          style: TextStyle(fontSize: 44.sp, color: BizColors.rgb1000c1018)),
    );
  }

  @override
  YuMoneyController createController() {
    return YuMoneyController();
  }

}