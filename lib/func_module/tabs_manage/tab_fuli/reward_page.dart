import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AdRewardPageCenter extends StatelessWidget {
  final double moneyGet;
  const AdRewardPageCenter({super.key, this.moneyGet = 0});


  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 312.w,
        height: 346.h,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AssetsRes.imgFuLiRewardBg), fit: BoxFit.fill)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 114.h,),
            Text("恭喜你",style: TextStyle(fontSize: 24.sp,color: BizColors.rgb1000c1018,fontFamily: BizStrings.aLiMaMaBold),),
            SizedBox(height: 26.h,),
            Text("获得$moneyGet元", style: TextStyle(fontSize: 16.sp, color: BizColors.rgb1000c1018)),
            SizedBox(height: 90.h,),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomButtonView(
                  text: "完成",
                  width: 208.w,
                  height: 44.h,
                  onTap: () {
                    Get.back();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}