import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_manage_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/younger_mode/young_mode_start/youth_mode_opened_main_page.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_tab_dyn_info/dynamic_tab_info.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TabsManageView extends GetView<TabsManageController> {
  const TabsManageView({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      bottom: false,
      child: Obx(
        () => IndexedStack(
          index: controller.nInitModePage.value,
          children: [
            Scaffold(
              body: _bodyPageViewContent(),
              bottomNavigationBar: _bottomNavBarWidget(),
            ),
            ///显示青少年模式页面
            YouthModeOpenedMainPage(isGlobal: true, tag: ToolsUtil().genUuId()),
          ],
        ),
      ),
    );
  }

  Widget _bodyPageViewContent() {
    return GetBuilder<TabsManageController>(
      id: "pageIndex",
      builder: (_) {
        return PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: controller.pageController,
          children: controller.pages,
        );
      },
    );
  }

  Widget _bottomNavBarWidget() {
    final double bottomPadding = MediaQuery.of(Get.context!).padding.bottom;
    return SafeArea(
      top: false,
      bottom: false,
      child: SizedBox(
        height: 56.h + bottomPadding,
        child: ClipRRect(
          // borderRadius: const BorderRadius.all(Radius.circular(24)),
          child: GetBuilder<TabsManageController>(
            id: "bottomIndex",
            builder: (_) {
              return BottomNavigationBar(
                  backgroundColor: controller.bShowRecommend ? BizColors.rgb1000c1018 : Colors.white,
                  elevation: 0,
                  iconSize: 24.w,
                  unselectedItemColor:  controller.bShowRecommend ? Colors.white : BizColors.rgb1000c1018,
                  selectedItemColor:  controller.bShowRecommend ? Colors.white : BizColors.rgb1000c1018,
                  selectedLabelStyle: TextStyle(fontSize:12.sp,fontWeight: FontWeight.w500,color: controller.bShowRecommend ? Colors.white : BizColors.rgb1000c1018),
                  unselectedLabelStyle: TextStyle(fontSize:12.sp,fontWeight: FontWeight.w400,color: controller.bShowRecommend ? Colors.white : BizColors.rgb1000c1018),
                  currentIndex: controller.currentIndex,
                  ///第几个菜单选中
                  type: BottomNavigationBarType.fixed,
                  ///如果底部有4个或者4个以上的菜单的时候就需要配置这个参数
                  onTap: (index) {
                    controller.setCurrentIndex(index);
                  },
                  items: _bottomItems(bRecommendSel: controller.bShowRecommend));
            },
          ),
        ),
      ),
    );
  }

  BottomNavigationBarItem bottomBarItem(
      String strIconSel, String strIconUnSel, String label) {
    return BottomNavigationBarItem(
        icon: SizedBox(
            width: 24.w,
            child: strIconUnSel.startsWith('http')
                ? CachedNetworkImage(imageUrl: strIconUnSel, width: 24.w, height: 24.w, useOldImageOnUrlChange: true,fadeOutDuration: const Duration(milliseconds: 0),fadeInDuration: const Duration(milliseconds: 0),)
                : Image.asset(strIconUnSel, width: 24.w, height: 24.w, gaplessPlayback: true)),
        activeIcon: SizedBox(
          width: 24.w,
          child: strIconSel.startsWith('http')
              ? CachedNetworkImage(imageUrl: strIconSel, width: 24.w, height: 24.w, useOldImageOnUrlChange: true,fadeOutDuration: const Duration(milliseconds: 0),fadeInDuration: const Duration(milliseconds: 0),)
              : Image.network(strIconSel, width: 24.w, height: 24.w, gaplessPlayback: true),
        ),
        label: label);
  }

  List<BottomNavigationBarItem> _bottomItems({bool bRecommendSel = false}) {
    List<BottomNavigationBarItem> listBotItem = [];
    int nLen = DynamicTabInfo.listNavTabInfo.length;
    if (nLen > 0) {
      for (int nIndex = 0; nIndex < nLen; nIndex++) {
        BotNavItemInfo itemInfo = DynamicTabInfo.listNavTabInfo[nIndex];
        switch (itemInfo.id) {
          case 1:
            {
              listBotItem.add(bottomBarItem(itemInfo.activeIcon, itemInfo.inactiveBlackIcon, itemInfo.name));
            }
            break;
          // case 2:
          //   {
          //     listBotItem.add(bottomBarItem(itemInfo.activeIcon, bRecommendSel ? itemInfo.inactiveWhiteIcon : itemInfo.inactiveBlackIcon, itemInfo.name));
          //   }
          //   break;
          // case 3:
          //   {
          //     listBotItem.add(bottomBarItem(itemInfo.activeIcon, bRecommendSel ? AssetsRes.tabFuLiUnSelWhite : AssetsRes.tabFuLiUnSel, LanStrings.tabFuLi.tr));
          //   }
          //   break;
          // case 4:
          //   {
          //     listBotItem.add(bottomBarItem(itemInfo.activeIcon, bRecommendSel ? AssetsRes.tabBookUnSelWhite : AssetsRes.tabBookUnSel, LanStrings.tabBook.tr));
          //   }
          //   break;
          // case 5:
          //   {
          //     listBotItem.add(bottomBarItem(itemInfo.activeIcon, bRecommendSel ? AssetsRes.tabMyUnSelWhite : AssetsRes.tabMyUnSel, LanStrings.tabMy.tr),);
          //   }
          //   break;
          default:
            {
              listBotItem.add(bottomBarItem(itemInfo.activeIcon, bRecommendSel ? itemInfo.inactiveWhiteIcon : itemInfo.inactiveBlackIcon, itemInfo.name));
            }
            break;
        }
      }
    } else {
      for (int nIndex = 0; nIndex < controller.listTab.length; nIndex++) {
        TabIndexData indexData = controller.listTab[nIndex];
        switch (indexData.strName) {
          case LanStrings.tabRecommend:
            {
              listBotItem.add(bottomBarItem(AssetsRes.tabRecommendSel, AssetsRes.tabRecommendUnSel, LanStrings.tabRecommend.tr));
            }
            break;
          case LanStrings.tabShortVideo:
            {
              listBotItem.add(bottomBarItem(AssetsRes.tabVideoSel, bRecommendSel ? AssetsRes.tabVideoUnSelWhite : AssetsRes.tabVideoUnSel, LanStrings.tabShortVideo.tr));
            }
            break;
          case LanStrings.tabFuLi:
            {
              listBotItem.add(bottomBarItem(AssetsRes.tabFuLiSel, bRecommendSel ? AssetsRes.tabFuLiUnSelWhite : AssetsRes.tabFuLiUnSel, LanStrings.tabFuLi.tr));
            }
            break;
          case LanStrings.tabBook:
            {
              listBotItem.add(bottomBarItem(AssetsRes.tabBookSel, bRecommendSel ? AssetsRes.tabBookUnSelWhite : AssetsRes.tabBookUnSel, LanStrings.tabBook.tr));
            }
            break;
          case LanStrings.tabMy:
            {
              listBotItem.add(bottomBarItem(AssetsRes.tabMySel, bRecommendSel ? AssetsRes.tabMyUnSelWhite : AssetsRes.tabMyUnSel, LanStrings.tabMy.tr),);
            }
            break;
          default:
            break;
        }
      }
    }

    return listBotItem;
  }
}
