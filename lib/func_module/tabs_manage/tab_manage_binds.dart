import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/tab_fuli_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_manage_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_messages/tab_message_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/tab_my_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_recommend/tab_commend_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/tab_videos_controller.dart';
import 'package:get/get.dart';

class TabsManageBinding extends Bindings {
  @override
  void dependencies() {
    ///默认主页
    Get.lazyPut<TabsManageController>(() => TabsManageController());
  }
}

class TabRecommendBinding extends Bindings {
  @override
  void dependencies() {
    ///推荐
    Get.lazyPut<TabRecommendController>(() => TabRecommendController());
  }
}

class TabVideosBinding extends Bindings {
  @override
  void dependencies() {
    ///剧场
    Get.lazyPut<TabVideosController>(() => TabVideosController());
  }
}

class TabMsgBinding extends Bindings {
  @override
  void dependencies() {
    ///消息
    Get.lazyPut<TabMessageController>(() => TabMessageController());
  }
}

class TabMyBinding extends Bindings {
  @override
  void dependencies() {
    ///我的
    Get.lazyPut<TabMyController>(() => TabMyController());
  }
}

class TabMessageBinding extends Bindings {
  @override
  void dependencies() {
    ///我的
    Get.lazyPut<TabMessageController>(() => TabMessageController());
  }
}

class TabFuLiBinding extends Bindings {
  @override
  void dependencies() {
    ///福利页面
    Get.lazyPut<TabFuLiController>(() => TabFuLiController());
  }
}
