class UserInfoDetail {
  int id = 0;
  String strNickName = "";
  String strAvatar = "";
  int nGender = 1;
  String strPhone = "";
  String strBirthday = "";
  String strDesc = "";
  ///暂不考虑
  int nStatus = 1;
  ///所在地区的Id
  int nAreaId = 0;
  ///ip属地
  String strIpLocation = "";
  ///所有区域的关联信息 比如河南省 郑州市
  List<AreaInfoUnion> listAreaInfo = [];

  String strCreateTime = "";
  String strUpdateTime = "";

  UserInfoDetail({
    this.id = 0,
    this.strNickName = "",
    this.strAvatar = "",
    this.nGender = 1,
    this.strPhone = "",
    this.strBirthday = "",
    this.strDesc = "",
    this.nStatus = 1,
    this.nAreaId = 0,
    this.strIpLocation = "",
    this.listAreaInfo = const [],
    this.strCreateTime = "",
    this.strUpdateTime = "",
  });


  UserInfoDetail.fromJson(Map<String,dynamic>mapData) {
    id = mapData["id"] ?? 0;
    strNickName = mapData["nickname"] ?? "";
    strAvatar = mapData["picture"] ?? "";
    nGender = mapData["gender"] ?? 1;
    strPhone = mapData["phone"] ?? "";
    strBirthday = mapData["birthday"] ?? "";
    strDesc = mapData["description"] ?? "";
    strIpLocation = mapData["ipLocation"] ?? "";
    strCreateTime = mapData["createTime"] ?? "";
    strUpdateTime = mapData["updateTime"] ?? "";

    listAreaInfo = [];
    var areaAncestors = mapData['areaAncestors'] ?? [];
    for( var item in areaAncestors ) {
      AreaInfoUnion areaInfo = AreaInfoUnion.fromJson(item);
      listAreaInfo.add(areaInfo);
    }
  }
}

///所在地区的一个上下级关联信息
class AreaInfoUnion {
  int nId = 0;
  String strName = "";
  int nLevel = 0;
  int nParentId = 0;

  AreaInfoUnion({this.nId = 0, this.strName = "", this.nLevel = 0, this.nParentId = 0});

  AreaInfoUnion.fromJson(Map<String, dynamic> mapData) {
    nId = mapData["id"] ?? 0;
    strName = mapData["name"] ?? "";
    nLevel = mapData["level"] ?? "";
    nParentId = mapData["parentId"] ?? 0;
  }
}