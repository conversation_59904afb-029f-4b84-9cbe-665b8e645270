  class ThirdLoginTypeInfo {
  ///登录方式
  int nType = -1;
  String strName = "";
  String iconUrl = "";

  ThirdLoginTypeInfo({this.nType = -1, this.strName = "", this.iconUrl = ""});

  ThirdLoginTypeInfo.fromJson(Map<String, dynamic> mapJson) {
    nType = mapJson["code"] ?? -1;
    strName = mapJson["name"] ?? "";
    iconUrl = mapJson["icon"] ?? "";
  }
}

class LoginType {
  ///手机号登录
  static const int loginPhone = 1;

  ///表明是三方登录
  static const int loginTypeThird = 2;
}

class ThirdLoginTypeSrc {
  ///手机号登录
  static const String loginSrcWx = "1";
}

  ///跳转到某个页面的原因
class PageFromReason {
  ///需要绑定手机号
  static const String fromReasonBind = "needBindPhone";
}

  ///提现方式
class WithdrawType {
  static const int typeWx = 1;
  static const int typeALiPay = 2;
}
