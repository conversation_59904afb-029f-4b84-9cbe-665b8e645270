
import 'package:flutter_common_base/common/biz_values/biz_ad_config.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:get/get.dart';

class VideoContentModel {
  VideoSettings? videoSettings = VideoSettings();
  StorySettings? storySettings = StorySettings();
  VideoContentModel();
  String strAppId = GetPlatform.isAndroid ? BizAdConfig.appId : BizAdConfig.appIdIOS;

  VideoContentModel.fromJson(Map<String, dynamic> json) {
    strAppId = '${json["appId"] ?? ""}';
    if (strAppId.isEmpty) {
      strAppId = GetPlatform.isAndroid ? BizAdConfig.appId : BizAdConfig.appIdIOS;
    }
    videoSettings = json['videoSettings'] != null ? VideoSettings.fromJson(json['videoSettings']) : VideoSettings();
    storySettings = json['storySettings'] != null ? StorySettings.fromJson(json['storySettings']) : StorySettings();
  }
}

class VideoSettings {
  AdEpisodes? adEpisodes = AdEpisodes();
  double dFreeEpisodes = 0.2;
  int nUnlockEpisodes = 10;

  VideoSettings();

  VideoSettings.fromJson(Map<String, dynamic> json) {
    adEpisodes = json['adEpisodes'] != null ? AdEpisodes.fromJson(json['adEpisodes']) : AdEpisodes();
    dFreeEpisodes = json['freeEpisodes']?.toDouble() ?? 0.2;
    nUnlockEpisodes = json['unlockEpisodes'] ?? 10;
  }
}

class AdEpisodes {
  int nId = ToolsUtil.safeParseInt(BizAdConfig.jiLiAdId);
  int nRecommendType = 1;

  AdEpisodes({
    this.nId = 103515414,
    this.nRecommendType = 1,
  });

  AdEpisodes.fromJson(Map<String, dynamic> json) {
    nId = json['id'] ?? 103515414;
    nRecommendType = json['recommendType'] ?? 1;
  }
}

class StorySettings {
  AdChapters? adChapters = AdChapters();
  StorySettings();

  StorySettings.fromJson(Map<String, dynamic> json) {
    adChapters = json['adChapters'] != null ? AdChapters.fromJson(json['adChapters']) : AdChapters();
  }
}

class AdChapters {
  int nId = 103531306;
  int nRecommendType = 1;

  AdChapters({
    this.nId = 103531306,
    this.nRecommendType = 1,
  });

  AdChapters.fromJson(Map<String, dynamic> json) {
    nId = json['id'] ?? 103531306;
    nRecommendType = json['recommendType'] ?? 1;
  }
}
