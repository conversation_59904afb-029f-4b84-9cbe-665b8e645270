class ActorsInfo extends ActorsAndVideosCommon {
  LikeActorsData businessData = LikeActorsData();

  ActorsInfo.fromJson(Map<String, dynamic> mapData) : super.fromJson(mapData) {
    businessData = LikeActorsData.fromJson(mapData["businessData"] ?? {});
  }
}


class VideoInfo extends ActorsAndVideosCommon {
  VideoData businessData = VideoData();

  VideoInfo.fromJson(Map<String, dynamic> item) : super.fromJson(item) {
    businessData = VideoData.fromJson(item);
  }

}

class ActorsAndVideosCommon {
  int nId = 0;
  String strCreateTime = "";
  String strUpdateTime = "";
  int nUId = 0;
  int nType = 0;
  int nBusinessType = 0;
  int nBusinessId = 0;

  ActorsAndVideosCommon({
    this.nId = 0,
    this.strCreateTime = "",
    this.strUpdateTime = "",
    this.nUId = 0,
    this.nType = 0,
    this.nBusinessId = 0,
  });

  ActorsAndVideosCommon.fromJson(Map<String,dynamic>mapData) {
    nId = mapData["id"] ?? "";
    strCreateTime = mapData["createTime"] ?? "";
    strUpdateTime = mapData["updateTime"] ?? "";
    nUId = mapData["uid"] ?? 0;
    nType = mapData["type"] ?? 0;
    nBusinessId = mapData["businessId"] ?? 0;
    nBusinessType = mapData["businessType"] ?? 0;
  }
}



///短剧的分类，目前一个短剧属于一个分类
class VideoCat {
  int id = 0;
  String strCreateTime = "";
  String strUpdateTime = "";
  int nStatus = 0;
  String strName = "";
  int nLevel = 0;
  int nSort = 0;
  int nParentId = 0;

  VideoCat(
      {this.id = 0,
      this.strCreateTime = "",
      this.strUpdateTime = "",
      this.nStatus = 0,
      this.strName = "",
      this.nLevel = 0,
      this.nSort = 0,
      this.nParentId = 0});

  VideoCat.fromJson(Map<String, dynamic> mapData) {
    id = mapData["id"] ?? 0;
    strCreateTime = mapData["createTime"] ?? "";
    strUpdateTime = mapData["updateTime"] ?? "";
    nStatus = mapData["status"] ?? 0;
    strName = mapData["name"] ?? "";
    nLevel = mapData["level"] ?? 0;
    nSort = mapData["sort"] ?? 0;
    nParentId = mapData["parentId"] ?? 0;
  }
}

///演员数据
class LikeActorsData {
  int nId = 0;
  String strCreateTime = "";
  String strUpdateTime = "";
  String strName = "";
  String strHeadImg = "";
  String strDesc = "";

  LikeActorsData(
      {this.nId = 0,
      this.strCreateTime = "",
      this.strUpdateTime = "",
      this.strName = "",
      this.strHeadImg = "",
      this.strDesc = ""});

  LikeActorsData.fromJson(Map<String, dynamic> mapResult) {
    nId = mapResult["id"] ?? 0;
    strCreateTime = mapResult["createTime"] ?? "";
    strUpdateTime = mapResult["updateTime"] ?? "";
    strName = mapResult["name"] ?? "";
    strHeadImg = mapResult["picture"] ?? "";
    strDesc = mapResult["description"] ?? "";
  }
}


///短剧
class VideoData {
  int nId = 0;
  String strCreateTime = "";
  String strUpdateTime = "";
  int nStatus = 0;
  int nType = 0;
  int nChannel = 0;
  bool nFinished = true;
  String strPubTime = "";
  int nSource = 0;
  String strSourceId = "";
  String strPlayUrl = "";
  String strName = "";
  String strSummary = "";
  String strDescription = "";
  String strCover = "";
  ///总集数
  int nTotalEpisode = 0;
  ///完成的的集数
  int nFinishedEpisode = 0;
  bool bHot = false;
  bool bLatest = false;
  bool bExclusive = false;
  int nLevel = 0;
  bool bPotential = false;
  String icpNumber = "";

  VideoCat videoCat = VideoCat();

  VideoData({
    this.nId = 0,
    this.strCreateTime = "",
    this.strUpdateTime = "",
    this.nStatus = 0,
    this.nType = 0,
    this.nChannel = 0,
    this.nFinished = true,
    this.strPubTime = "",
    this.nSource = 0,
    this.strSourceId = "",
    this.strPlayUrl = "",
    this.strName = "",
    this.strSummary = "",
    this.strCover = "",
    this.nTotalEpisode = 0,
    this.nFinishedEpisode = 0,
    this.bHot = false,
    this.bLatest = false,
    this.bExclusive = false,
    this.nLevel = 0,
    this.bPotential = false,
    this.icpNumber = "",
    this.strDescription = "",
  });

  VideoData.fromJson(Map<String, dynamic> mapData) {
    nId = mapData["id"] ?? 0;
    strCreateTime = mapData["createTime"] ?? "";
    strUpdateTime = mapData["updateTime"] ?? "";
    nStatus = mapData["status"] ?? 0;
    nType = mapData["type"] ?? 0;
    nChannel = mapData["channel"] ?? 0;
    nFinished = mapData["finished"] ?? true;
    strPubTime = mapData["publishTime"] ?? "";
    nSource = mapData["source"] ?? 0;
    strSourceId = mapData["sourceId"] ?? "";
    strPlayUrl = mapData["playURL"] ?? "";
    strName = mapData["name"] ?? "";
    strSummary = mapData["summary"] ?? "";
    strCover = mapData["cover"] ?? "";
    nTotalEpisode = mapData["allEpisodeCount"] ?? 0;
    nFinishedEpisode = mapData["finishedEpisodeCount"] ?? 0;
    bHot = mapData["hot"] ?? false;
    bLatest = mapData["latest"] ?? false;
    bExclusive = mapData["exclusive"] ?? false;
    nLevel = mapData["level"] ?? 0;
    bPotential = mapData["potential"] ?? false;
    icpNumber = mapData["icpNumber"] ?? "";
    strDescription = mapData["description"] ?? "";
    videoCat = VideoCat.fromJson(mapData["category"] ?? {});
  }
}


class TransactionData {
  int nId = 0;
  String strCreateTime = "";
  String strUpdateTime = "";
  int nAppId = 0;
  int nBusinessId = 0;
  int nType = 0;
  int nUid = 0;
  num nBeforeBalance = 0;
  num nAmount = 0;
  num nAfterBalance = 0;
  String strFlowNumber = "";
  String strRemark = "";
  String strAppName = "";
  String strBusinessName = "";

  TransactionData({
    this.nId = 0,
    this.strCreateTime = "",
    this.strUpdateTime = "",
    this.nAppId = 0,
    this.nBusinessId = 0,
    this.nType = 0,
    this.nUid = 0,
    this.nBeforeBalance = 0,
    this.nAmount = 0,
    this.nAfterBalance = 0,
    this.strFlowNumber = "",
    this.strRemark = "",
    this.strAppName = "",
    this.strBusinessName = "",
  });

  TransactionData.fromJson(Map<String, dynamic> mapResult) {
    nId = mapResult["id"] ?? 0;
    strCreateTime = mapResult["createTime"] ?? "";
    strUpdateTime = mapResult["updateTime"] ?? "";
    nAppId = mapResult["appId"] ?? 0;
    nBusinessId = mapResult["businessId"] ?? 0;
    nType = mapResult["type"] ?? 0;
    nUid = mapResult["uid"] ?? 0;
    nBeforeBalance = (mapResult["beforeBalance"] ?? 0) as num;
    nAmount = (mapResult["amount"] ?? 0) as num;
    nAfterBalance = (mapResult["afterBalance"] ?? 0) as num;
    strFlowNumber = mapResult["flowNumber"] ?? "";
    strRemark = mapResult["remark"] ?? "";
    strAppName = mapResult["appName"] ?? "";
    strBusinessName = mapResult["businessName"] ?? "";
  }
}

