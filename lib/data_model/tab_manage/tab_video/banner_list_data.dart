class BannerListData {
  int nId = 0;
  String strCreateTime = "";
  String strUpdateTime = "";
  String strTitle = "";
  String strDescription = "";
  int nType = 0;
  String strRoute = "";
  String strCover = "";

  BannerListData({
    this.nId = 0,
    this.strCreateTime = "",
    this.strUpdateTime = "",
    this.strTitle = "",
    this.strDescription = "",
    this.nType = 0,
    this.strRoute = "",
    this.strCover = "",
  });

  BannerListData.fromJson(Map<String, dynamic> mapData) {
    nId = mapData["id"] ?? 0;
    strCreateTime = mapData["createTime"] ?? "";
    strUpdateTime = mapData["updateTime"] ?? "";
    strTitle = mapData["title"] ?? "";
    strDescription = mapData["description"] ?? "";
    nType = mapData["type"] ?? 0;
    strRoute = mapData["route"] ?? "";
    strCover = mapData["cover"] ?? "";
  }
}
