class TabIndexData {
  String strName = "";
  int nIndex = -1;
  String strIcon = "";
  String strIconActive = "";

  TabIndexData(
      {this.strName = "",
      this.strIcon = "",
      this.strIconActive = "",
      this.nIndex = -1});
}


///短剧下面的分类目录
class VideoCatData {
  int id = 0;
  String createTime = "";
  String updateTime = "";
  int status = 0;
  String name = "";
  int level = 0;
  int sort = 0;
  int parentId = 0;

  VideoCatData({
    this.id = 0,
    this.createTime = "",
    this.updateTime = "",
    this.status = 0,
    this.name = "",
    this.level = 0,
    this.sort = 0,
    this.parentId = 0,
  });

  VideoCatData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    createTime = json['createTime'] ?? "";
    updateTime = json['updateTime']?? "";
    status = json['status'] ?? "";
    name = json['name'] ?? "";
    level = json['level'] ?? 0;
    sort = json['sort'] ?? 0;
    parentId = json['parentId'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createTime': createTime,
      'updateTime': updateTime,
      'status': status,
      'name': name,
      'level': level,
      'sort': sort,
      'parentId': parentId,
    };
  }
}

///搜索索引页面的标签数据
class CatIndexData {
  String strName = "";
  int nId = -1;
  CatIndexData({this.strName = "", this.nId = -1});
}


/// 更新信息项
class UpdateInfo {
  int id = 0;
  String createTime = "";
  String updateTime = "";
  int type = 0;
  String code = "";
  String publishTime = "";
  String apkURL = "";
  String ipaURL = "";
  bool forceUpdate = false;
  String description = "";

  UpdateInfo({
    this.id = 0,
    this.createTime = "",
    this.updateTime = "",
    this.type = 0,
    this.code = "",
    this.publishTime = "",
    this.apkURL = "",
    this.ipaURL = "",
    this.forceUpdate = false,
    this.description = "",
  });

  UpdateInfo.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    createTime = json['createTime'] ?? "";
    updateTime = json['updateTime'] ?? "";
    type = json['type'] ?? 0;
    code = json['code'] ?? "";
    publishTime = json['publishTime'] ?? "";
    apkURL = json['apkURL'] ?? "";
    ipaURL = json['ipaURL'] ?? "";
    forceUpdate = json['forceUpdate'] ?? false;
    description = json['description'] ?? "";
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createTime': createTime,
      'updateTime': updateTime,
      'type': type,
      'code': code,
      'publishTime': publishTime,
      'apkURL': apkURL,
      'ipaURL': ipaURL,
      'forceUpdate': forceUpdate,
      'description': description,
    };
  }
}

/// 底部导航项信息
class BotNavItemInfo {
  int id = 0;
  String createTime = "";
  String updateTime = "";
  int status = 0;
  String name = "";
  String view = "";
  String activeIcon = "";
  String inactiveBlackIcon = "";
  String inactiveWhiteIcon = "";
  int sort = 0;

  BotNavItemInfo({
    this.id = 0,
    this.createTime = "",
    this.updateTime = "",
    this.status = 0,
    this.name = "",
    this.view = "",
    this.activeIcon = "",
    this.inactiveBlackIcon = "",
    this.inactiveWhiteIcon = "",
    this.sort = 0,
  });

  BotNavItemInfo.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    createTime = json['createTime'] ?? "";
    updateTime = json['updateTime'] ?? "";
    status = json['status'] ?? 0;
    name = json['name'] ?? "";
    view = json['view'] ?? "";
    activeIcon = json['activeIcon'] ?? "";
    inactiveBlackIcon = json['inactiveBlackIcon'] ?? "";
    inactiveWhiteIcon = json['inactiveWhiteIcon'] ?? "";
    sort = json['sort'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createTime': createTime,
      'updateTime': updateTime,
      'status': status,
      'name': name,
      'view': view,
      'activeIcon': activeIcon,
      'inactiveIcon': inactiveBlackIcon,
      "inactiveWhiteIcon": inactiveWhiteIcon,
      'sort': sort,
    };
  }
}
