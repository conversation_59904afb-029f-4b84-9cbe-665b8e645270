class WithDrawType {
  static const int nTypeALiPay = 1;
  static const int nTypeWeiXin = 2;
}

class WithDrawTypeInfo {
  int nId = 0;
  String strImg = "";
  String strTypeTip = "";

  WithDrawTypeInfo({this.nId = 0, this.strImg = "", this.strTypeTip = ""});

  WithDrawTypeInfo.fromJson(Map<String, dynamic> mapParam) {
    nId = mapParam["code"] ?? -1;
    strTypeTip = mapParam["name"] ?? "";
    strImg = mapParam["icon"] ?? "";
  }
}
