import 'package:flutter_common_base/common/biz_values/biz_values.dart';

class AdBannerData {
  int nId = 0;
  String strCreateTime = "";
  String strUpdateTime = "";
  String strTitle = "";
  String strIcon = "";
  String strDescription = "";
  ///广告类型 如激励视频广告
  int nType = 0;
  ///广告平台：1、穿山甲 2 、快手
  int nSource = 0;
  ///广告位id
  String strSourceId = "";
  int nPositionId = 0;
  int nClickLimit = BizValues.nAdClickLimit;
  int nClickAlready = 0;

  AdBannerData({
    this.nId = 0,
    this.strCreateTime = "",
    this.strUpdateTime = "",
    this.strTitle = "",
    this.strIcon = "",
    this.strDescription = "",
    this.nType = 0,
    this.nSource = 0,
    this.strSourceId = "",
    this.nPositionId = 0,
    this.nClickLimit = BizValues.nAdClickLimit,
    this.nClickAlready = 0,
  });

  AdBannerData.fromJson(Map<String, dynamic> mapData) {
    nId = mapData["id"] ?? 0;
    strCreateTime = mapData["createTime"] ?? "";
    strUpdateTime = mapData["updateTime"] ?? "";
    strTitle = mapData["title"] ?? "";
    strIcon = mapData["icon"] ?? "";
    strDescription = mapData["description"] ?? "";
    nType = mapData["type"] ?? 0;
    nSource = mapData["source"] ?? 0;
    strSourceId = mapData["sourceId"] ?? "";
    nPositionId = mapData["positionId"] ?? 0;
    nClickLimit = mapData["clickLimit"] ?? BizValues.nAdClickLimit;
  }

  Map<String, dynamic> toJson() {
    return {
      "id": nId,
      "createTime": strCreateTime,
      "updateTime": strUpdateTime,
      "title": strTitle,
      "icon": strIcon,
      "description": strDescription,
      "type": nType,
      "source": nSource,
      "sourceId": strSourceId,
      "positionId": nPositionId,
      "clickLimit" : nClickLimit,
      "clickAlready": nClickAlready,
    };
  }

  @override
  String toString() {
    return 'AdBannerData{id: $nId, createTime: $strCreateTime, updateTime: $strUpdateTime, '
        'title: $strTitle, icon: $strIcon, description: $strDescription, type: $nType, '
        'source: $nSource, sourceId: $strSourceId, positionId: $nPositionId},"clickLimit": $nClickLimit,"clickAlready": $nClickAlready';
  }
}


class AdClickRecordData {
  int nId = 0;
  String strCreateTime = "";
  int nUid = 0;
  int nAdId = 0;
  int nCount = 0;

  AdClickRecordData({
    this.nId = 0,
    this.strCreateTime = "",
    this.nUid = 0,
    this.nAdId = 0,
    this.nCount = 0,
  });

  AdClickRecordData.fromJson(Map<String, dynamic> mapData) {
    nId = mapData["id"] ?? 0;
    strCreateTime = mapData["date"] ?? "";
    nUid = mapData["uid"] ?? 0;
    nAdId = mapData["adId"] ?? 0;
    nCount = mapData["clickCount"] ?? 0;
  }

  Map<String, dynamic> toJson() {
    return {
      "id": nId,
      "createTime": strCreateTime,
      "uid": nUid,
      "adId": nAdId,
      "count": nCount,
    };
  }

  @override
  String toString() {
    return 'AdClickRecordData{id: $nId, createTime: $strCreateTime, '
        'uid: $nUid, adId: $nAdId, count: $nCount}';
  }
}

