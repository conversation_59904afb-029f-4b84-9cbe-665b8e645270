
import 'package:dio/dio.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';


enum LogStatus {
  loginStatusUnKnown,
  loginStatusSucceed,
  loginStatusFailed,
}

class FilterRespEvent {
  Response response;

  FilterRespEvent({required this.response });
}

///登录状态
class LoginStatusEvent {
  ///登录状态 0: 不进行登录了（目的是为了给出一个确定的状态，可能用不到 ）  1: 登录成功 2： 退出登录
  LogStatus loginStatus = LogStatus.loginStatusUnKnown;
  LoginStatusEvent({this.loginStatus = LogStatus.loginStatusUnKnown});
}

///点击了地区的条目
class AreaItemClickEvent {
  AreaInfoUnion itemInfo;
  int nLevel = 0;

  AreaItemClickEvent({required this.itemInfo, this.nLevel = 0});
}

///青少年开着的事件通知
class YouthModeStateEvent {
  bool bOpen = false;

  YouthModeStateEvent({this.bOpen = false});
}

///搜索内容变化
class SearchKeyWorkChangeEvent {
  String strKeyWord = "";

  SearchKeyWorkChangeEvent({this.strKeyWord = ""});
}


class SearchKeyHistoryEvent {
  String? strKeyWord;

  SearchKeyHistoryEvent(this.strKeyWord);
}

class RouteChangeEvent {
  String strRouteCurName = "";
  String strRoutePre = "";

  RouteChangeEvent({this.strRouteCurName = "", this.strRoutePre = ""});
}


class MainTabChange {
  String strName = "";

  MainTabChange({this.strName = ""});
}

///选择提现方式
class PayTypeSelEvent {
  int nIndex = 1;

  PayTypeSelEvent({this.nIndex = 1});
}


class MyHisOrColTabChgEvent {
  int nIndex = 0;
  String strSourceId = "";

  MyHisOrColTabChgEvent({this.nIndex = 1, this.strSourceId = ""});
}

class AdRewardReceivedEvent {
  int nAdId = -1;
  String strSrcFrom = "";
  String strTransId = "";
  String strExtraData = "";
  String strEcpm = "";

  AdRewardReceivedEvent(
      {this.nAdId = 0,
      this.strSrcFrom = "",
      this.strTransId = "",
      this.strExtraData = "",
      this.strEcpm = ""});
}


class AdSelfActionEvent {
  int nAdId = -1;
  String strAction = "";

  AdSelfActionEvent({this.nAdId = 0, this.strAction = ""});
}
