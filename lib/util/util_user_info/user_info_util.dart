import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/user_info/user_info_detail.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';

class UserInfoUtil {
  static UserInfoDetail userInfoDetail = UserInfoDetail();

  static setUserInfo(UserInfoDetail userInfo,{bool isLogin = true}) {
    userInfoDetail = userInfo;
    commonEventBus.fire(LoginStatusEvent(loginStatus: isLogin? LogStatus.loginStatusSucceed : LogStatus.loginStatusUnKnown));
  }

  static UserInfoDetail getUserInfo() {
    return userInfoDetail;
  }

  static String getToken() {
    String strToken = "";
    strToken = StorageUtil.get<String>(SPKey.keyToken);
    return strToken;
  }

  static bool isLogin() {
    String strToken = getToken();
    return strToken.isNotEmpty && userInfoDetail.id > 0 ;
  }

  static getUserInfoArea(UserInfoDetail userInfo) {
    String areaInfo = "";
    for (int nIndex = 0; nIndex < userInfo.listAreaInfo.length; nIndex++) {
      AreaInfoUnion infoItem = userInfo.listAreaInfo[nIndex];
      areaInfo = areaInfo + infoItem.strName;
      if ((nIndex + 1) < userInfo.listAreaInfo.length) {
        areaInfo = "$areaInfo-";
      }
    }
    return areaInfo;
  }

  ///返回用户的详细信息
  ///
  static Future<Map<String, dynamic>> getUserInfoDetail() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getUserInfoDetail();
    int nRetCode = mapResult["code"] ?? -1;
    if (nRetCode != ResponseCodeParse.codeSuccess) {
      if (nRetCode == ResponseCodeParse.codeNeedBindPhone) {
        return mapResult;
      }
      StorageUtil.remove(SPKey.keyToken);
      commonEventBus.fire(LoginStatusEvent(loginStatus: LogStatus.loginStatusFailed));
      return {"code": nRetCode};
    }

    UserInfoDetail detail = UserInfoDetail.fromJson(mapResult["data"] ?? {});
    setUserInfo(detail);
    return {"code": 0, "userInfo": detail};
  }
}
