import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/image_preload_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';

class DynamicTabInfo {
  static List<BotNavItemInfo> listNavTabInfo = [
    BotNavItemInfo(name: LanStrings.tabRecommend,activeIcon: AssetsRes.tabRecommendSel,inactiveBlackIcon: AssetsRes.tabRecommendUnSel),
    BotNavItemInfo(name: LanStrings.tabShortVideo,activeIcon: AssetsRes.tabVideoSel,inactiveBlackIcon: AssetsRes.tabVideoUnSel,inactiveWhiteIcon: AssetsRes.tabVideoUnSelWhite),
    BotNavItemInfo(name: LanStrings.tabMessages,activeIcon: AssetsRes.tabMessageSel,inactiveBlackIcon: AssetsRes.tabMessageUnSel,inactiveWhiteIcon: AssetsRes.tabMyUnSelWhite),
    BotNavItemInfo(name: LanStrings.tabMy,activeIcon: AssetsRes.tabMySel,inactiveBlackIcon: AssetsRes.tabMyUnSel,inactiveWhiteIcon: AssetsRes.tabMyUnSelWhite),
  ];

  static Future<void> reqNavTabInfo() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getNavTabInfo();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess || mapResult["data"] == null) {
      return;
    }
    List<BotNavItemInfo> listDataTemp = [];
    List tabList = mapResult["data"] ?? [];
    for( var item in tabList ) {
      BotNavItemInfo itemInfo = BotNavItemInfo.fromJson(item);
      listDataTemp.add(itemInfo);
    }

    if( listDataTemp.isNotEmpty ) {
      listNavTabInfo.clear();
      listNavTabInfo.addAll(listDataTemp);
    }

    for( int nIndex = 0;nIndex < listNavTabInfo.length;nIndex++) {
      BotNavItemInfo itemInfo = listNavTabInfo[nIndex];
      String activeUrl = itemInfo.activeIcon;
      if (activeUrl.isNotEmpty && ToolsUtil.isNet(activeUrl)) {
        ImagePreloadManager().recordUrl(activeUrl);
      }
      String activeUrlWhite = itemInfo.inactiveWhiteIcon;
      if (activeUrlWhite.isNotEmpty && ToolsUtil.isNet(activeUrlWhite)) {
        ImagePreloadManager().recordUrl(activeUrlWhite);
      }
      String activeUrlBlack = itemInfo.inactiveBlackIcon;
      if (activeUrlBlack.isNotEmpty && ToolsUtil.isNet(activeUrlBlack)) {
        ImagePreloadManager().recordUrl(activeUrlBlack);
      }
    }
  }
}