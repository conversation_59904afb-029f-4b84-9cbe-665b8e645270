import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:platform_device_id_plus/platform_device_id.dart';

class SystemInfo {
  static final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  ///初始化设备和相应的包信息，
  ///这个要放在sputil的后面
  static Future<void> initInfo() async {
    await _initPackageInfo();
    await _initDevInfo();
  }

  ///初始化包信息
  static Future<void> _initPackageInfo() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    String appName = packageInfo.appName;
    StorageUtil.set(SPKey.appName, appName);

    String packageName = packageInfo.packageName;
    StorageUtil.set(SPKey.packageName, packageName);

    String version = packageInfo.version;
    StorageUtil.set(SPKey.keyVersion, version);

    String buildNumber = packageInfo.buildNumber;
    StorageUtil.set(SPKey.buildNumber, buildNumber);
  }

  ///初始化设备信息
  static Future<void> _initDevInfo() async {
    Map<String, dynamic> deviceData;
    String strDevId = "";
    String strBrand = "";
    String strModel = "";
    String strOSVer = "";
    bool isPhysicalDevice = false;
    String system = "";
    strDevId = await PlatformDeviceId.getDeviceId ?? "";
    try {
      if (GetPlatform.isAndroid) {
        StorageUtil.set(SPKey.keyPlatForm, 'ar');
        deviceData = _readAndroidBuildData(await deviceInfoPlugin.androidInfo);

        strBrand = deviceData['brand']; //比如 Xiaomi Mi Note 3
        strModel = deviceData['model'];
        strOSVer = deviceData['version.release'];
        isPhysicalDevice = deviceData['isPhysicalDevice'];

        int sdkInt = deviceData['version.sdkInt'];
        StorageUtil.set(SPKey.keySdkInt, sdkInt);
      } else if (GetPlatform.isIOS) {
        StorageUtil.set(SPKey.keyPlatForm, 'ip');
        deviceData = _readIosDeviceInfo(await deviceInfoPlugin.iosInfo);
        strDevId = deviceData['identifierForVendor'];
        // TODO 如果是iOS 需要区分型号
        strModel = deviceData['utsname.machine:']; //例如 iPhone9,2
        strModel = ToolsUtil.getModel(strModel);
        var systemVersion = deviceData['systemVersion'];
        isPhysicalDevice = deviceData['isPhysicalDevice'];
        strOSVer = systemVersion;
        StorageUtil.set('systemVersion', systemVersion);
        strBrand = deviceData["model"] ?? "unknown";
      }
    } catch (except) {
      deviceData = <String, dynamic>{
        'Error:': 'Failed to get platform version.'
      };
    }

    if (strDevId.isEmpty) {
      strDevId = ToolsUtil().genUuId();
    }

    StorageUtil.set(SPKey.keyDevId, strDevId);
    StorageUtil.set(SPKey.keyBrand, strBrand);
    StorageUtil.set(SPKey.keyModel, strModel);
    StorageUtil.set(SPKey.keySysVersion, strOSVer);

    StorageUtil.set(SPKey.isPhysicalDevice, isPhysicalDevice);
  }

  static Map<String, dynamic> _readAndroidBuildData(AndroidDeviceInfo build) {
    return <String, dynamic>{
      'version.securityPatch': build.version.securityPatch,
      'version.sdkInt': build.version.sdkInt,
      'version.release': build.version.release,
      'version.previewSdkInt': build.version.previewSdkInt,
      'version.incremental': build.version.incremental,
      'version.codename': build.version.codename,
      'version.baseOS': build.version.baseOS,
      'board': build.board,
      'bootloader': build.bootloader,
      'brand': build.brand,
      'device': build.device,
      'display': build.display,
      'fingerprint': build.fingerprint,
      'hardware': build.hardware,
      'host': build.host,
      'id': build.id,
      'manufacturer': build.manufacturer,
      'model': build.model,
      'product': build.product,
      'supported32BitAbis': build.supported32BitAbis,
      'supported64BitAbis': build.supported64BitAbis,
      'supportedAbis': build.supportedAbis,
      'tags': build.tags,
      'type': build.type,
      'isPhysicalDevice': build.isPhysicalDevice,
      'androidId': build.serialNumber,
      'systemFeatures': build.systemFeatures,
    };
  }

  static Map<String, dynamic> _readIosDeviceInfo(IosDeviceInfo data) {
    return <String, dynamic>{
      'name': data.name,
      'systemName': data.systemName,
      'systemVersion': data.systemVersion,
      'model': data.model,
      'localizedModel': data.localizedModel,
      'identifierForVendor': data.identifierForVendor,
      'isPhysicalDevice': data.isPhysicalDevice,
      'utsname.sysname:': data.utsname.sysname,
      'utsname.nodename:': data.utsname.nodename,
      'utsname.release:': data.utsname.release,
      'utsname.version:': data.utsname.version,
      'utsname.machine:': data.utsname.machine,
    };
  }
}
