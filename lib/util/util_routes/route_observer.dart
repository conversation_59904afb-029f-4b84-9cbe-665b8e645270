
import 'package:flutter/material.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/util/util_routes/route_manager.dart';
class SubRouteObserver extends NavigatorObserver {


  @override
  void didPop(Route route, Route? previousRoute) {
    if (route.settings.name != null) {
      RouteUtil.routeTrace.remove(route.settings.name);
      RouteUtil.routeObj.remove(route);
    }

    commonEventBus.fire(RouteChangeEvent(strRouteCurName: route.settings.name ?? "", strRoutePre: previousRoute?.settings.name ?? ""));
    super.didPop(route, previousRoute);
  }

  @override
  void didPush(Route route, Route? previousRoute) {
    if (route.settings.name != null) {
      RouteUtil.routeTrace.insert(0, route.settings.name ?? "");
      RouteUtil.routeObj.insert(0, route);
      pushRouteManage(route.settings.name ?? "", previousRoute?.settings?.name ?? "");
    }

    debugPrint("___________________+++ push ${route.settings.name}");
    super.didPush(route, previousRoute);
  }

  Future<void> pushRouteManage(String routeName,String strPre) async {
    commonEventBus.fire(RouteChangeEvent(strRouteCurName: routeName, strRoutePre: strPre));
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    if (route.settings.name != null) {
      RouteUtil.routeTrace.remove(route.settings.name);
      RouteUtil.routeObj.remove(route);
    }
    commonEventBus.fire(RouteChangeEvent(strRouteCurName: route.settings.name ?? "", strRoutePre: previousRoute?.settings.name ?? ""));
    super.didRemove(route, previousRoute);
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    int nIndexFind = RouteUtil.routeTrace.indexWhere((element) => element == (oldRoute?.settings.name ?? "" ));
    if (nIndexFind >= 0) {
      RouteUtil.routeTrace[nIndexFind] = newRoute?.settings.name ?? "";
    }

    nIndexFind = RouteUtil.routeObj.indexWhere((element) => element == oldRoute);
    if (nIndexFind >= 0 && newRoute != null) {
      RouteUtil.routeObj[nIndexFind] = newRoute;
    }

    commonEventBus.fire(RouteChangeEvent(strRouteCurName: newRoute?.settings.name ?? "", strRoutePre: oldRoute?.settings.name ?? ""));
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }
}