import 'package:flutter/material.dart';
import 'package:flutter_common_base/func_module/splash_route/splash_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/tab_fuli_view.dart';

import 'package:flutter_common_base/func_module/tabs_manage/tab_manage_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_messages/tab_message_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/tab_my_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_recommend/tab_recommend_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/tab_videos_view.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';

class AppPages {
  AppPages._();
  static const initial = Routes.initRoutePage;

  /// Flutter原生路由生成器
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case Routes.initRoutePage:
        return MaterialPageRoute(
          builder: (_) => const InitRouteSplashPage(),
          settings: settings,
        );

      case Routes.homeMain:
        return MaterialPageRoute(
          builder: (_) => const TabsManageView(),
          settings: settings,
        );

      case Routes.tabRecommend:
        return MaterialPageRoute(
          builder: (_) => const TabRecommendPage(),
          settings: settings,
        );

      case Routes.tabVideos:
        return MaterialPageRoute(
          builder: (_) => const TabVideosPage(),
          settings: settings,
        );

      case Routes.tabMsg:
        return MaterialPageRoute(
          builder: (_) => const TabMessageView(),
          settings: settings,
        );

      case Routes.tabMy:
        return MaterialPageRoute(
          builder: (_) => const TabMyView(),
          settings: settings,
        );

      case Routes.tabFuLi:
        return MaterialPageRoute(
          builder: (_) => const TabFuLiPage(),
          settings: settings,
        );

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('页面未找到')),
            body: const Center(child: Text('404 - 页面未找到')),
          ),
          settings: settings,
        );
    }
  }

  /// 路由映射表，用于页面构建器
  static final Map<String, WidgetBuilder> routes = {
    Routes.initRoutePage: (_) => const InitRouteSplashPage(),
    Routes.homeMain: (_) => const TabsManageView(),
    Routes.tabRecommend: (_) => const TabRecommendPage(),
    Routes.tabVideos: (_) => const TabVideosPage(),
    Routes.tabMsg: (_) => const TabMessageView(),
    Routes.tabMy: (_) => const TabMyView(),
    Routes.tabFuLi: (_) => const TabFuLiPage(),
  };
}
