import 'package:flutter_common_base/func_module/splash_route/splash_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/tab_fuli_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_manage_binds.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_manage_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_messages/tab_message_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/tab_my_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_recommend/tab_recommend_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/tab_videos_view.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:get/get.dart';

class AppPages {
  AppPages._();
  static const initial = Routes.initRoutePage;
  static final routes = [
    ///路由页面
    GetPage(
      name: Routes.initRoutePage,
      page: () => const InitRouteSplashPage(),
    ),

    ///主页
    GetPage(
      name: Routes.homeMain,
      page: () => const TabsManageView(),
      binding: TabsManageBinding()
    ),

    ///推荐
    GetPage(
      name: Routes.tabRecommend,
      page: () => const TabRecommendPage(),
      binding: TabRecommendBinding()
    ),

    ///短剧
    GetPage(
      name: Routes.tabVideos,
      page: () => const TabVideosPage(),
      binding: TabVideosBinding()
    ),

    ///消息
    GetPage(
      name: Routes.tabMsg,
      page: () => const TabMessageView(),
      binding: TabMessageBinding()
    ),

    ///我的
    GetPage(
      name: Routes.tabMy,
      page: () => const TabMyView(),
      binding: TabMyBinding()
    ),

    ///福利
    GetPage(
        name: Routes.tabFuLi,
        page: () => const TabFuLiPage(),
        binding: TabFuLiBinding()
    ),

  ];
}
