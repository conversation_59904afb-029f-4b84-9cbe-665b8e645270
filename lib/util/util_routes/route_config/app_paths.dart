
class Routes {
  ///初始导航路由页面
  static const initRoutePage = _Paths.initRoutePage;
  ///主页
  static const homeMain = _Paths.homeMain;

  static const tabRecommend = _Paths.tabRecommend;
  static const tabVideos = _Paths.tabVideos;
  static const tabMsg = _Paths.tabMsg;
  static const tabMy = _Paths.tabMy;
  static const tabFuLi = _Paths.tabFuLi;
  static const routeUnLockDlg = _Paths.routeUnLockDlg;
  static const routeLoginMain = "/loginMain";

}

class _Paths {
  static const initRoutePage = "/initRoute";
  static const homeMain = '/tabsMain';
  static const tabRecommend = '/tabRecommend';
  static const tabVideos = '/tabVideos';
  static const tabMsg = '/tabMsg';
  static const tabMy = '/tabMy';
  static const tabFuLi = '/tabFuLi';
  static const routeUnLockDlg = "/unLockDlg";
}
