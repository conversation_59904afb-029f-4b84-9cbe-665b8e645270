import 'package:flutter/material.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_pages.dart';

/// Flutter原生路由管理器
/// 替代GetX的导航功能，提供相同的API接口
class NativeRouteManager {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  /// 获取当前导航器
  static NavigatorState? get navigator => navigatorKey.currentState;
  
  /// 获取当前上下文
  static BuildContext? get context => navigatorKey.currentContext;
  
  /// 获取当前路由名称
  static String? get currentRoute {
    String? currentRouteName;
    navigator?.popUntil((route) {
      currentRouteName = route.settings.name;
      return true;
    });
    return currentRouteName;
  }
  
  /// 跳转到指定页面 (替代 Get.to)
  static Future<T?> to<T extends Object?>(
    Widget page, {
    String? routeName,
    Object? arguments,
    Duration? duration,
  }) async {
    if (navigator == null) return null;
    
    return await navigator!.push<T>(
      MaterialPageRoute(
        builder: (_) => page,
        settings: RouteSettings(
          name: routeName,
          arguments: arguments,
        ),
      ),
    );
  }
  
  /// 跳转到命名路由 (替代 Get.toNamed)
  static Future<T?> toNamed<T extends Object?>(
    String routeName, {
    Object? arguments,
  }) async {
    if (navigator == null) return null;
    
    return await navigator!.pushNamed<T>(
      routeName,
      arguments: arguments,
    );
  }
  
  /// 替换当前页面 (替代 Get.off)
  static Future<T?> off<T extends Object?, TO extends Object?>(
    Widget page, {
    String? routeName,
    Object? arguments,
    TO? result,
  }) async {
    if (navigator == null) return null;
    
    return await navigator!.pushReplacement<T, TO>(
      MaterialPageRoute(
        builder: (_) => page,
        settings: RouteSettings(
          name: routeName,
          arguments: arguments,
        ),
      ),
      result: result,
    );
  }
  
  /// 替换当前页面到命名路由 (替代 Get.offNamed)
  static Future<T?> offNamed<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    TO? result,
  }) async {
    if (navigator == null) return null;
    
    return await navigator!.pushReplacementNamed<T, TO>(
      routeName,
      arguments: arguments,
      result: result,
    );
  }
  
  /// 清空导航栈并跳转 (替代 Get.offAll)
  static Future<T?> offAll<T extends Object?>(
    Widget page, {
    String? routeName,
    Object? arguments,
    bool Function(Route<dynamic>)? predicate,
  }) async {
    if (navigator == null) return null;
    
    return await navigator!.pushAndRemoveUntil<T>(
      MaterialPageRoute(
        builder: (_) => page,
        settings: RouteSettings(
          name: routeName,
          arguments: arguments,
        ),
      ),
      predicate ?? (route) => false,
    );
  }
  
  /// 清空导航栈并跳转到命名路由 (替代 Get.offAllNamed)
  static Future<T?> offAllNamed<T extends Object?>(
    String routeName, {
    Object? arguments,
    bool Function(Route<dynamic>)? predicate,
  }) async {
    if (navigator == null) return null;
    
    return await navigator!.pushNamedAndRemoveUntil<T>(
      routeName,
      predicate ?? (route) => false,
      arguments: arguments,
    );
  }
  
  /// 返回上一页 (替代 Get.back)
  static void back<T extends Object?>([T? result]) {
    if (navigator?.canPop() == true) {
      navigator!.pop<T>(result);
    }
  }
  
  /// 返回到指定页面 (替代 Get.until)
  static void until(bool Function(Route<dynamic>) predicate) {
    navigator?.popUntil(predicate);
  }
  
  /// 显示对话框 (替代 Get.dialog)
  static Future<T?> dialog<T>(
    Widget dialog, {
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) async {
    if (context == null) return null;
    
    return await showDialog<T>(
      context: context!,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
      builder: (_) => dialog,
    );
  }
  
  /// 显示底部弹窗 (替代 Get.bottomSheet)
  static Future<T?> bottomSheet<T>(
    Widget bottomSheet, {
    Color? backgroundColor,
    double? elevation,
    bool isDismissible = true,
    bool enableDrag = true,
  }) async {
    if (context == null) return null;
    
    return await showModalBottomSheet<T>(
      context: context!,
      backgroundColor: backgroundColor,
      elevation: elevation,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      builder: (_) => bottomSheet,
    );
  }
  
  /// 显示Snackbar (替代 Get.snackbar)
  static void snackbar(
    String title,
    String message, {
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
    Color? backgroundColor,
  }) {
    if (context == null) return;
    
    ScaffoldMessenger.of(context!).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
            Text(message),
          ],
        ),
        duration: duration,
        action: action,
        backgroundColor: backgroundColor,
      ),
    );
  }
}
