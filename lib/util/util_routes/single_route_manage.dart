import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/data_model/search/search_info.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/func_module/ad_union/drama_detail.dart';
import 'package:flutter_common_base/func_module/ad_union/self_define_page/self_define_detail_page.dart';
import 'package:flutter_common_base/func_module/login/account_bind.dart';
import 'package:flutter_common_base/func_module/login/login_main_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/bill_detail_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/bill_balance/withdraw_detail_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_fuli/yu_money_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_manage_controller.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/edit_info/edit_area/edit_area_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/edit_info/edit_info_main_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/edit_info/edit_text_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/about_us_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/account_safe_page_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/font_set_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/modify_phone_page_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/setting_page_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/wallet/waller_detail_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_my/settings/withdraw_account/withdraw_account_main_page.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/ranking_list_detail/hot_rank_list_view.dart';
import 'package:flutter_common_base/func_module/tabs_manage/tab_videos/search/search_main_view/search_page_main_view.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/sel_date_picker.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:flutter_common_base/util/util_routes/route_manager.dart';
import 'package:flutter_common_base/util/util_withdraw_data.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/router_report.dart';

class SingleRoutePageManage {
  ///跳转到主页
  static Future<void> routeMainPage(String strTabName) async {
    /// 清空导航栈，跳转到主页面（TabsManageView）
    Get.until((route) => Get.currentRoute == Routes.homeMain);

    /// 延迟确保页面加载完成后跳转 tab
    Future.delayed(const Duration(milliseconds: 50), () {
      final controller = Get.find<TabsManageController>();
      controller.jumpToTabName(strTabName);
    });
  }

  ///路由到设置页面
  static Future<dynamic> routeToSettingPage(
      {Map<String, dynamic> arguments = const <String, dynamic>{}}) async {
    return await Get.to(() => SettingPageView(
          isGlobal: false,
          tag: ToolsUtil().genUuId(),
          mapParam: arguments,
        ));
  }

  ///路由到登录页面
  static Future<dynamic> routeToLoginMain(
      {Map<String, dynamic> arguments = const <String, dynamic>{}}) async {
    return await Get.to(() => const LoginMainView(isGlobal: true),
        routeName: Routes.routeLoginMain);
  }

  ///路由到编辑个人资料页面
  static Future<dynamic> routeToEditProfilePage(
      {Map<String, dynamic> arguments = const <String, dynamic>{}}) async {
    return await Get.to(
        () => EditInfoMainPage(
              isGlobal: false,
              tag: ToolsUtil().genUuId(),
              mapParam: arguments,
            ),
        arguments: arguments);
  }

  ///路由到编辑昵称或简介页面
  static Future<dynamic> routeToEditTextPage(
      {Map<String, dynamic> arguments = const <String, dynamic>{}}) async {
    return await Get.to(
        () => EditTextPage(
              isGlobal: false,
              tag: ToolsUtil().genUuId(),
              mapParam: arguments,
            ),
        arguments: arguments);
  }

  ///跳转到时间选取页面
  static Future<dynamic> routeDateTimePickerPage({List<String>? parts}) async {
    return await Navigator.of(Get.context!).push(PageRouteBuilder(
      opaque: false,
      barrierColor: Colors.black54,
      pageBuilder: (context, animation, secondaryAnimation) {
        return selectDate(parts: parts);
      },
      transitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;
        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);
        return SlideTransition(position: offsetAnimation, child: child);
      },
    ));
  }

  ///跳转到编辑手机号页面
  ///路由到编辑昵称或简介页面
  static Future<dynamic> routeToAccountSafePage(
      {Map<String, dynamic> arguments = const <String, dynamic>{}}) async {
    return await Get.to(
        () => AccountSafePageView(
              isGlobal: false,
              tag: ToolsUtil().genUuId(),
              mapParam: arguments,
            ),
        arguments: arguments);
  }

  ///跳转到编辑手机号 发送验证码页面
  static Future<dynamic> routeToModifyPhoneVerifyCode(
      {Map<String, dynamic> arguments = const <String, dynamic>{}}) async {
    return await Get.to(
        () => ModifyPhonePageView(
              isGlobal: false,
              tag: ToolsUtil().genUuId(),
              mapParam: arguments,
            ),
        arguments: arguments);
  }

  ///关于页面
  static Future<void> routeToAboutUs() async {
    return Get.to(() => AboutUsPage());
  }

  ///字体大小设置页面
  static Future<dynamic> routeToFontSizeSettingPage() async {
    await Get.to(() => FontPreviewPage());
  }

  ///跳转到地区选择页面
  static Future<dynamic> routeToAreaInfo() async {
    return await Get.to(
        () => AreaInfoPage(isGlobal: false, tag: ToolsUtil().genUuId()));
  }

  ///跳转到账号注销页面
  static Future<dynamic> routeToAccountWithdraw() async {
    return await Get.to(() =>
        WithdrawAccountMainPage(isGlobal: false, tag: ToolsUtil().genUuId()));
  }

  ///跳转到搜索页面
  static Future<dynamic> routeToSearchMainPage() async {
    return await Get.to(() => SearchPageMainView(
          searchInfo: SearchInfo(
              searchType: SearchType.eDefaultPage, hintText: "请输入搜索内容"),
        ));
  }

  ///跳转到搜索页面
  static Future<dynamic> routeToSearchHotList() async {
    return await Get.to(() => const HotRankListView());
  }

  ///跳转到播放详情页
  static Future<dynamic> routeDramaDetailPage(Drama detail,
      {bool bCollectDrama = false}) async {
    // bool bCollect = await ApiReqInterface.queryDramaFavStateByParam(detail.id);
    // bCollectDrama = bCollect;
    if (RouteUtil.routeTrace.contains(BizStrings.dramaDetailPageRoute)) {
      return await Get.offUntil(
          GetPageRoute(
              page: () => DramaDetail(
                    drama: detail,
                    bCollect: bCollectDrama,
                  ),
              settings:
                  const RouteSettings(name: BizStrings.dramaDetailPageRoute)),
          (route) => route.settings.name == Routes.homeMain || route.isFirst);
    }
    return await Get.to(
        () => DramaDetail(
              drama: detail,
              bCollect: bCollectDrama,
            ),
        routeName: BizStrings.dramaDetailPageRoute);
  }

  ///转换一下
  static Future<dynamic> routeDramaDetailPageVideoData(VideoData itemInfo,
      {bool bCollect = false}) async {
    late Drama detail;
    List<Drama> reqDrama = await FlutterAdcontent.requestDrama(
        [ToolsUtil.safeParseInt(itemInfo.strSourceId)]);
    if (reqDrama.isNotEmpty) {
      detail = reqDrama[0];
    } else {
      detail = Drama(
        id: ToolsUtil.safeParseInt(itemInfo.strSourceId),
        title: itemInfo.strName,
        coverImage: itemInfo.strCover,
        status: itemInfo.nStatus,
        total: itemInfo.nTotalEpisode,
        index: 2,
        type: itemInfo.videoCat.strName,
        typeId: itemInfo.videoCat.id,
        desc: itemInfo.strDescription,
        isPotential: itemInfo.bPotential,
        // tags: [itemInfo.videoCat.strName],
      );
    }

    if (RouteUtil.routeTrace.contains(BizStrings.dramaDetailPageRoute)) {
      return await Get.offUntil(
          GetPageRoute(
              page: () => DramaDetail(
                    drama: detail,
                    bCollect: bCollect,
                  ),
              settings:
                  const RouteSettings(name: BizStrings.dramaDetailPageRoute)),
          (route) => route.settings.name == Routes.homeMain || route.isFirst);
    }

    return await routeDramaDetailPage(detail, bCollectDrama: bCollect);
  }

  ///跳转到自定义详情页面
  static Future<dynamic> routeToSelfDefineDramaDetail(Drama detail) async {
    return await Get.to(
        () => SelfDefineDetailPage(isGlobal: false, tag: ToolsUtil().genUuId()),
        arguments: {"drama": detail},
        duration: const Duration(milliseconds: 100),
        routeName: BizStrings.dramaSelfDetailPageRoute);
  }

  ///去到余额页面
  static Future<dynamic> routeToBalanceDetailPage(
      {double nowBill = 0.0, List<String> listTips = const []}) async {
    return await Get.to(
        () => YuMoneyView(isGlobal: false, tag: ToolsUtil().genUuId()),
        routeName: BizStrings.yuEPageRoute,
        arguments: {"balance": nowBill, "tips": listTips});
  }

  ///路由到钱包页面
  static Future<dynamic> routeToWallerPage() async {
    return await Get.to(
        () => WallerDetailPage(isGlobal: false, tag: ToolsUtil().genUuId()));
  }

  ///去到提现详情页面
  static Future<void> routeToWithdrawDetail() async {
    return await Get.to(
        () => WithdrawDetailPage(
              isGlobal: false,
              tag: ToolsUtil().genUuId(),
            ),
        arguments: {"types": WithdrawInfoData().listType});
  }

  ///去到账单详情详情页
  static Future<void> routeBillDetailView() async {
    return await Get.to(
        () => BillDetailView(isGlobal: false, tag: ToolsUtil().genUuId()));
  }

  ///跳转到绑定成功提示的页面
  static Future<void> routeToBindSuccessTipPage() async {
    return await Get.dialog(const AccountBindPage());
  }
}
