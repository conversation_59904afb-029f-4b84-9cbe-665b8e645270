import 'package:flutter_common_base/data_model/ad/ad_config.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/uitl_login/login_util.dart';
import 'package:flutter_common_base/util/util_common_widget/image_preload_util.dart';
import 'package:flutter_common_base/util/util_csj/csj_union_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_respcode_filter/resp_code_filter.dart';
import 'package:flutter_common_base/util/util_sys_info/sys_info.dart';
import 'package:flutter_common_base/util/util_tab_dyn_info/dynamic_tab_info.dart';
import 'package:get/get.dart';

class ServiceInit {
  static Future<void> init() async {

    ///初始化设备信息
    await SystemInfo.initInfo();

    ///返回码状态监听
    RespCodeFilter.init();

    ///获取广告配置
    await getAdSettings();

    ///初始化穿山甲
    await CsjUnionUtil.init();

    ///预加载图片
    _preloadCachedImg();

    ///初始化用户信息
    // LoginInfoUtil.initLoginData();
    ///初始化信息，包括设备信息和包本身的信息
    ///获取系统导航
    await DynamicTabInfo.reqNavTabInfo();
    ///系统初始化
    ApiReqInterface.sysStartUp();
    ///初始微信
    LoginUtil().regWXSdk();
    ///初始化网络监听
    // await NetWorkStatus.init();
    ///加载时区
    // loadLocalTimZoneData();

    ///初始化监听
    // await WebSocketService().initData();
  }


  static Future<void> getAdSettings() async {
    Map<String,dynamic> mapResult = await ApiReqInterface.getAdConfig();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    CsjUnionUtil.adSettings = VideoContentModel.fromJson(mapResult["data"] ?? {});
  }

  static Future<void> _preloadCachedImg() async {
    final preloadManager = ImagePreloadManager();
    await preloadManager.restoreFromStorage(); /// 恢复 URL 列表
    await preloadManager.preloadAll(Get.context!, maxConcurrent: 4); // 开始预加载
  }
}
