import 'package:wakelock_plus/wakelock_plus.dart';

class ForGroundServiceUtil {
  static Future<void> initService() async {
    ///初始化
    await _initService();
  }

  ///初始化
  static Future<void> _initService() async {
  }

  static Future<void> startService() async {

  }

  static Future<void> wakeLockOn() async {
    await WakelockPlus.enable();
  }

  static Future<void> wakeLockOff() async {
    await WakelockPlus.disable();
  }
}
