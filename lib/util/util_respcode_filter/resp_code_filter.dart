import 'dart:async';
import 'dart:convert';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/user_info/login_type_info.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/util_dio/dio_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:flutter_common_base/util/util_routes/route_manager.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';

class RespCodeFilter {
  static StreamSubscription? eventResponse;
  static void init() {
    eventResponse = commonEventBus.on<FilterRespEvent>().listen(_responseCodeAction);
  }

  static void unInit() {
    eventResponse?.cancel();
  }

  static Future<void> _responseCodeAction(FilterRespEvent resp) async {
    int nStatusCode = resp.response.statusCode ?? 0;
    if (nStatusCode < 200 || nStatusCode > 300) {
      ToastUtil.showToast(resp.response.statusMessage ?? "");
      return;
    }

    BaseResult result = BaseResult.fromMap(json.decode(resp.response.data));
    if ((result.code ?? -1) > 0) {
      ToastUtil.showToast(result.strMsg);
    }

    if (result.code == ResponseCodeParse.codeNeedBindPhone) {
      SingleRoutePageManage.routeToModifyPhoneVerifyCode(arguments: {"fromSrc": PageFromReason.fromReasonBind});
      return;
    }

    if( result.code != ResponseCodeParse.codeNeedLogin) {
      return;
    }

    ///获取用户信息不拦截
    if (resp.response.requestOptions.path.contains(ApiReqUrl.userInfo)) {
      return;
    }

    if (RouteUtil.routeTrace.contains(Routes.routeLoginMain)) {
      return;
    }

    /// 需要弹登录页面
    SingleRoutePageManage.routeToLoginMain();
  }
}
