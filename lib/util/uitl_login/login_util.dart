import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/data_model/user_info/login_type_info.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:fluwx/fluwx.dart';

  class LoginUtil {

  static final LoginUtil _instance = LoginUtil._internal();
  factory LoginUtil() {
    return _instance;
  }
  LoginUtil._internal();

  List<ThirdLoginTypeInfo> thdLoginTypeInfo = [];

  ///微信实例
  Fluwx fluWx = Fluwx();
  ///微信sdk是否注册成功
  bool bRegSuccess = false;

  Future<void> getLoginTypeInfo() async {
    Map<String,dynamic> mapResult = await ApiReqInterface.getThirdLoginType();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    List logItem = mapResult["data"] ?? [];
    thdLoginTypeInfo = [];

    for(var item in logItem) {
      ThirdLoginTypeInfo loginTypeInfo = ThirdLoginTypeInfo.fromJson(item);
      thdLoginTypeInfo.add(loginTypeInfo);
    }
  }

  ///注册微信sdk
  Future<void> regWXSdk() async {
    bRegSuccess = await fluWx.registerApi(appId: BizStrings.wxSdkAppId,universalLink: BizStrings.strIOSUniLink);
    debugPrint("微信初始化: $bRegSuccess");
  }
}
