import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/custom_button_view.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_toast/toast_util.dart';
import 'package:flutter_common_base/util/util_update/update_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';


class UpDateUtil {
  ///默认检查更新时间
  static int defaultInterval = 1000 * 60 * 60 * 24 * 7;
  static int interInMilliseconds = defaultInterval;
  static Future<void> getUpdateInfo({String strSrc = CheckUpdateSrc.updateLunch}) async {
    ///如果是强制升级会跑到统一的状态码拦截处
    Map<String, dynamic> mapResult = await ApiReqInterface.getUpdateVersion();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess || mapResult['data'] == null) {
      if( strSrc == CheckUpdateSrc.updateSetting ) {
        ToastUtil.showToast("已是最新版本");
      }
      return;
    }

    ///解析数据
    UpdateInfo updateInfoCurrent = UpdateInfo.fromJson(mapResult["data"]["current"] ?? {});
    UpdateInfo updateInfoLatest = UpdateInfo.fromJson(mapResult["data"]["latest"] ?? {});

    ///动态检查更新时间
    interInMilliseconds = mapResult["data"]["interval"] ?? defaultInterval;

    ///强制更新
    if( updateInfoCurrent.forceUpdate ) {
      routeToUpdate(bForceUpdate: true,strUrlLink: GetPlatform.isAndroid ? updateInfoLatest.apkURL : updateInfoLatest.ipaURL,strDesc: updateInfoLatest.description);
      return;
    }
    ///当前版本
    String strLocalVersion = updateInfoCurrent.code;
    ///最新版本
    String strRemote = updateInfoLatest.code;
    int nVerUpdate = ToolsUtil.compareVersion(strLocalVersion, strRemote);
    ///本地版本大于服务器返回的版本则不需要升级
    if( nVerUpdate >= 0 ) {
      if( strSrc == CheckUpdateSrc.updateSetting ) {
        ToastUtil.showToast("已是最新版本");
      }
      return;
    }

    if (strSrc == CheckUpdateSrc.updateSetting) {
      routeToUpdate(bForceUpdate: false, strUrlLink: GetPlatform.isAndroid ? updateInfoLatest.apkURL : updateInfoLatest.ipaURL, strDesc: updateInfoLatest.description);
      return;
    }

    ///需要升级
    ///并且并非强制升级
    int nNowMilliseconds = DateTime.now().millisecondsSinceEpoch;
    int nLastTipTime = StorageUtil.get<int>(SPKey.keyUpdateTip);
    if (nLastTipTime <= 0 || ((nNowMilliseconds - nLastTipTime) > interInMilliseconds)) {
      StorageUtil.set<int>(SPKey.keyUpdateTip, nNowMilliseconds);
      routeToUpdate(bForceUpdate: false, strUrlLink: GetPlatform.isAndroid ? updateInfoLatest.apkURL : updateInfoLatest.ipaURL, strDesc: updateInfoLatest.description);
      return;
    }
  }

  static Future<void> routeToUpdate({bool bForceUpdate = false, String strUrlLink = "",String strDesc = ""}) async {
    return Get.dialog(
        UpdateDialogPage(strTip: strDesc, strPackageUrl: strUrlLink, bForceUpdate: bForceUpdate),
      barrierDismissible: !bForceUpdate
    );
  }

  static Widget updateBtnOperate({bool bForceUpdate = false,String strUrlLink = ""}) {
    return SizedBox(
      height: 48.h,
      width: 1.sw,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          bForceUpdate ? const SizedBox.shrink() : CustomButtonView(text: "暂不更新",width: 113, height:41,disableTextColor:BizColors.rgb10ffffff,onTap: () {Get.back();},bIsDefault: false,),
          bForceUpdate ? const SizedBox.shrink() : SizedBox(width: 12.w),
          CustomButtonView(text: "立即更新", width: 113, height:41,onTap: () {Get.back();},),
        ],
      ),
    );
  }
}


class CheckUpdateSrc {
  static const String updateLunch = "appStart";
  static const String updateSetting = "settingCheck";
}