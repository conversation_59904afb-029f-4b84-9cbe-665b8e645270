

import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/util_dio/dio_interceptors.dart';
import 'package:flutter_common_base/util/util_dio/dio_method.dart';
import 'package:flutter_common_base/util/util_dio/dio_transformer.dart';

class BaseResult<T> {
  int? code = -1;
  T? data;
  String strMsg = "";

  BaseResult({this.code,this.data,this.strMsg = ""});

  static BaseResult fromMap(Map<String, dynamic>? map) {
    BaseResult result = BaseResult();
    result.code = map?['code'] ?? -1;
    result.strMsg = map?["msg"] ?? "";
    result.data = map?['data'];
    return result;
  }

  Map<String, dynamic> toJson() => {
    "code": code,
    "msg" : strMsg,
    "data": data,
  };
}

class DioUtil {
  /// 连接超时时间
  static const int connectTimeout = 16 * 1000;

  /// 响应超时时间
  static const int receiveTimeout = 16 * 1000;

  /// 是否开启网络缓存,默认false
  static bool cacheEnable = false;

  /// 最大缓存时间(按秒), 默认缓存七天,可自行调节
  static int maxCacheAge = 7 * 24 * 60 * 60;

  /// 最大缓存条数(默认一百条)
  static int maxCacheCount = 100;

  Dio _dio = Dio();
  Dio get dio => _dio;

  DioUtil({String baseUrl = ApiReqUrl.baseUrlPassport, Map<String, dynamic>? headers, String contentType = "application/json",String proxy = ""}) {
    /// 初始化基本选项
    BaseOptions options = BaseOptions(
      /// 请求基地址,可以包含子路径，如: "https://www.google.com/api/".
      baseUrl: baseUrl,

      /// 连接服务器超时时间，单位是毫秒.
      connectTimeout: const Duration(milliseconds: connectTimeout),

      /// 接收数据的总时限.
      receiveTimeout: const Duration(milliseconds: receiveTimeout),

      /// [表示期望以那种格式(方式)接受响应数据。接受四种类型 `json`, `stream`, `plain`, `bytes`. 默认值是 `json`](https://github.com/flutterchina/dio/issues/30)
      responseType: ResponseType.plain,

      contentType: contentType,

      /// Http请求头.
      headers: headers ?? {"api-ver": "application/json"},
    );

    /// 初始化dio
    _dio = Dio(options);

    /// 添加拦截器
    _dio.interceptors.add(DioInterceptors());

    /// 添加转换器
    _dio.transformer = DioTransformer();

    /// 添加cookie管理器
    // _dio.interceptors.add(CookieManager(cookieJar));

    /// 刷新token拦截器(lock/unlock)
    // _dio.interceptors.add(DioTokenInterceptors());

    /// 添加缓存拦截器
    // _dio.interceptors.add(DioCacheInterceptors());
  }

  /// 取消请求token
  final CancelToken _cancelToken = CancelToken();

  /// cookie
  // CookieJar cookieJar = CookieJar();

  /// 请求类
  Future<Response> request<T>(
      String path, {
        DioReqMethod method = DioReqMethod.get,
        Map<String, dynamic>? params,
        data,
        CancelToken? cancelToken,
        Options? options,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress,
      }) async {
    const methodValues = {
      DioReqMethod.get: 'get',
      DioReqMethod.post: 'post',
      DioReqMethod.put: 'put',
      DioReqMethod.delete: 'delete',
      DioReqMethod.patch: 'patch',
      DioReqMethod.head: 'head'
    };

    options ??= Options(method: methodValues[method]);
    Response? response;
    try {
      response = await _dio.request(path,
          data: data,
          queryParameters: params,
          cancelToken: cancelToken ?? _cancelToken,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
      commonEventBus.fire(FilterRespEvent(response: response));
      return response;
    } on DioException catch (e) {
      formatError(e);
    }
    return response ?? Response(requestOptions: RequestOptions(method: method.name,data: data),statusCode: -1,statusMessage: "");
  }

  /// 取消网络请求
  void cancelRequests({CancelToken? token}) {
    token ?? _cancelToken.cancel("cancelled");
  }

  /// 设置Http代理(设置即开启)
  void setProxy({String? proxyAddress, bool enable = false}) {
    if (!enable) {
      return;
    }
    dio.httpClientAdapter = IOHttpClientAdapter()
      ..onHttpClientCreate = (client) {
        client.findProxy = (uri) {
          return proxyAddress ?? '';
        };
        return client;
      };
  }

  /// 设置https证书校验
  void setHttpsCertificateVerification({String? pem, bool enable = false}) {
    if (enable) {
      (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
        client.badCertificateCallback = (X509Certificate cert, String host, int port) {
          /// 验证证书
          if (cert.pem == pem) {
            return true;
          }
          return false;
        };
      };
    }
  }

  /// 开启日志打印
  void openLog() {
    _dio.interceptors.add(LogInterceptor(responseBody: true));
  }

  void formatError(DioException e) {
    if (e.type == DioExceptionType.connectionTimeout) {
      // It occurs when url is opened timeout.
    } else if (e.type == DioExceptionType.sendTimeout) {
      // It occurs when url is sent timeout.
    } else if (e.type == DioExceptionType.receiveTimeout) {
      //It occurs when receiving timeout
    } else if (e.type == DioExceptionType.badResponse) {
      // When the server response, but with a incorrect status, such as 404, 503...
    } else if (e.type == DioExceptionType.cancel) {
      // When the request is cancelled, dio will throw a error with this type.
    } else {
      //DEFAULT Default error type, Some other Error. In this case, you can read the DioError.error if it is not null.
    }
  }
}
