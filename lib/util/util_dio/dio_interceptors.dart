import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_key_store.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';

import '../util_sign/SignUtils.dart';

class DioInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    String strDeviceId = StorageUtil.get<String>(SPKey.keyDevId,
        defaultValue: ToolsUtil().genUuId());
    if (strDeviceId.isEmpty) {
      strDeviceId = ToolsUtil().genUuId();
    }

    String strToken = StorageUtil.get<String>(SPKey.keyToken);

    String strBrand = StorageUtil.get(SPKey.keyBrand, defaultValue: "unknown");
    String strModel = StorageUtil.get(SPKey.keyModel, defaultValue: "unknown");
    String strSysVersion =
        StorageUtil.get(SPKey.keySysVersion, defaultValue: "unknownVer");
    String strVersion = StorageUtil.get<String>(SPKey.keyVersion);
    // 自定义头部（注意不包含 Sign）
    Map<String, String> customHeaders = {
      "Platform": Platform.isAndroid ? "1" : "2",
      "Brand": strBrand,
      "Model": strModel,
      "Version": strVersion,
      "Device": strDeviceId,
      "Token": strToken,
      "OperatingSystemVersion": strSysVersion,
    };

    // 添加 headers 到请求头
    options.headers.addAll(customHeaders);

    // 1. 判断 content-type 是否是 multipart 或 application/octet-stream
    final contentType = options.contentType?.toLowerCase() ?? '';
    final isMultipart = contentType.startsWith('multipart/');
    final isStream = contentType == 'application/octet-stream';

    if (isMultipart || isStream) {
      // 跳过签名（后端不会验签）
      handler.next(options);
      return;
    }

    // 2. 收集参数：query + body（data）=> Map<String, List<String>>
    final Map<String, List<String>> allParams = {};

    // Query 参数
    options.queryParameters.forEach((key, value) {
      if (value != null) {
        allParams[key] = [value.toString()];
      }
    });

    // Body 参数处理
    final data = options.data;
    if (data is Map) {
      data.forEach((key, value) {
        if (value != null) {
          allParams[key.toString()] = [value.toString()];
        }
      });
    } else if (data is FormData) {
      for (final field in data.fields) {
        final key = field.key;
        final value = field.value;
        allParams.putIfAbsent(key, () => []).add(value);
      }
      // 文件类型在签名里跳过（类似后端）
      // 所以不处理 data.files
    }

    // 3. 生成签名
    final sign =
        SignUtils.generateSignature(headers: customHeaders, params: allParams);

    // 4. 添加签名到 headers
    options.headers['Sign'] = sign;

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    printResponse(response);
    // 请求成功是对数据做基本处理
    // if (response.statusCode == 200) {
    //   response.data = DioResponse(code: 0, message: "请求成功啦", data: response.data);
    // } else {
    //   response.data = DioResponse(code: 1, message: "请求失败啦", data: response.data);
    // }
    // 对某些单独的url返回数据做特殊处理
    if (response.requestOptions.baseUrl.contains("???????")) {
      //....
    }

    /// 根据业务需求进行定制化处理

    /// 重点
    handler.next(response);
  }

  void printResponse(Response response) {
    Map httpLogMap = Map();
    httpLogMap.putIfAbsent(
        "HttpRequestUrl", () => "${response.requestOptions.uri}");
    httpLogMap.putIfAbsent(
        "HttpRequestHeaders", () => response.requestOptions.headers);
    httpLogMap.putIfAbsent("HttpRequestQueryParameters",
        () => response.requestOptions.queryParameters);
    httpLogMap.putIfAbsent(
        "HttpRequestQueryData", () => response.requestOptions.data);
    httpLogMap.putIfAbsent("HttpRequestResponse", () => response.data);
    printJson(httpLogMap);
  }

  void printJson(Object object) {
    try {
      JsonEncoder encoder = const JsonEncoder.withIndent('');
      var encoderString = encoder.convert(object);
      debugPrint('\x1B[32m'); //  设置文本颜色为绿色
      debugPrint(encoderString);
      debugPrint('\x1B[0m'); // 重置文本颜色为默认值
    } catch (e) {
      print(e);
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    switch (err.type) {
      /// 连接服务器超时
      case DioExceptionType.connectionTimeout:
        {
          /// 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;

      /// 响应超时
      case DioExceptionType.receiveTimeout:
        {
          /// 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;

      /// 发送超时
      case DioExceptionType.sendTimeout:
        {
          ///根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;

      /// 请求取消
      case DioExceptionType.cancel:
        {
          /// 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;
      // 404/503错误
      case DioExceptionType.badResponse:
        {
          /// 根据自己的业务需求来设定该如何操作,可以是弹出框提示/或者做一些路由跳转处理
        }
        break;

      /// other 其他错误类型
      case DioExceptionType.unknown:
        {}
        break;
      default:
        break;
    }
    super.onError(err, handler);
  }
}
