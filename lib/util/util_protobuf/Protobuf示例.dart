import 'package:fixnum/src/int64.dart';

import 'MessageProto.pb.dart';

void main() {
  // 创建 Message 实例
  var message = Message()
    ..track = "track123"
    ..timestamp = DateTime.now().millisecondsSinceEpoch as Int64
    ..sender = 1001 as Int64
    ..receiver = 1002 as Int64
    ..type = 1
    ..content = [104, 101, 108, 108, 111]; // "hello" 的字节数组

  // 序列化
  var byteArray = message.writeToBuffer();
  print("Serialized Data: $byteArray");

  // 反序列化
  var webSocketMessage = Message.fromBuffer(byteArray);

  // 唯一ID
  var track = webSocketMessage.track;

  // 消息的发送者
  var sender = webSocketMessage.sender;

  // 消息的接收者
  var receiver = webSocketMessage.receiver;

  // 时间戳
  var timestamp = webSocketMessage.timestamp;


  /**
   *  CHAT(1, "聊天"),
      COMMENT(2, "评论"),
      LIKE(3, "点赞");
   */
  var business = webSocketMessage.business;

  /**
   * 内容类型
   *  J<PERSON><PERSON>(1, "JSON"),
      FIL<PERSON>(2, "文件"),
      PHOTO(3, "图片"),
      VOICE(4, "语音"),
      VIDEO(5, "视频"),
      TEXT(6, "文本");
   */
  var messageType = webSocketMessage.type;

  // 二进制内容
  var byteArrayContent = webSocketMessage.content;

  if (messageType == 1) {
    // 将 byteArrayContent 转成 String 再转成JSON
  } else if (messageType == 2) {
    // 将 byteArrayContent 转成文件
  } else if (messageType == 3) {
    // 将 byteArrayContent 转成图片
  } else if (messageType == 4) {
    // 将 byteArrayContent 转成语音
  } else if (messageType == 5) {
    // 将 byteArrayContent 转成视频
  } else if (messageType == 6) {
    //  将 byteArrayContent 转成文本
  }
}
