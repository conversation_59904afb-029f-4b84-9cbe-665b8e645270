//
//  Generated code. Do not modify.
//  source: MessageProto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use messageDescriptor instead')
const Message$json = {
  '1': 'Message',
  '2': [
    {'1': 'track', '3': 1, '4': 1, '5': 9, '10': 'track'},
    {'1': 'timestamp', '3': 2, '4': 1, '5': 3, '10': 'timestamp'},
    {'1': 'sender', '3': 3, '4': 1, '5': 3, '10': 'sender'},
    {'1': 'receiver', '3': 4, '4': 1, '5': 3, '10': 'receiver'},
    {'1': 'business', '3': 5, '4': 1, '5': 3, '10': 'business'},
    {'1': 'type', '3': 6, '4': 1, '5': 5, '10': 'type'},
    {'1': 'content', '3': 7, '4': 1, '5': 12, '10': 'content'},
  ],
};

/// Descriptor for `Message`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List messageDescriptor = $convert.base64Decode(
    'CgdNZXNzYWdlEhQKBXRyYWNrGAEgASgJUgV0cmFjaxIcCgl0aW1lc3RhbXAYAiABKANSCXRpbW'
    'VzdGFtcBIWCgZzZW5kZXIYAyABKANSBnNlbmRlchIaCghyZWNlaXZlchgEIAEoA1IIcmVjZWl2'
    'ZXISGgoIYnVzaW5lc3MYBSABKANSCGJ1c2luZXNzEhIKBHR5cGUYBiABKAVSBHR5cGUSGAoHY2'
    '9udGVudBgHIAEoDFIHY29udGVudA==');

