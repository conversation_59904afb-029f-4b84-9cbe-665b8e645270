import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CustomButtonView extends StatefulWidget {
  final String text;
  final double width;
  final double height;

  final List<Color> enabledBgColors;
  final List<Color> disabledBgColors;
  final EdgeInsetsGeometry margin;
  final EdgeInsetsGeometry padding;
  final VoidCallback? onTap;
  final Duration debounceDuration;

  final bool isEnabled;
  final double borderRadius;
  final bool bIsDefault;
  final double fontSize;
  final FontWeight fontWeight;
  final Color disableTextColor;
  final Color enableTextColor;

  const CustomButtonView(
      {super.key,
      required this.text,
      this.width = double.maxFinite,
      this.height = 48,
      this.padding = const EdgeInsets.symmetric(horizontal: 8),
      this.margin = EdgeInsets.zero,
      this.borderRadius = 40,
      this.enabledBgColors = BizColors.gradientColor,
      this.disabledBgColors = const [BizColors.rgb100fafafa, BizColors.rgb100fafafa],
      this.isEnabled = true,
      this.bIsDefault = true,
      this.fontSize = 15,
      this.fontWeight = FontWeight.w500,
      this.disableTextColor = BizColors.rgb100939eb3,
      this.enableTextColor = BizColors.rgb100ffffff,
      this.debounceDuration = const Duration(seconds: 1),
      this.onTap});

  @override
  CustomButtonViewState createState() => CustomButtonViewState();
}

class CustomButtonViewState extends State<CustomButtonView> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isEnabled
          ? () {
              if (widget.onTap != null) {
                widget.onTap!();
              }
            }
          : null,
      child: Container(
        width: widget.width,
        height: widget.height,
        margin: widget.margin,
        padding: widget.padding,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(widget.borderRadius)),
          gradient: LinearGradient(
            colors: ((widget.isEnabled && widget.bIsDefault) ? widget.enabledBgColors : widget.disabledBgColors),
          ),
        ),
        child: Center(
          child: Text(
            widget.text.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: (widget.bIsDefault && widget.isEnabled) ? widget.enableTextColor : (widget.disableTextColor ),
              fontSize: widget.fontSize.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}
