import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter/services.dart';

class NestedTabsWithRefresh extends StatefulWidget {
  const NestedTabsWithRefresh({super.key});

  @override
  State<NestedTabsWithRefresh> createState() => _NestedTabsWithRefreshState();
}

class _NestedTabsWithRefreshState extends State<NestedTabsWithRefresh> with SingleTickerProviderStateMixin {
  final List<String> _tabs = ['Tab 1', 'Tab 2'];
  late TabController _tabController;

  final ScrollController _scrollController = ScrollController();
  final Map<int, List<String>> _tabData = {};
  final Map<int, RefreshController> _refreshControllers = {};

  Color _appBarColor = Colors.transparent; // Default color
  Color _statusBarColor = Colors.transparent;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);

    for (int i = 0; i < _tabs.length; i++) {
      _tabData[i] = List.generate(20, (index) => 'Item $index');
      _refreshControllers[i] = RefreshController();
    }

    // Add listener to detect scroll direction
    _scrollController.addListener(() {
      debugPrint("scrollController.position.pixels ${_scrollController.position.pixels}");

      debugPrint("_scrollController.position.maxScrollExtent ${_scrollController.position.maxScrollExtent}");
      double pixels = _scrollController.position.pixels;
      double maxPixels = _scrollController.position.maxScrollExtent;
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        // When scrolling stops
        setState(() {
          _appBarColor = Colors.blue;  // Change AppBar color to blue when scroll stops
          _statusBarColor = Colors.blue;  // Change StatusBar color to blue
        });
      } else if (_scrollController.position.pixels >= 0 ) {
        // When scrolling starts (either forward or reverse)
        setState(() {
          _appBarColor = Colors.transparent; // Change AppBar color to transparent when scrolling starts
          _statusBarColor = Colors.transparent; // Change StatusBar color to transparent when scrolling starts
        });
      }
    });
  }

  void _onRefresh(int index) async {
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      _tabData[index] = List.generate(20, (i) => 'Refreshed $i');
    });
    _refreshControllers[index]!.refreshCompleted();
  }

  void _onLoading(int index) async {
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      _tabData[index]!.addAll(List.generate(10, (i) => 'Loaded ${i + 1}'));
    });
    _refreshControllers[index]!.loadComplete();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    for (var controller in _refreshControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, _) => [
          SliverAppBar(
            pinned: true,
            expandedHeight: 200,
            backgroundColor: Colors.blue, // Change AppBar color dynamically
            flexibleSpace: Stack(
              fit: StackFit.expand,
              children: [
                Image.asset(
                  AssetsRes.loginLogPic, // <-- 你的背景图路径
                  fit: BoxFit.cover,
                ),
                Container(
                  alignment: Alignment.bottomLeft,
                  padding: const EdgeInsets.all(16),
                  child: const Text(
                    'Nested Tabs Refresh',
                    style: TextStyle(
                      fontSize: 24,
                      color: Colors.white,
                      shadows: [Shadow(color: Colors.black54, blurRadius: 4)],
                    ),
                  ),
                ),
              ],
            ),
          ),
          SliverPersistentHeader(
            pinned: true,
            delegate: _StickyTabBarDelegate(
              height: 48,
              child: TabBar(
                controller: _tabController,
                tabs: _tabs.map((e) => Tab(text: e)).toList(),
              ),
            ),
          ),
        ],
        body: TabBarView(
          controller: _tabController,
          children: _tabs.asMap().entries.map((entry) {
            final index = entry.key;
            return SmartRefresher(
              controller: _refreshControllers[index]!,
              enablePullDown: true,
              enablePullUp: true,
              onRefresh: () => _onRefresh(index),
              onLoading: () => _onLoading(index),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                physics: const ClampingScrollPhysics(),
                itemCount: _tabData[index]!.length,
                itemBuilder: (context, i) => ListTile(title: Text(_tabData[index]![i])),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

class _StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _StickyTabBarDelegate({required this.child, required this.height});

  @override
  double get minExtent => height;
  @override
  double get maxExtent => height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      color: Colors.white,
      child: SizedBox(height: height, child: child),
    );
  }

  @override
  bool shouldRebuild(covariant _StickyTabBarDelegate oldDelegate) {
    return child != oldDelegate.child || height != oldDelegate.height;
  }
}
