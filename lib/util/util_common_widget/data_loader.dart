import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

enum LoaderState {
  stateNoAction,
  stateLoading,
  stateSucceed,
  stateFailed,
  stateNoData,
}

class ViewLoaderState {
  ViewLoaderState() {
    loadState = LoaderState.stateLoading;
  }
  LoaderState loadState = LoaderState.stateLoading;
}

class LoadingView extends StatelessWidget {
  final Color color;

  const LoadingView({super.key, this.color = Colors.white});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: color,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            SizedBox(
              width: 88.w,
              height: 88.w,
              child: Lottie.asset(
                AssetsRes.lottieDataLoading,
                height: 88.w,
                width: 88.w,
                fit: BoxFit.fitWidth,
                repeat: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LoaderContainer extends StatefulWidget {
  final ViewLoaderState? loaderState;
  final Widget? loadingView;
  final Widget? errorView;
  final Widget? emptyView;
  final Widget Function()? contentBuilder;
  final VoidCallback? onReload;
  final String? reloadText;
  final String? errorTip;
  final String? emptyTip;

  const LoaderContainer({
    required this.contentBuilder,
    required this.loaderState,
    this.emptyView,
    this.errorView,
    this.loadingView,
    this.onReload,
    this.reloadText,
    this.errorTip,
    this.emptyTip,
    super.key,
  });

  @override
  State<LoaderContainer> createState() => _LoaderContainerState();
}

class _LoaderContainerState extends State<LoaderContainer> {
  @override
  Widget build(BuildContext context) {
    Widget? currentWidget;
    switch (widget.loaderState?.loadState) {
      case LoaderState.stateLoading:
        currentWidget = widget.loadingView ?? const LoadingView();
        break;
      case LoaderState.stateFailed:
        currentWidget = widget.errorView ??
            ClassicalErrorView(
              onReload: widget.onReload,
              reloadText: widget.reloadText,
              errorTip: widget.errorTip,
            );
        break;
      case LoaderState.stateNoData:
        currentWidget = widget.emptyView ??
            ClassicalNoDataView(
              emptyTip: widget.emptyTip,
            );
        break;
      case LoaderState.stateSucceed:
      case LoaderState.stateNoAction:
        currentWidget = widget.contentBuilder?.call();
        break;
      default:
        break;
    }
    return Material(
      color: Colors.transparent,
      child: currentWidget ?? Container(),
    );
  }
}

class ClassicalErrorView extends StatelessWidget {
  const ClassicalErrorView({
    super.key,
    this.onReload,
    this.reloadText,
    this.errorTip,
    this.color = Colors.white,
  });

  final VoidCallback? onReload;
  final String? reloadText;
  final String? errorTip;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: color,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Image.asset(
              AssetsRes.defNoDataIcon,
              width: 116.w,
              height: 116.w,
              fit: BoxFit.cover,
            ),
            Text(
              errorTip ?? LanStrings.netWorkError.tr,
              style: const TextStyle(fontSize: 16, color: Color(0xff9FA2AC)),
            ),
            const Padding(padding: EdgeInsets.only(top: 21)),
            TextButton(
              onPressed: onReload ?? () {},
              style: TextButton.styleFrom(
                backgroundColor: const Color.fromARGB(0x0f, 0x66, 0x66, 0x66),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Container(
                alignment: Alignment.center,
                height: 38,
                width: 160,
                child: Text(
                  reloadText ?? LanStrings.clickToRetry.tr,
                  style:
                  const TextStyle(fontSize: 14, color: Color(0xff9FA2AC)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ClassicalNoDataView extends StatelessWidget {
  final String? emptyTip;
  final Color? color;
  final String strDefImg;

  const ClassicalNoDataView({
    super.key,
    this.emptyTip,
    this.color = Colors.transparent,
    this.strDefImg = AssetsRes.defNoDataIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: color,
      child: Center(
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Image.asset(
              strDefImg,
              width: 64.w,
              height: 64.w,
              fit: BoxFit.cover,
            ),
            Padding(
              padding: EdgeInsets.only(top: 68.w),
              child: Text(
                emptyTip ?? LanStrings.noDataNow.tr,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: BizColors.rgb100999999,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
