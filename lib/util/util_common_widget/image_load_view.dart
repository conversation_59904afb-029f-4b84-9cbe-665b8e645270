import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/util/util_common_widget/image_preload_util.dart';
import 'package:get/get.dart';

enum ImageType { network, assets, localFile }

class _ImageLoadComplete extends StatefulWidget {
  final ExtendedImageState? state;
  final double? width;
  final double? height;
  final Duration? fadeOutDuration;
  final Duration? fadeInDuration;
  final Function(bool isComplete)? imageCompleteCallback;

  final AfterPaintImage? afterPaintImage;
  @override
  _ImageLoadCompleteState createState() => _ImageLoadCompleteState();

  const _ImageLoadComplete(this.state, this.width, this.height,
      {this.fadeOutDuration,
      this.fadeInDuration,
      this.imageCompleteCallback,
      this.afterPaintImage});
}

class _ImageLoadCompleteState extends State<_ImageLoadComplete>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  @override
  void initState() {
    _controller = AnimationController(
        vsync: this,
        duration: widget.fadeInDuration,
        lowerBound: 0.0,
        upperBound: 1.0);
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((value) {
      if (widget.imageCompleteCallback != null) {
        widget.imageCompleteCallback!(true);
      }
    });
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _controller?.forward();
    return FadeTransition(
      opacity: _controller!,
      child: ExtendedRawImage(
        image: widget.state?.extendedImageInfo?.image,
        width: widget.width, //ScreenUtil.instance.setWidth(wi),
        height: widget.height, //ScreenUtil.instance.setWidth(400),
        fit: BoxFit.cover,
        afterPaintImage: widget.afterPaintImage,
      ),
    );
  }
}

class ImageLoadView extends StatelessWidget {
  /// 图片URL
  String? path;

  /// 圆角半径
  final double? radius;

  /// 宽
  final double? width;

  /// 高
  final double? height;

  /// 填充效果
  final BoxFit? fit;

  /// 加载中图片
  final String? placeholder;

  ///
  ImageType? imageType;

  /// 透明度
  final double? opacity;

  final double? sigmaX;
  final double? sigmaY;

  /// 过滤颜色
  final Color? filterColor;

  final Widget? child;
  final EdgeInsetsGeometry? padding;

  /// 图片外边框
  final EdgeInsetsGeometry? margin;

  /// 子控件位置
  final AlignmentGeometry? alignment;

  final double? elevation;

  final Color? shadowColor;

  final BoxShape? shape;

  final Color? borderColor;

  final double? borderWidth;

  final Widget? placeholderWidget;

  /// Will resize the image in cache to have a certain width using [ResizeImage]
  final int? memCacheWidth;

  /// Will resize the image in cache to have a certain height using [ResizeImage]
  final int? memCacheHeight;

  final preloadManager = ImagePreloadManager();

  /// 图片加载完成回调
  final Function(bool isComplete)? imageCompleteCallback;

  final Duration? fadeOutDuration;
  final Duration? fadeInDuration;
  AfterPaintImage? afterPaintImage;
  Color placeHolderColor = BizColors.rgb100f6f6f6;
  ImageLoadView(
    this.path, {
    Key? key,
    this.radius = 0.0,
    this.width,
    this.height,
    this.margin = EdgeInsets.zero,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.placeholderWidget,
    this.imageType = ImageType.network,
    this.opacity = 1.0,
    this.sigmaX = 0.0,
    this.sigmaY = 0.0,
    this.filterColor = Colors.transparent,
    this.child,
    this.alignment = Alignment.center,
    this.padding = EdgeInsets.zero,
    this.elevation = 0.0,
    this.shadowColor,
    this.shape = BoxShape.rectangle,
    this.borderColor,
    this.borderWidth = 0.0,
    this.memCacheWidth,
    this.memCacheHeight,
    this.imageCompleteCallback,
    this.afterPaintImage,
    this.placeHolderColor = BizColors.rgb100f6f6f6,
    this.fadeOutDuration = const Duration(milliseconds: 0),
    this.fadeInDuration = const Duration(milliseconds: 0),
  }) : super(key: key) {
    if (path != null) {
      if (path!.isNotEmpty && imageType != ImageType.localFile) {
        imageType = path!.toLowerCase().startsWith("http") ? ImageType.network : ImageType.assets;
      }
    } else {
      path = "";
    }
  }

  Widget? _getPlaceHolder() {
    return Container(color: (placeholder?.isEmpty ?? true ) ? placeHolderColor : Colors.transparent,child: placeholderWidget ?? Image.asset((placeholder == null || (placeholder?.isEmpty ?? true)) ? AssetsRes.imageLoadingPlaceHolder : placeholder ?? AssetsRes.imageLoadingPlaceHolder,fit: BoxFit.contain,));
  }

  Widget loadStateChangedFunc(ExtendedImageState state) {
    switch (state.extendedImageLoadState) {
      case LoadState.loading:
        return _getPlaceHolder() ?? Image.asset(AssetsRes.imageLoadingPlaceHolder);
      case LoadState.completed:
        return _ImageLoadComplete(
          state,
          width,
          height,
          imageCompleteCallback: imageCompleteCallback,
          fadeOutDuration: fadeOutDuration,
          fadeInDuration: fadeInDuration,
          afterPaintImage: afterPaintImage,
        );
        break;
      case LoadState.failed:
        if (imageCompleteCallback != null) {
          imageCompleteCallback!(false);
        }
        return _getPlaceHolder() ?? Image.asset(AssetsRes.imageLoadingPlaceHolder);
    }
  }

  // extended_image: ^1.5.0
  Widget? _getImageWidgetNew() {
    Widget? imageWidget;
    switch (imageType) {
      case ImageType.network:
        imageWidget = (path?.isEmpty ?? true)
            ? _getPlaceHolder()
            : ExtendedImage.network(
                path ?? "",
                height: height,
                width: width,
                fit: fit,
                cache: true,
                clearMemoryCacheWhenDispose: true,
                cacheWidth: memCacheWidth ?? ((width ?? 0 ) > 0 ? (((width ?? 0 ) * 2).toInt()) : (null)),
                cacheHeight: memCacheHeight ?? ((height ?? 0 ) > 0 ? (((height ?? 0 ) * 2).toInt()) : (null)),
                loadStateChanged: loadStateChangedFunc,
              );
        break;
      case ImageType.assets:
        imageWidget = ((path?.isEmpty ?? true)
            ? AssetImage(placeholder ?? AssetsRes.imageLoadingPlaceHolder)
            : ExtendedImage.asset(path ?? "",
                height: height,
                width: width,
                fit: fit,
                clearMemoryCacheWhenDispose: true,
                cacheWidth: memCacheWidth ?? ((width ?? 0 ) > 0 ? (((width ?? 0 ) * 2).toInt()) : (null)),
                cacheHeight: memCacheHeight ?? ((height ?? 0 ) > 0 ? (((height ?? 0 ) * 2).toInt()) : (null)),
                loadStateChanged: loadStateChangedFunc)) as Widget?;
        break;
      case ImageType.localFile:
        imageWidget = ((path?.isEmpty ?? true)
            ? AssetImage(placeholder ?? AssetsRes.imageLoadingPlaceHolder)
            : ExtendedImage.file(File(path ?? ""),
                height: height,
                width: width,
                fit: fit,
                clearMemoryCacheWhenDispose: true,
                cacheWidth: memCacheWidth ?? ((width ?? 0 ) > 0 ? (((width ?? 0 ) * 2).toInt()) : (null)),
                cacheHeight: memCacheHeight ?? ((height ?? 0 ) > 0 ? (((height ?? 0 ) * 2).toInt()) : (null)),
                loadStateChanged: loadStateChangedFunc)) as Widget?;
        break;
      default:
        break;
    }
    return imageWidget;
  }

  Widget? _getImageWidget() {
    Widget? imageWidget;
    switch (imageType) {
      case ImageType.network:
        if ((path?.isEmpty ?? true)) {
          imageWidget = _getPlaceHolder();
        } else {
          preloadManager.recordUrl(path!);
          imageWidget = CachedNetworkImage(
            placeholder: (context, url) => _getPlaceHolder() ?? Image.asset(AssetsRes.imageLoadingPlaceHolder),
            imageUrl: path ?? "",
            fit: fit,
            height: height,
            width: width,
            cacheKey: path,
            placeholderFadeInDuration: Duration(milliseconds: 0),
            fadeInDuration: fadeInDuration ?? const Duration(milliseconds: 50),
            fadeOutDuration: fadeOutDuration ?? const Duration(milliseconds: 50),
            memCacheWidth: memCacheWidth ?? ((width ?? 0) > 0 ? (((width ?? 0) * 2).toInt()) : (null)),
            memCacheHeight: memCacheHeight ?? ((height ?? 0) > 0 ? (((height ?? 0) * 2).toInt()) : (null)),
            errorWidget: (context, url, error) => _getPlaceHolder() ?? Image.asset(AssetsRes.imageLoadingPlaceHolder),
          );
        }

        break;
      case ImageType.assets:
        imageWidget = ((path?.isEmpty ?? true)
            ? AssetImage(placeholder ?? "")
            : ExtendedImage.asset(path ?? '')) as Widget?;
        break;
      case ImageType.localFile:
        imageWidget = ((path?.isEmpty ?? true)
            ? AssetImage(placeholder ?? "")
            : FadeInImage(placeholder: AssetImage(placeholder ?? AssetsRes.imageLoadingPlaceHolder), image: ResizeImage.resizeIfNeeded(memCacheWidth, memCacheHeight, FileImage(File(path ?? ""))), fit: fit)) as Widget?;
        break;
      default:
        break;
    }
    return imageWidget;
  }

  @override
  Widget build(BuildContext context) {
    Widget? imageWidget = (imageCompleteCallback == null && afterPaintImage == null) ? _getImageWidget() : _getImageWidgetNew();
    return Card(
        color: Colors.transparent,
        shadowColor: shadowColor,
        shape: shape == BoxShape.circle ? const CircleBorder() : RoundedRectangleBorder(borderRadius: BorderRadius.circular(radius ?? 0)),
        clipBehavior: Clip.antiAlias,
        elevation: elevation,
        margin: margin,
        child: SizedBox(
            height: height ?? double.infinity,
            width: width ?? double.infinity,
            child: Stack(children: <Widget>[
              Positioned.fill(child: imageWidget ?? Container()),
              Positioned.fill(
                  child: Container(
                      decoration: BoxDecoration(
                          shape: shape ?? BoxShape.rectangle,
                          borderRadius: shape == BoxShape.circle ? null : BorderRadius.circular(radius ?? 0),
                          border: Border.all(
                              color: borderColor ?? Theme.of(context).primaryColor,
                              width: borderWidth ?? 0,
                              style: borderWidth == 0.0 ? BorderStyle.none : BorderStyle.solid)))),
/*              BackdropFilter(
                  filter: ImageFilter.blur(
                      sigmaX: sigmaX ?? 0, sigmaY: sigmaY ?? 0),
                  child: Opacity(
                      opacity: opacity ?? 0,
                      child: Container(
                          color: filterColor,
                          alignment: alignment,
                          padding: padding,
                          child: child ?? const SizedBox())))*/
            ])));
  }
}
