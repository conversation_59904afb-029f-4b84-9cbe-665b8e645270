import 'package:flutter/material.dart';

class SimpleAlertDialog {
  static const int alertCancel = 0;
  static const int alertOk = 1;
  static const int aAlertOkDelFile = 2;

  static Future<dynamic> show(BuildContext context,
      String content, String cancelTxt, String okTxt, {String strTitle = "", bool contentCenter = false, bool barrierDismissible = false}) async {
    return showDialog(
      context: context,
      builder: (context) {
        bool noTitle = (strTitle == null || strTitle.isEmpty);
        return ButtonBarTheme(
          data: ButtonBarThemeData(alignment: MainAxisAlignment.center),
          child: AlertDialog(
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(15))),
            title: noTitle
                ? null
                : Text(
              strTitle,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18, color: Color(0xff333333)),
            ),
            content: Text(
              content,
              textAlign: contentCenter ? TextAlign.center : null,
              style: noTitle
                  ? TextStyle(fontSize: 18, color: Color(0xff333333))
                  : TextStyle(fontSize: 15, color: Colors.black54),
            ),
            contentPadding: noTitle
                ? EdgeInsets.fromLTRB(24.0, 30.0, 24.0, 20.0)
                : EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 20.0),
            actions: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Visibility(
                    visible: cancelTxt.isNotEmpty,
                    child: Container(
                      width: 110,
                      height: 40,
                      child: TextButton(
                        style: TextButton.styleFrom(
                          backgroundColor: Color(0xFFF5F5F5),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0),),
                        ),
                        child: Text(cancelTxt,
                          style: TextStyle(fontSize: 15, color: Color(0xff333333)),),
                        onPressed: () => Navigator.pop(context, alertCancel),
                      ),
                    ),
                  ),
                  Visibility(visible: cancelTxt.isNotEmpty, child: SizedBox(width: 20)),
                  Container(
                    width: 110,
                    height: 40,
                    child: TextButton(
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.black12,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0),),
                      ),
                      child: Text(okTxt, style: TextStyle(fontSize: 15, color: Color(0xff14413d)),),
                      onPressed: () => Navigator.pop(context, alertOk),
                    ),),
                ],
              ),
            ],
            actionsPadding: EdgeInsets.only(bottom: 15),
          ),
        );
      },
      barrierDismissible: barrierDismissible,
      routeSettings: RouteSettings(name: "/AlertPage"),
    );
  }

}