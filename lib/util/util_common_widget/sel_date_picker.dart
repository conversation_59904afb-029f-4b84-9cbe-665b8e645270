import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

Widget selectDate({List<String>? parts}) {
  var time = '';

  Widget titleLineContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      height: 48.h,
      width: 1.sw,
      child: Row(
        children: [
          InkWell(
              onTap: () {
                Get.back(result: "");
              },
              child: Container(
                  width: 80.w,
                  height: 24.h,
                  alignment: Alignment.center,
                  child: Text("取消", style: TextStyle(fontSize: 18.sp, color: BizColors.rgb100939eb3)))),
          Container(
              width: 180.w,
              height: 24.h,
              alignment: Alignment.center,
              child: Text("选择日期", style: TextStyle(fontSize: 18.sp, color:BizColors.rgb1000c1018,),)),
          InkWell(
              onTap: () {
                Get.back(result: time);
              },
              child: Container(width: 80.w, height: 24.h, alignment: Alignment.center, child: Text("确定", style: TextStyle(fontSize: 18.sp, color: BizColors.rgb100939eb3)))),
        ],
      ),
    );
  }


  DateTime now = DateTime.now();
  DateTime maxDate = DateTime(now.year, now.month, now.day);
  return Scaffold(
    backgroundColor: Colors.transparent,
    resizeToAvoidBottomInset: false,
    body: Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(topLeft: Radius.circular(32), topRight: Radius.circular(32)),
          child: Container(
            height: 356.h,
            width: 1.sw,
            color: Colors.white,
            child: Stack(
              children: [
                // ImageLoadView("", width: 1.sw, height: 96,),
                Column(
                  children: [
                    Gap(4.h),
                    titleLineContent(),
                    Expanded(
                        child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          maximumYear: DateTime.now().year,
                          maximumDate: maxDate,
                          initialDateTime: (parts != null) ? DateTime(ToolsUtil.safeParseInt(parts[0]), ToolsUtil.safeParseInt(parts[1]), ToolsUtil.safeParseInt(parts[2])) : DateTime.now().subtract(const Duration(days: 365 * 18 + 4)),
                          onDateTimeChanged: (DateTime newDate) {
                            if (newDate.isAfter(maxDate)) {
                              newDate = maxDate;
                            }
                            /// 如果选择的日期在最小日期之前，则修正为最小日期
                            if (newDate.isBefore(DateTime(1976, 1, 1))) {
                              newDate = DateTime(1976, 1, 1);
                            }
                            time = DateFormat('yyyy-MM-dd').format(newDate);
                          },
                        )),
                    // CustomButtonView(margin: EdgeInsets.symmetric(horizontal: 24.w), text: "确定", bIsDefault: true, isEnabled: true,
                    //   onTap: () {
                    //     Get.back(result: time);
                    //   },
                    // ),
                    Gap(24.h),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    ),
  );





}
