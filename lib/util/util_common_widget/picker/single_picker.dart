import 'package:flutter/cupertino.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';

Future<void> showCupertinoPickerModal({
  required BuildContext context,
  required List<String> items,
  int initialIndex = 0,
  double itemHeight = 42,
  int visibleItemCount = 3,
  double? height,
  Widget? cancel,
  Widget? title,
  Widget? confirm,
  required void Function(int selectedIndex) onConfirm,
}) async {
  final FixedExtentScrollController scrollController =
  FixedExtentScrollController(initialItem: initialIndex);

  const double topBarHeight = 52;
  final double selectionHeight = itemHeight;
  final double pickerHeight = height ?? (topBarHeight + itemHeight * visibleItemCount);
  await showCupertinoModalPopup(
    context: context,
    builder: (_) {
      double bottomPadding = MediaQuery.of(context).padding.bottom;
      int selectedIndex = initialIndex;
      return StatefulBuilder(
        builder: (context, setState) {
          return Container(
            height: pickerHeight + bottomPadding + 24,
            padding: const EdgeInsets.only(bottom: 44),
            decoration: const BoxDecoration(
              color: CupertinoColors.systemBackground,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Stack(
              children: [
                // Picker
                Positioned(
                  top: topBarHeight,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: CupertinoPicker.builder(
                    scrollController: scrollController,
                    itemExtent: itemHeight,
                    childCount: items.length,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectedIndex = index;
                      });
                    },
                    selectionOverlay: Container(
                      height: selectionHeight,
                      decoration: BoxDecoration(
                        border: Border.symmetric(
                          horizontal: BorderSide(
                            color: BizColors.rgb100d1d8e1,
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                    itemBuilder: (context, index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontSize: 18,
                            color: index == selectedIndex
                                ? BizColors.rgb1000c1018
                                : BizColors.rgb100d1d8e1,
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // 顶部操作栏
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  height: topBarHeight,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CupertinoButton(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        minSize: 44,
                        child: cancel ??
                            const Text(
                              '取消',
                              style: TextStyle(
                                fontSize: 18,
                                color: BizColors.rgb100939eb3,
                              ),
                            ),
                        onPressed: () => Navigator.pop(context),
                      ),
                      Expanded(
                        child: Center(
                          child: title ??
                              const Text(
                                '选择性别',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: BizColors.rgb1000c1018,
                                ),
                              ),
                        ),
                      ),
                      CupertinoButton(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        minSize: 44,
                        child: confirm ??
                            const Text(
                              '确定',
                              style: TextStyle(
                                fontSize: 18,
                                color: BizColors.rgb100939eb3,
                              ),
                            ),
                        onPressed: () {
                          Navigator.pop(context);
                          onConfirm(selectedIndex);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}
