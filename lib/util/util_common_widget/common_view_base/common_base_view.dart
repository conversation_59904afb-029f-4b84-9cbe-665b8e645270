import 'package:flutter/material.dart';
import 'package:flutter_common_base/util/util_common_widget/common_view_base/common_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/smart_refresher_widget.dart';
import 'package:get/get.dart';

abstract class CommonBaseView<T extends CommonBaseController> extends StatefulWidget {
  final bool isGlobal;
  final String? tag;
  final Map<String,dynamic>? mapParam;
  final bool enablePullUp;
  final bool enablePullDown;

  /// 构造函数，支持全局和局部模式
  const CommonBaseView({super.key, required this.isGlobal, this.tag, this.mapParam,this.enablePullDown = false,this.enablePullUp = false});
}

abstract class CommonBaseViewState<T extends CommonBaseController> extends State<CommonBaseView<T>> {

  late T controller;
  T createController();

  @override
  void initState() {
    super.initState();

    /// 在视图初始化时，进行控制器的注册和初始化
    if (widget.isGlobal) {
      /// 全局模式：如果控制器没有注册过，就创建
      if (!Get.isRegistered<T>(tag: widget.tag)) {
        controller = Get.put<T>(createController(), tag: widget.tag,);
      } else {
        controller = Get.find<T>(tag: widget.tag);
      }
    } else {
      /// 局部模式：每次都创建一个新的控制器
      controller = Get.put<T>(createController(), tag: widget.tag);
    }
  }

  @override
  void dispose() {
    /// 局部模式下销毁 Controller
    if (!widget.isGlobal) {
      Get.delete<T>(tag: widget.tag); /// 删除局部控制器
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<T>(
      tag: widget.tag,
      builder: (_) {
        return LoaderContainer(
          contentBuilder: () => SmartRefresherWidget(
            buildBody(),
            controller.refreshController,
            onPullUpLoading: widget.enablePullUp ? controller.onPullUpLoadMore : null,
            onPullDownRefresh: widget.enablePullDown ? controller.onPullDownReload : null,
          ),
          loaderState: controller.dataLoadState,
          emptyView: noDataView(),
        );
      },
    );
  }

  Widget noDataView() {
    return const ClassicalNoDataView(emptyTip: null,);
  }

  /// 子类需要实现这个方法来构建 UI
  Widget buildBody();
}