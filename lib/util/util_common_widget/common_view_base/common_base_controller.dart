import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

abstract class CommonBaseController extends GetxController {
  /// 子类可以重写这个方法
  late RefreshController refreshController;

  ///数据加载遮罩
  ViewLoaderState dataLoadState = ViewLoaderState();

  @override
  void onInit() {
    super.onInit();

    refreshController = RefreshController(initialLoadStatus: LoadStatus.idle);
  }

  ///上下拉加载更多
  Future<void> onPullUpLoadMore() async {

  }
  ///下拉刷新
  Future<void> onPullDownReload() async {

  }

}