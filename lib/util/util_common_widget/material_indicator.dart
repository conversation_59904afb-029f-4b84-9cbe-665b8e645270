import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class MaterialIndicator extends Decoration {
  /// Height of the indicator. Defaults to 4
  final double height;

  /// Determines to location of the tab, [TabPosition.bottom] set to default.
  final TabPosition tabPosition;

  /// topRight radius of the indicator, default to 5.
  final double topRightRadius;

  /// topLeft radius of the indicator, default to 5.
  final double topLeftRadius;

  /// bottomRight radius of the indicator, default to 0.
  final double bottomRightRadius;

  /// bottomLeft radius of the indicator, default to 0
  final double bottomLeftRadius;

  /// Color of the indicator, default set to [Colors.black]
  final Color color;

  /// Horizontal padding of the indicator, default set 0
  final double horizontalPadding;

  /// [PagingStyle] determines if the indicator should be fill or stroke, default to fill
  final PaintingStyle paintingStyle;

  /// StrokeWidth, used for [PaintingStyle.stroke], default set to 2
  final double strokeWidth;

  /// valid when tabPosition is bottom, default set 0
  final double bottomPadding;

  ///width不为0的时候horizontalPadding不管用了，以width为准
  final double width;

  MaterialIndicator({
    this.height = 4,
    this.tabPosition = TabPosition.bottom,
    this.topRightRadius = 5,
    this.topLeftRadius = 5,
    this.bottomRightRadius = 0,
    this.bottomLeftRadius = 0,
    this.color = Colors.black,
    this.horizontalPadding = 0,
    this.paintingStyle = PaintingStyle.fill,
    this.strokeWidth = 2,
    this.bottomPadding = 0,
    this.width = 0,
  });
  @override
  _CustomPainter createBoxPainter([VoidCallback? onChanged]) {
    return new _CustomPainter(
      this,
      onChanged ?? () {},
      bottomLeftRadius: bottomLeftRadius,
      bottomRightRadius: bottomRightRadius,
      color: color,
      height: height,
      horizontalPadding: horizontalPadding,
      tabPosition: tabPosition,
      topLeftRadius: topLeftRadius,
      topRightRadius: topRightRadius,
      paintingStyle: paintingStyle,
      strokeWidth: strokeWidth,
      bottomPadding: bottomPadding,
      width: width,
    );
  }
}

class _CustomPainter extends BoxPainter {
  final MaterialIndicator? decoration;
  final double? height;
  final TabPosition? tabPosition;
  final double? topRightRadius;
  final double? topLeftRadius;
  final double? bottomRightRadius;
  final double? bottomLeftRadius;
  final Color? color;
  double? horizontalPadding;
  final double? strokeWidth;
  final PaintingStyle? paintingStyle;
  final double? bottomPadding;
  final double? width;

  _CustomPainter(
    this.decoration,
    VoidCallback onChanged, {
    this.height,
    this.tabPosition,
    this.topRightRadius,
    this.topLeftRadius,
    this.bottomRightRadius,
    this.bottomLeftRadius,
    this.color,
    this.horizontalPadding,
    this.paintingStyle,
    this.strokeWidth,
    this.bottomPadding,
    this.width,
  })  : assert(decoration != null),
        super(onChanged);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(horizontalPadding != null && (horizontalPadding ?? 0) >= 0);
    assert((horizontalPadding ?? 0) < ((configuration.size?.width) ?? 0) / 2,
        "Padding must be less than half of the size of the tab");
    assert(color != null);
    assert(height != null && (height ?? 0) > 0);
    assert(tabPosition != null);
    assert(topRightRadius != null);
    assert(topLeftRadius != null);
    assert(bottomRightRadius != null);
    assert(bottomLeftRadius != null);
    assert((strokeWidth ?? 0) >= 0 &&
        (strokeWidth ?? 0) < (configuration.size?.width ?? 0) / 2 &&
        (strokeWidth ?? 0) < (configuration.size?.height ?? 0) / 2);

    //offset is the position from where the decoration should be drawn.
    //configuration.size tells us about the height and width of the tab.
    if ((width ?? 0) > 0 && (width ?? 0) < (configuration.size?.width ?? 0)) {
      horizontalPadding = ((configuration.size?.width ?? 0) - (width ?? 0)) / 2;
    }
    Size mySize = Size(
        (configuration.size?.width ?? 0) - (horizontalPadding ?? 0 * 2),
        height ?? 0);

    Offset myOffset = Offset(
      offset.dx + (horizontalPadding ?? 0),
      offset.dy +
          (tabPosition == TabPosition.bottom
              ? (configuration.size?.height ?? 0) -
                  (height ?? 0) -
                  (bottomPadding ?? 0)
              : 0),
    );

    final Rect rect = myOffset & mySize;
    final Paint paint = Paint();
    paint.color = color ?? Colors.white;
    paint.style = paintingStyle ?? PaintingStyle.stroke;
    paint.strokeWidth = strokeWidth ?? 0;
    canvas.drawRRect(
        RRect.fromRectAndCorners(
          rect,
          bottomRight: Radius.circular(bottomRightRadius ?? 0),
          bottomLeft: Radius.circular(bottomLeftRadius ?? 0),
          topLeft: Radius.circular(topLeftRadius ?? 0),
          topRight: Radius.circular(topRightRadius ?? 0),
        ),
        paint);
  }
}

enum TabPosition { top, bottom }
