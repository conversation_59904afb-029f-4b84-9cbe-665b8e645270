import 'dart:async';
import 'dart:isolate';

import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

class FutureLoadingDialog extends StatefulWidget {
  final Future<dynamic> future;

  const FutureLoadingDialog(this.future, {super.key});

  @override
  FutureLoadingDialogState createState() => FutureLoadingDialogState();
}

class FutureLoadingDialogState extends State<FutureLoadingDialog> {
  @override
  void initState() {
    super.initState();

    widget.future.then((value) {
      if (mounted) {
        Navigator.of(context).pop(value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: SizedBox(
            width: 75.w,
            height:75.w,
            child: Lottie.asset(AssetsRes.lottieDataLoading, fit: BoxFit.fitWidth)),
      ),
    );
  }
}
