import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/data_model/tab_manage/tabIndexData.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class MyCustomTabBar extends StatefulWidget {
  final TabController tabController;
  final List<VideoCatData> listTabs;
  final double tabHeight;
  const MyCustomTabBar({super.key,required this.tabController,required this.listTabs,this.tabHeight = 44});
  @override
  State<StatefulWidget> createState() {
    return MyHomePage();
  }
}

class MyHomePage extends State<MyCustomTabBar> with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _curvedAnimation;
  List<GlobalKey> tabKeys = [];
  int nLenTabs = 0;

  @override
  void initState() {
    super.initState();
    _tabController = widget.tabController;
    _scrollController = ScrollController();
    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 100),);
    _curvedAnimation = CurvedAnimation(parent: _animationController, curve: Curves.linear,);
    _tabController.addListener(_scrollToTab);

    ///tab数量
    nLenTabs = widget.listTabs.length;
    for (int nIndex = 0; nIndex < nLenTabs; nIndex++) {
      tabKeys.add(GlobalKey());
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.tabHeight,
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: TabBar(
          controller: _tabController,
          isScrollable: true,
          padding: EdgeInsets.zero,
          labelPadding: EdgeInsets.only(left: 0, right: 24.w),
          labelStyle: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, color: Colors.white),
          unselectedLabelStyle: TextStyle(fontSize: 14.sp,fontWeight: FontWeight.w400,color: Colors.white),
          tabAlignment: TabAlignment.start,
          indicator: const BoxDecoration(),
          tabs: _listSubTabs(nLenTabs),
        ),
      ),
    );
  }

  List<Widget> _listSubTabs(int nLen) {
    return List.generate(nLen, (index) =>
        CustomTab(
          key: tabKeys[index],
          tabController: _tabController,
          index: index,
          text: widget.listTabs[index].name,
        ),
    );
  }

  void _scrollToTab() {
    if (_tabController.indexIsChanging) {
      final RenderBox renderBox = tabKeys[_tabController.index].currentContext!.findRenderObject() as RenderBox;
      final double tabLeft = renderBox.localToGlobal(Offset.zero).dx;
      final double tabWidth = renderBox.size.width;
      final double screenWidth = MediaQuery.of(context).size.width;
      double targetScroll = _scrollController.offset + tabLeft - (screenWidth - tabWidth) / 2;

      targetScroll = targetScroll.clamp(_scrollController.position.minScrollExtent, _scrollController.position.maxScrollExtent,);
      _animationController.forward(from: 0.0);
      _curvedAnimation.addListener(() {
        _scrollController.jumpTo(_scrollController.offset + (_curvedAnimation.value * (targetScroll - _scrollController.offset)),
        );
      });
    }
  }
}

class CustomTab extends StatelessWidget {
  final TabController tabController;
  final int index;
  final String text;

  const CustomTab({
    super.key,
    required this.tabController,
    required this.index,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(text),
        AnimatedBuilder(
          animation: tabController.animation!,
          builder: (context, child) {
            double value = (tabController.animation!.value - index).abs();
            double opacity = 1.0 - value.clamp(0.0, 1.0);
            return Opacity(
              opacity: opacity >= 1 ? 1 : (opacity <= 0 ? 0 : opacity),
              child: Image.asset(AssetsRes.tabVideoCatSub, width: 12.w, height: 5.h)
            );
          },
        ),
      ],
    );
  }
}