import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ImagePreloadManager {
  static const String _key = 'cached_image_urls';
  static const int _maxUrls = 500;

  static final ImagePreloadManager _instance = ImagePreloadManager._internal();

  factory ImagePreloadManager({bool debug = false}) {
    _instance.debug = debug;
    return _instance;
  }

  ImagePreloadManager._internal();

  final Set<String> _cachedUrls = {};
  bool debug = false;

  void setDebug(bool value) {
    debug = value;
    debugPrint('调试日志已${debug ? '开启' : '关闭'}');
  }

  /// 返回已记录的 URL 列表（只读）
  List<String> get cachedUrls => _cachedUrls.toList(growable: false);

  /// 添加一个合法 URL 到缓存记录
  void recordUrl(String url) {
    if (_isValidUrl(url) && _cachedUrls.add(url)) {
      if (debug) debugPrint('记录图片URL: $url');
      _saveToStorage();
    }
    // if (debug) debugPrint("+++++++++++++++++++++++++++++++++++ size ${_cachedUrls.length}--------------------");
  }

  /// 批量添加多个合法 URL
  void recordUrls(Iterable<String> urls) {
    bool changed = false;
    for (final url in urls) {
      if (_isValidUrl(url) && _cachedUrls.add(url)) {
        changed = true;
        if (debug) debugPrint('记录图片URL: $url');
      }
    }
    if (changed) _saveToStorage();
  }

  Future<void> restoreFromStorage() async {
    final prefs = await SharedPreferences.getInstance();
    final list = prefs.getStringList(_key) ?? [];
    _cachedUrls.clear();
    _cachedUrls.addAll(list.where(_isValidUrl));
    if (debug) debugPrint('恢复已记录URL ${_cachedUrls.length} 个');
  }

  Future<void> _saveToStorage() async {
    final prefs = await SharedPreferences.getInstance();
    final urls = _cachedUrls.take(_maxUrls).toList();
    await prefs.setStringList(_key, urls);
  }

  Future<void> clearStorage() async {
    _cachedUrls.clear();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_key);
    if (debug) debugPrint('清空 URL 缓存记录');
  }

  /// 限制并发数量，预加载所有图片
  Future<void> preloadAll(BuildContext context, {int maxConcurrent = 4}) async {
    final urls = _cachedUrls.toList();
    if (urls.isEmpty) return;
    if (debug) debugPrint('开始预加载 ${urls.length} 张图片');

    final queue = List<String>.from(urls);
    final List<Future<void>> workers = [];

    for (int i = 0; i < maxConcurrent; i++) {
      workers.add(Future(() async {
        while (queue.isNotEmpty) {
          final url = queue.removeAt(0);
          await _preloadUrl(context, url);
        }
      }));
    }

    await Future.wait(workers);
    if (debug) debugPrint('所有图片预加载完成');
  }

  Future<void> _preloadUrl(BuildContext context, String url) async {
    final provider = CachedNetworkImageProvider(url);
    final config = createLocalImageConfiguration(context);

    final completer = Completer<void>();
    final stream = provider.resolve(config);

    final listener = ImageStreamListener(
          (ImageInfo _, bool __) {
        if (debug) debugPrint('预加载成功: $url');
        if (!completer.isCompleted) completer.complete();
      },
      onError: (Object error, StackTrace? stackTrace) {
        if (debug) debugPrint('预加载失败: $url - $error');
        if (!completer.isCompleted) completer.complete();
      },
    );

    stream.addListener(listener);
    await completer.future;
    stream.removeListener(listener); // 避免内存泄漏
  }

  /// 判断 URL 是否有效
  bool _isValidUrl(String url) {
    return url.isNotEmpty && (url.startsWith('http://') || url.startsWith('https://'));
  }
}
