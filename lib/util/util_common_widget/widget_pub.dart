import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/tab_manage/my_collect_like/actors_videos_data.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/image_load_view.dart';
import 'package:flutter_common_base/util/util_routes/single_route_manage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';


typedef StringCallback = Future<void> Function(String param);

mixin class CommonWidgetPub {
  ///红点
  Widget redPointNum(int nNum) {
    if (nNum <= 0) {
      return Container();
    }
    String strRealNum = nNum > 99 ? "99+" : '$nNum';
    return ConstrainedBox(
      constraints: BoxConstraints(minWidth: 17.w, maxWidth: 28.w),
      child: SizedBox(
        child: Container(
          decoration: const BoxDecoration(color: BizColors.rgb100ffffff, borderRadius: BorderRadius.all(Radius.circular(8))),
          padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
          child: Text(strRealNum, textAlign: TextAlign.center, style: TextStyle(fontWeight: FontWeight.w400, color: BizColors.rgb100ffffff, fontSize: 10.sp)),
        ),
      ),
    );
  }


  Widget switchIcon(bool bActive) {
    String strImgIcon = bActive ? AssetsRes.iconSwitchActive : AssetsRes.iconSwitchInActive;
    return SizedBox(width: 32.w,height: 16.w,child: Image.asset(strImgIcon,width: 32.w,height: 16.h,));
  }

  Widget verifyCodePin({TextEditingController? editController, StringCallback? callback,StringCallback? valueChanged,int? length}) {
    return PinCodeTextField(
      inputFormatters: [FilteringTextInputFormatter.allow(RegExp('[0-9]'))],
      textStyle: TextStyle(fontSize: 24.sp, color: BizColors.rgb1000c1018),
      showCursor: true,
      cursorColor: BizColors.mainLineGradientSecond,
      cursorWidth: 2.w,
      // autoFocus: true,
      //进入就弹出键盘
      keyboardType: TextInputType.number,
      blinkWhenObscuring: true,
      //调用数字键盘
      length: length ?? 6,
      obscureText: false,
      animationType: AnimationType.fade,
      // dialogConfig: DialogConfig(
      //   //汉化dialog
      //     dialogTitle: "黏贴验证码",
      //     dialogContent: "确定要黏贴验证码",
      //     affirmativeText: "确定",
      //     negativeText: "取消"),
      //配置dialog
      pinTheme: PinTheme(
        ///形状
        shape: PinCodeFieldShape.box,

        ///圆角半径
        borderRadius: BorderRadius.circular(10),

        ///高
        fieldHeight: 72.w,

        ///宽
        fieldWidth: 48.h,

        ///不要边框
        borderWidth: 0,

        activeColor: Colors.transparent,///已经输入过数字的边框颜色
        activeFillColor: BizColors.rgb100ededed,///已经输入过数字的 填充 颜色
        selectedColor: Colors.transparent,///将要输入的边框颜色
        selectedFillColor: BizColors.rgb100ededed,///将要输入的填充颜色
        inactiveColor: Colors.transparent,///没有输入过数字的 边框颜色
        inactiveFillColor: BizColors.rgb100ededed,///没有输入过数字的填充颜色
      ),
      animationDuration: const Duration(milliseconds: 300),
      enableActiveFill: true,
      controller: editController,
      //TextFiled控制器
      onCompleted: (v) async {
        callback?.call(v);
      },
      onChanged: (value) {
        valueChanged?.call(value);
      },
      beforeTextPaste: (text) {
        return true;
      },
      appContext: Get.context!, //注意需要传入context
    );
  }


  Widget customWidgetAppbar({bool bHideBack = false,Widget? widChild }) {
    return SizedBox(
      width: 1.sw,
      height: BizValues.statusHeight.h + BizValues.appbarHeight.h,
      child: Column(
        children: [
          SizedBox(width: 1.sw, height: BizValues.statusHeight.h),
          Container(
            padding: EdgeInsets.only(left: 6.w, right: 12.w),
            height: BizValues.appbarHeight.h,
            child: Stack(
              alignment: Alignment.centerLeft,
              children: [
                Row(
                  children: <Widget>[
                    ///返回按钮
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Visibility(
                        visible: bHideBack ? false : true,
                        child: SizedBox(
                          width: 22.w,
                          height: 22.w,
                          child: Image.asset(AssetsRes.navLeadingIcon, width: 22.w, height: 22.w),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Flexible(child: widChild ?? const SizedBox.shrink()),
                  ],
                ),
              ],
            ),
          )
        ],
      ),);
  }


  ///统一的appbar
  PreferredSize customAppbar(
      {String strTitleText = "",
      TextStyle? titleTextStyle,
      Widget? centerTitle,
      VoidCallback? onLeadingPress,
      List<Widget> actions = const [],
      double horizontalPadding = 6,
      double horizonRightPadding = 12,
      bool bShowLeading = true,

      bool bLightLeading = false}) {
    return PreferredSize(
      preferredSize: Size.fromHeight(BizValues.statusHeight.h + BizValues.appbarHeight.h),
      child: Container(
        padding: EdgeInsets.only(left: horizontalPadding.w, right: horizonRightPadding.w),
        height: BizValues.statusHeight.h + BizValues.appbarHeight.h,
        width: 1.sw,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: BizValues.statusHeight.h, width: 1.sw),
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  height: BizValues.appbarHeight.h,
                  width: 1.sw,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      /// 左侧按钮（靠左对齐）
                      bShowLeading
                          ? Align(alignment: Alignment.centerLeft, child: SizedBox(width: 22.w, height: 22.w, child: leadingWidget(onPress: onLeadingPress, bLoginPage: bLightLeading)))
                          : const SizedBox.shrink(),
                      /// 右侧按钮（靠右对齐）
                      Align(alignment: Alignment.centerRight, child: Row(mainAxisSize: MainAxisSize.min, children: actions,),
                      ),
                    ],
                  ),
                ),

                /// 标题居中显示
                centerTitle ?? appBarTitleText(strTitleText, titleTextStyle: titleTextStyle),
              ],
            ),
          ],
        ),
      ),
    );
  }

  ///标题栏文本
  Widget appBarTitleText(String title,{TextStyle? titleTextStyle}) {
    return Text(
      title ?? "",
      style: titleTextStyle ?? TextStyle(color: BizColors.rgb1000c1018, fontSize: 18.sp),
    );
  }

  ///导航栏
  Widget leadingWidget({VoidCallback? onPress,bool bLoginPage = false}) {
    return IconButton(
      padding: EdgeInsets.zero,
      onPressed: onPress,
      iconSize: 22.w,
      icon: bLoginPage ? const Icon(Icons.arrow_back_ios_rounded,color: Colors.white54,size: 22,) : Image.asset(AssetsRes.navLeadingIcon, width: 22.w, height: 22.w,),
    );
  }




  ///统一的封面块
  Widget gridItem(
    VideoData itemInfo, {
    EdgeInsetsGeometry? margin,
    TextStyle defTextStyle = const TextStyle(overflow: TextOverflow.ellipsis, color: BizColors.rgb1000c1018, fontSize: 13, fontWeight: FontWeight.w500),
    TextStyle tagTextStyle = const TextStyle(overflow: TextOverflow.ellipsis, color: BizColors.rgb30000000, fontSize: 11, fontWeight: FontWeight.w500),
    int nIndex = -1,
    bool bCollect = false,
  }) {
    return InkWell(
      onTap: () async {
        // bool bCollectDrama = bCollect;
        // if( !bCollectDrama ) {
        //   bCollectDrama = await ApiReqInterface.queryDramaFavStateByParam(ToolsUtil.safeParseInt(itemInfo.strSourceId));
        // }
        SingleRoutePageManage.routeDramaDetailPageVideoData(itemInfo, bCollect: bCollect);
      },
      child: Container(
        width: 109.w,
        height: BizValues.itemBlockHeight.h,
        // color: Colors.red,
        margin: margin,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _imageInfoIcon(itemInfo, nIndex: nIndex),
            SizedBox(height: 2.h,),
            ConstrainedBox(constraints: BoxConstraints(maxWidth: 106.w,maxHeight: 18.h), child: Align(alignment: Alignment.centerLeft, child: Text(itemInfo.strName, style: defTextStyle,),)),
            SizedBox(height: 2.h,),
            SizedBox(height:16.h,child:  Text('${itemInfo.videoCat.strName}·全${itemInfo.nTotalEpisode}集' ,style: tagTextStyle,)),
          ],
        ),
      ),
    );
  }

  Widget _imageInfoIcon(VideoData itemInfo, {int nIndex = -1}) {
    return SizedBox(width: 109.w,height: 153.h,child: Stack(
      children: [
        ImageLoadView(itemInfo.strCover,width: 109.w,height: 153.h,fit: BoxFit.fill,radius: 8.r,),
        Positioned(bottom: 8.h, right: 8.w, child: _iconInfoBottomRight(itemInfo)),
        Positioned(top: -1, left: 0, child: _iconInfoTopLeft(nIndex)),
      ],
    ),);
  }

  Widget _iconInfoTopLeft(int nIndex ) {
    if( nIndex < 0 ) {
      return const SizedBox.shrink();
    }

    String strImg = 'assets/images/tab_manage/tab_video/top_more.png';
    int nRealIndex = nIndex + 1;
    if( nRealIndex <= 3 ) {
      strImg = 'assets/images/tab_manage/tab_video/top_$nRealIndex.png';
    }

    return SizedBox(
        width: 29.w,
        height: 24.h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            ImageLoadView(strImg, width: 29.w, height: 24.h),
            Positioned(left:9.w,top:4.h,child: Text('$nRealIndex', style: TextStyle(fontSize: 13.sp, color: BizColors.rgb100ffffff, fontWeight: FontWeight.w600),)),
          ],
        ));
  }

  Widget _iconInfoBottomRight(VideoData itemInfo) {
    List<Color> listBgColor = [Colors.transparent,Colors.transparent];
    String strTextDesc = "";

    if (itemInfo.bHot) {
      strTextDesc = "爆剧";
      listBgColor = [BizColors.rgb100ff4141, BizColors.rgb100ff4141];
    } else if (itemInfo.bLatest) {
      strTextDesc = "新剧";
      listBgColor = [BizColors.mainColorGreen, BizColors.mainColorGreen];
    } else {
      return const SizedBox.shrink();
    }

    return Container(
      width: 30.w,
      height: 15.h,
      alignment: Alignment.center,
      decoration:
      BoxDecoration(borderRadius: const BorderRadius.all(Radius.circular(4)),gradient: LinearGradient(colors: listBgColor)),
      child: Text(strTextDesc,textAlign:TextAlign.center,style: TextStyle(height:1.2,fontSize: 9.sp,color: Colors.white,fontWeight: FontWeight.w500),),
    );
  }

  ///默认灰色的搜索框
  Widget searchBox({VoidCallback? onPress}) {
    return InkWell(
        onTap: onPress,
        child:
            Image.asset(AssetsRes.iconSearchGrey, width: 22.w, height: 22.w));
  }
}

/// 普通选项条目
class SettingCommon extends StatelessWidget {
  ///最左边的距离
  final double paddingLeft;

  ///最右边的距离
  final double paddingRight;

  ///最开始的图标
  final String? iconImg;
  final double iconHeight;
  final double iconWidth;
  final double symmetricHeight;

  ///图标后面的文字
  final String? title;
  final double? titlePaddingLeft;
  final Color? titleColor;
  final double? titleFontSize;
  final FontWeight? titleFontWeight;

  ///后面的内容
  final String? content;
  final double? contentFontSize;
  final FontWeight? contentFontWeight;
  final Color? contentColor;
  final VoidCallback? onPressed;
  final bool isLast;
  final Widget? contentIcon;
  final double? contentMaxWidth;
  final Widget? navWidget;

  final double? arrowMarginRight;
  final double? divPaddingLeft;
  final double? divPaddingRight;
  final Color? itemBgColor;
  final Color? mainBgColor;

  const SettingCommon(
      {super.key,
        this.paddingLeft = 16,
        this.paddingRight = 16,
        this.iconHeight = 20,
        this.iconWidth = 20,
        this.symmetricHeight = 16,
        this.iconImg,
        this.title,
        this.titleColor = BizColors.rgb1000c1018,
        this.titleFontSize = 15,
        this.titleFontWeight = FontWeight.w500,
        this.titlePaddingLeft = 12,
        this.content,
        this.contentIcon,
        this.contentMaxWidth,
        this.onPressed,
        this.isLast = true,
        this.navWidget,
        this.itemBgColor,
        this.arrowMarginRight = 8,
        this.divPaddingLeft = 0,
        this.divPaddingRight = 0,
        this.contentColor = BizColors.rgb100939eb3,
        this.contentFontSize = 14,
        this.contentFontWeight = FontWeight.w500,
        this.mainBgColor});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: mainBgColor ?? Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        child: Container(
          padding: EdgeInsets.only(left: paddingLeft, right: paddingRight),
          child: Column(
            children: <Widget>[
              Container(
                color: itemBgColor ?? Colors.transparent,
                padding: EdgeInsets.symmetric(vertical: symmetricHeight.h),
                child: IntrinsicHeight(
                  child: Row(
                    children: <Widget>[
                      ///开头的icon
                      Visibility(visible: iconImg?.isNotEmpty ?? false, child: Image.asset(iconImg ?? "", width: iconWidth.w, height: iconWidth == iconHeight ? iconHeight.w: iconHeight.h,)),
                      ///紧挨着的标题
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(left: titlePaddingLeft ?? 0),
                          child: Text(title ?? "", style: TextStyle(fontSize: titleFontSize ?? 14.sp, fontWeight: titleFontWeight ?? FontWeight.w500, color: titleColor ?? BizColors.rgb1000c1018),
                          ),
                        ),
                      ),
                      contentIcon ?? Container(),
                      ///内容区域
                      Container(
                        constraints: BoxConstraints(maxWidth: contentMaxWidth ?? 300),
                        margin: EdgeInsets.symmetric(horizontal: 5.w),
                        child: Text(content ?? "", maxLines: 1, overflow:TextOverflow.ellipsis,style: TextStyle(fontSize: contentFontSize ?? 14.sp, color: contentColor ?? BizColors.rgb100ff4141,),),
                      ),
                      ///导航箭头
                      navWidget ??
                          Container(
                            margin: EdgeInsets.only(left: 5.w, right: arrowMarginRight ?? 0),
                            child: Image.asset(AssetsRes.navSmallRightIcon, width: 16.w, height: 16.w,),
                          ),
                    ],
                  ),
                ),
              ),
              isLast
                  ? Container()
                  : Container(
                      padding: EdgeInsets.only(left: divPaddingLeft ?? 0, right: divPaddingRight ?? 0),
                      child: Container(height: 0.5, color: Colors.white.withAlpha(12),),
                    )
            ],
          ),
        ),
      ),
    );
  }
}
