import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:get/get.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_language_strings.dart';

/// 用于外部感知和控制刷新状态
class RefreshStateController {
  bool hasMissedTrigger = false;
  bool isLoading = false;
}

class SmartRefresherWidget extends StatefulWidget {
  final Widget child;
  final RefreshController controller;
  final Future<void> Function()? onPullDownRefresh;
  final Future<void> Function()? onPullUpLoading;
  final Widget? loadOverItemBody;
  final double footerHeight;
  final TextStyle? textStyle;
  final RefreshStateController? stateController;

  const SmartRefresherWidget(
      this.child,
      this.controller, {
        super.key,
        this.onPullDownRefresh,
        this.onPullUpLoading,
        this.loadOverItemBody,
        this.footerHeight = 30,
        this.textStyle,
        this.stateController,
      });

  @override
  State<SmartRefresherWidget> createState() => _SmartRefresherWidgetState();
}

class _SmartRefresherWidgetState extends State<SmartRefresherWidget> {
  late RefreshStateController _state;

  @override
  void initState() {
    super.initState();
    _state = widget.stateController ?? RefreshStateController();
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle style = widget.textStyle ?? TextStyle(fontSize: 10.sp, color: BizColors.rgb100999999);

    return NotificationListener<ScrollNotification>(
      onNotification: _handleScrollNotification,
      child: SmartRefresher(
        controller: widget.controller,
        enablePullDown: widget.onPullDownRefresh != null,
        enablePullUp: widget.onPullUpLoading != null,
        onRefresh: widget.onPullDownRefresh,
        onLoading: _handleLoading,
        header: CustomHeader(
          builder: (context, mode) {
            Widget body;
            switch (mode) {
              case RefreshStatus.idle:
                body = Text(LanStrings.dataPullDownRefresh.tr, style: style);
                break;
              case RefreshStatus.canRefresh:
                body = Text(LanStrings.releaseToRefresh.tr, style: style);
                break;
              case RefreshStatus.refreshing:
                body = Text(LanStrings.loading.tr, style: style);
                break;
              case RefreshStatus.failed:
                body = Text(LanStrings.loadFailedRetry.tr, style: style);
                break;
              case RefreshStatus.completed:
              default:
                body = Text(LanStrings.refreshComplete.tr, style: style);
                break;
            }
            return SizedBox(height: 55.h, child: Center(child: body));
          },
        ),
        footer: CustomFooter(
          height: widget.footerHeight.h,
          builder: (context, mode) {
            final footerStatus = widget.controller.footerStatus;
            Widget body;

            if (_state.isLoading || footerStatus == LoadStatus.loading) {
              body = body = Text(LanStrings.loading.tr, style: style);
            } else if (_state.hasMissedTrigger) {
              body = Text("等待加载触发…", style: style);
            } else if (footerStatus == LoadStatus.idle) {
              body = Text(LanStrings.pullUpLoadMore.tr, style: style);
            } else if (footerStatus == LoadStatus.failed) {
              body = Text(LanStrings.loadFailedTapRefresh.tr, style: style);
            } else if (footerStatus == LoadStatus.canLoading) {
              body = Text(LanStrings.releaseToLoadMore.tr, style: style);
            } else if (footerStatus == LoadStatus.noMore) {
              body = widget.loadOverItemBody ??
                  Text(LanStrings.noMoreData.tr, style: style);
            } else {
              body = const SizedBox.shrink();
            }
            return SizedBox(
              height: widget.footerHeight.h,
              child: Align(alignment: Alignment.topCenter, child: body),
            );
          },
        ),
        child: widget.child,
      ),
    );
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (widget.onPullUpLoading == null) return false;

    if (_state.isLoading) {
      // 加载中，不响应滚动触发加载，避免重复加载
      return false;
    }

    final metrics = notification.metrics;

    if ((notification is ScrollEndNotification ||
        notification is UserScrollNotification) &&
        metrics.pixels >= metrics.maxScrollExtent - 30 &&
        widget.controller.footerStatus == LoadStatus.idle) {
      _state.hasMissedTrigger = true;
      _maybeTriggerLoadMore();
    }

    return false;
  }

  void _handleLoading() {
    _maybeTriggerLoadMore();
  }

  void _maybeTriggerLoadMore() {
    if (_state.isLoading || widget.onPullUpLoading == null) return;

    _state.isLoading = true;
    _state.hasMissedTrigger = false;

    widget.onPullUpLoading!().whenComplete(() {
      _state.isLoading = false;
    });
  }
}
