import 'package:flutter/material.dart';
import 'package:flutter_common_base/data_model/user_info/login_type_info.dart';
import 'package:flutter_common_base/util/util_common_widget/normal_page_builder/normal_page_base_controller.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

abstract class NormalPageViewBase<T extends NormalPageControllerBase> extends StatefulWidget {
  final bool isGlobal;
  final String? tag;
  final Map<String,dynamic>? mapParam;

  bool resizeToAvoidBottomInset = false;

  NormalPageViewBase({super.key, required this.isGlobal, this.tag, this.mapParam});
}

abstract class NormalPageBaseViewState<T extends NormalPageControllerBase> extends State<NormalPageViewBase<T>> with CommonWidgetPub{

  late T controller;
  T createController();

  ///返回键是否可以返回
  bool bCanRet = true;

  @override
  void initState() {
    super.initState();

    /// 在视图初始化时，进行控制器的注册和初始化
    if (widget.isGlobal) {
      /// 全局模式：如果控制器没有注册过，就创建
      if (!Get.isRegistered<T>(tag: widget.tag)) {
        controller = Get.put<T>(createController(), tag: widget.tag,);
      } else {
        controller = Get.find<T>(tag: widget.tag);
      }
    } else {
      /// 局部模式：每次都创建一个新的控制器
      controller = Get.put<T>(createController(), tag: widget.tag);
    }
    bCanRet = widget.mapParam?["fromSrc"] != PageFromReason.fromReasonBind;
  }

  @override
  void dispose() {
    /// 局部模式下销毁 Controller
    if (!widget.isGlobal) {
      Get.delete<T>(tag: widget.tag); /// 删除局部控制器
    }
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return PopScope(canPop: bCanRet, child: SafeArea(top: false, bottom: false, child: mainBody()));
  }

  /// 子类需要实现这个方法来构建 UI
  Widget mainBody() {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        mainBg(),
        mainContent(),
      ],
    );
  }

  ///背景
  Widget mainBg() {
    return Container(width: 1.sw, height: 1.sh, decoration: const BoxDecoration(
      // gradient: LinearGradient(
      //   colors: [
      //     Color(0xFFFFF7FA),
      //     Color(0xFFFDECF3),
      //     Color(0xFFF5E6F5),
      //     Color(0xFFF0F2F5),
      //   ],
      //   begin: Alignment.centerLeft,
      //   end: Alignment.centerRight,
      // ),
    ),);
  }

  Widget mainContent() {
    return SafeArea(
      top: false,
      bottom: false,
      child: Scaffold(
        extendBody: true,
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
        appBar: appbar(),
        body: bodyContentWidget(),
      ),
    );
  }
  PreferredSizeWidget appbar();

  Widget bodyContentWidget();

}