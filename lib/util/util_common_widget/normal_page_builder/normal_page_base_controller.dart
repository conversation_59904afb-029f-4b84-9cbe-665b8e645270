import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:get/get.dart';

abstract class NormalPageControllerBase extends FullLifeCycleController with FullLifeCycleMixin {

  ///数据加载遮罩
  ViewLoaderState dataLoadState = ViewLoaderState();

  @override
  void onInit() {
    super.onInit();
  }


  @override
  void onDetached() {
  }

  @override
  void onHidden() {
  }

  @override
  void onInactive() {
  }

  @override
  void onPaused() {
  }

  @override
  void onResumed() {
  }
}