import 'package:flutter/widgets.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_state_management/base_controller.dart';

abstract class NormalPageControllerBase extends BaseController
    with WidgetsBindingObserver {
  ///数据加载遮罩
  ViewLoaderState dataLoadState = ViewLoaderState();

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        onResumed();
        break;
      case AppLifecycleState.inactive:
        onInactive();
        break;
      case AppLifecycleState.paused:
        onPaused();
        break;
      case AppLifecycleState.detached:
        onDetached();
        break;
      case AppLifecycleState.hidden:
        onHidden();
        break;
    }
  }

  // 子类可以重写这些方法
  void onDetached() {}
  void onHidden() {}
  void onInactive() {}
  void onPaused() {}
  void onResumed() {}
}
