
import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/assets_res/assets_ref.dart';
import 'package:lottie/lottie.dart';

class PlaceholderPage extends StatefulWidget {

  const PlaceholderPage({super.key});

  @override
  PlaceholderPageState createState() => PlaceholderPageState();
}

class PlaceholderPageState extends State<PlaceholderPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: SizedBox(
            width: 120,
            height: 120,
            child: Lottie.asset(AssetsRes.lottieAdLoading, fit: BoxFit.fill)),
      ),
    );
  }
}
