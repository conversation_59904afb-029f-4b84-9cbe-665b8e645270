import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_routes/route_config/app_paths.dart';
import 'package:get/get.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';

class ToolsUtil {

  static Uuid uuidGen = const Uuid();


  static const Map<String, String> _iPhoneModels = {
    // iPhone 1st Gen
    "iPhone1,1": "iPhone",
    "iPhone1,2": "iPhone 3G",

    // iPhone 3GS
    "iPhone2,1": "iPhone 3GS",

    // iPhone 4
    "iPhone3,1": "iPhone 4 (GSM)",
    "iPhone3,2": "iPhone 4 (GSM Rev A)",
    "iPhone3,3": "iPhone 4 (CDMA)",

    // iPhone 4s
    "iPhone4,1": "iPhone 4s",

    // iPhone 5
    "iPhone5,1": "iPhone 5 (GSM)",
    "iPhone5,2": "iPhone 5 (Global)",
    "iPhone5,3": "iPhone 5c (GSM)",
    "iPhone5,4": "iPhone 5c (Global)",

    // iPhone 5s
    "iPhone6,1": "iPhone 5s (GSM)",
    "iPhone6,2": "iPhone 5s (Global)",

    // iPhone 6 / 6 Plus
    "iPhone7,1": "iPhone 6 Plus",
    "iPhone7,2": "iPhone 6",

    // iPhone 6s / 6s Plus
    "iPhone8,1": "iPhone 6s",
    "iPhone8,2": "iPhone 6s Plus",
    "iPhone8,4": "iPhone SE (1st Gen)",

    // iPhone 7 / 7 Plus
    "iPhone9,1": "iPhone 7",
    "iPhone9,2": "iPhone 7 Plus",
    "iPhone9,3": "iPhone 7",
    "iPhone9,4": "iPhone 7 Plus",

    // iPhone 8 / 8 Plus / X
    "iPhone10,1": "iPhone 8",
    "iPhone10,2": "iPhone 8 Plus",
    "iPhone10,3": "iPhone X (Global)",
    "iPhone10,4": "iPhone 8",
    "iPhone10,5": "iPhone 8 Plus",
    "iPhone10,6": "iPhone X (GSM)",

    // iPhone XR / XS / XS Max
    "iPhone11,2": "iPhone XS",
    "iPhone11,4": "iPhone XS Max",
    "iPhone11,6": "iPhone XS Max (China)",
    "iPhone11,8": "iPhone XR",

    // iPhone 11 系列
    "iPhone12,1": "iPhone 11",
    "iPhone12,3": "iPhone 11 Pro",
    "iPhone12,5": "iPhone 11 Pro Max",

    // iPhone SE 2nd
    "iPhone12,8": "iPhone SE (2nd Gen)",

    // iPhone 12 系列
    "iPhone13,1": "iPhone 12 mini",
    "iPhone13,2": "iPhone 12",
    "iPhone13,3": "iPhone 12 Pro",
    "iPhone13,4": "iPhone 12 Pro Max",

    // iPhone 13 系列
    "iPhone14,2": "iPhone 13 Pro",
    "iPhone14,3": "iPhone 13 Pro Max",
    "iPhone14,4": "iPhone 13 mini",
    "iPhone14,5": "iPhone 13",

    // iPhone SE 3rd
    "iPhone14,6": "iPhone SE (3rd Gen)",

    // iPhone 14 系列
    "iPhone14,7": "iPhone 14",
    "iPhone14,8": "iPhone 14 Plus",
    "iPhone15,2": "iPhone 14 Pro",
    "iPhone15,3": "iPhone 14 Pro Max",

    // iPhone 15 系列
    "iPhone15,4": "iPhone 15",
    "iPhone15,5": "iPhone 15 Plus",
    "iPhone16,1": "iPhone 15 Pro",
    "iPhone16,2": "iPhone 15 Pro Max",
  };

  /// 获取 iPhone 型号
  static String getModel(String machineName) {
    return _iPhoneModels[machineName] ?? machineName;
  }

  ///拉起外部页面
  static Future<void> launchMyUrl(String strUrl) async {
    try {
      launchUrl(Uri.parse(strUrl));
    } catch (e) {

    }
  }


  static void updateNavBarStyle({required bool isDarkBackground}) {
    final style = SystemUiOverlayStyle(
      systemNavigationBarColor:
      isDarkBackground ? BizColors.rgb1000c1018 : Colors.white,
      systemNavigationBarIconBrightness:
      isDarkBackground ? Brightness.light : Brightness.dark,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemChrome.setSystemUIOverlayStyle(style);
    });
  }


  static void updateNavigationBarStyle({required Color backgroundColor, required Brightness iconBrightness,}) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(systemNavigationBarColor: backgroundColor, systemNavigationBarIconBrightness: iconBrightness,),);
  }

  static void onTabChanged(bool bRecommend) {
    if (!bRecommend) {
      /// 页面是浅色背景，设置黑色图标
      updateNavigationBarStyle(backgroundColor: Colors.white, iconBrightness: Brightness.dark,);
    } else {
      /// 页面是深色背景，设置白色图标
      updateNavigationBarStyle(backgroundColor: BizColors.rgb1000c1018, iconBrightness: Brightness.light,);
    }
  }

  ///从附加字段中拿到来源
  static String getExtraFromSrc(String strExtraData) {
    String strFrom = "";
    try {
      List<String> result = strExtraData.split(BizStrings.adExtraDiv);
      if (result.isNotEmpty) {
        strFrom = result[0];
      }
    } catch (e) {
      // TODO
    }
    return strFrom;
  }

  static String genTimeNowMilliseconds() {
    String stNow = '${DateTime.now().millisecondsSinceEpoch}';
    return stNow;
  }

  static int roundToInt(double value) {
    return value.isNegative ? -((value.abs() + 0.5).toInt()) : (value + 0.5).toInt();
  }

  static int safeParseInt(String s, {int radix = 10}) {
    try {
      return int.parse(s.isEmpty ? "0" : s, radix: radix);
    } catch (e) {
      debugPrint("int.parse($s) exception!!!!");
    }
    return 0;
  }

  ///复制到剪切板
  static void copyToClipboard({String? text = ""}) {
    if (text == null || text.isEmpty) return;
    Clipboard.setData(ClipboardData(text: text));
  }

  ///从剪切版中读取数据
  static Future<String> getClipboardText() async {
    String text = '';
    ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    text = data?.text ?? "";
    return text;
  }

  ///收起键盘
  static void hideKeyboard() {
    FocusScope.of(Get.context!).unfocus();
  }

  ///调起url
  static Future<bool> launchMyUrlString(String strUrl) async {
    try {
      return await ToolsUtil.launchMyUrlUri(Uri.parse(strUrl));
    } catch (e) {
      return false;
    }
  }

  ///调起url uri
  static Future<bool> launchMyUrlUri(Uri urlUri) async {
    try {
      return await launchUrl(urlUri);
    } catch (e) {
      return false;
    }
  }

  ///base64编码
  static String base64Encode(String data) {
    String digest = base64.encode(utf8.encode(data));
    return digest;
  }

  /// Base64解码
  static String base64Decode(String? data) {
    if (data?.isEmpty ?? true) {
      return "";
    }
    var decoded = base64.decode(data ?? "");
    String result = utf8.decode(decoded);
    return result;
  }

  ///生成一个随机值
  String genUuId() {
    return uuidGen.v4();
  }

  ///计算年龄

  static int calculateAge(String birthDateStr, {String format = 'yyyy-MM-dd'}) {
    try {
      final birthDate = DateTime.parse(birthDateStr);
      final today = DateTime.now();
      int age = today.year - birthDate.year;
      /// 如果今年还没过生日，年龄减 1
      if (today.month < birthDate.month || (today.month == birthDate.month && today.day < birthDate.day)) {
        age--;
      }

      return age;
    } catch (e) {
      /// 字符串格式不对或解析失败
      return -1;
    }
  }

  ///是否是合法的中国大陆手机号
  static bool isValidPhone(String input) {
    final RegExp phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    try {
      return phoneRegex.hasMatch(input);
    } catch (e) {
      return false;
    }
  }

  /// 判断是否是六位纯数字
  static bool isSixDigitNumber(String input) {
    final RegExp sixDigitRegex = RegExp(r'^\d{6}$');
    try {
      return sixDigitRegex.hasMatch(input);
    } catch (e) {
      return false;
    }
  }

  static bool isNet(String path) {
    return path.toLowerCase().startsWith("http");
  }

  static int getColorHexFromStr(String colorStr, {String alpha = "FF"}) {
    colorStr = colorStr.replaceAll("#", "");
    if (colorStr.length == 6) {
      colorStr = alpha + colorStr;
    }
    int val = 0;
    int len = colorStr.length;
    for (int i = 0; i < len; i++) {
      int hexDigit = colorStr.codeUnitAt(i);
      if (hexDigit >= 48 && hexDigit <= 57) {
        val += (hexDigit - 48) * (1 << (4 * (len - 1 - i)));
      } else if (hexDigit >= 65 && hexDigit <= 70) {
        // A..F
        val += (hexDigit - 55) * (1 << (4 * (len - 1 - i)));
      } else if (hexDigit >= 97 && hexDigit <= 102) {
        // a..f
        val += (hexDigit - 87) * (1 << (4 * (len - 1 - i)));
      } else {
        throw const FormatException(
            "An error occurred when converting a color");
      }
    }
    return val;
  }

  ///根据输入的汉字输出拼音的首字母
  static String getPinyin(String text, {bool uppercase = false, bool fullPinyin = false, bool all = false}) {
    if (text.trim().isEmpty) return '';
    String result = '';
    if (all) {
      result = fullPinyin ? PinyinHelper.getPinyin(text, separator: '') : PinyinHelper.getShortPinyin(text);
    } else {
      String firstChar = text.characters.first;
      result = fullPinyin ? PinyinHelper.getPinyin(firstChar) : PinyinHelper.getShortPinyin(firstChar);
    }

    return uppercase ? result.toUpperCase() : result;
  }

  static Future<void> initYouthMode() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.youthModeOpenState();
    bool bOpen = mapResult["data"] ?? false;
    Get.offAllNamed(Routes.homeMain, arguments: {"youthMode": bOpen});
  }

  ///比较两上版本号大小
  static int compareVersion(String versionLocal, String versionRemote) {
    List<String> v1Parts = versionLocal.split('.');
    List<String> v2Parts = versionRemote.split('.');
    int maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;

    for (int i = 0; i < maxLength; i++) {
      int v1 = i < v1Parts.length ? int.tryParse(v1Parts[i]) ?? 0 : 0;
      int v2 = i < v2Parts.length ? int.tryParse(v2Parts[i]) ?? 0 : 0;

      if (v1 > v2) return 1;
      if (v1 < v2) return -1;
    }

    return 0;
  }
}
