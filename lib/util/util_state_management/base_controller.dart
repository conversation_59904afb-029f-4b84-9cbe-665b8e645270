import 'package:flutter/foundation.dart';

/// 基础控制器类，替代GetX Controller
/// 使用Provider的ChangeNotifier作为基础状态管理
abstract class BaseController extends ChangeNotifier {
  bool _disposed = false;
  
  /// 是否已销毁
  bool get disposed => _disposed;
  
  /// 初始化方法，子类可以重写
  void onInit() {
    // 子类可以重写此方法进行初始化
  }
  
  /// 销毁方法，子类可以重写
  void onClose() {
    // 子类可以重写此方法进行清理
  }
  
  /// 更新UI，替代GetX的update()方法
  void update([List<String>? ids]) {
    if (!_disposed) {
      notifyListeners();
    }
  }
  
  /// 销毁控制器
  @override
  void dispose() {
    if (!_disposed) {
      _disposed = true;
      onClose();
      super.dispose();
    }
  }
}

/// 状态管理工具类
class StateManager {
  static final Map<String, BaseController> _controllers = {};
  
  /// 注册控制器 (替代 Get.put)
  static T put<T extends BaseController>(T controller, {String? tag}) {
    String key = _getKey<T>(tag);
    _controllers[key] = controller;
    controller.onInit();
    return controller;
  }
  
  /// 获取控制器 (替代 Get.find)
  static T find<T extends BaseController>({String? tag}) {
    String key = _getKey<T>(tag);
    BaseController? controller = _controllers[key];
    if (controller == null) {
      throw Exception('Controller of type $T not found. Make sure to call put() first.');
    }
    return controller as T;
  }
  
  /// 删除控制器 (替代 Get.delete)
  static bool delete<T extends BaseController>({String? tag}) {
    String key = _getKey<T>(tag);
    BaseController? controller = _controllers[key];
    if (controller != null) {
      controller.dispose();
      _controllers.remove(key);
      return true;
    }
    return false;
  }
  
  /// 检查控制器是否存在
  static bool isRegistered<T extends BaseController>({String? tag}) {
    String key = _getKey<T>(tag);
    return _controllers.containsKey(key);
  }
  
  /// 清理所有控制器
  static void reset() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
  }
  
  /// 生成控制器的唯一键
  static String _getKey<T>(String? tag) {
    return tag != null ? '${T.toString()}_$tag' : T.toString();
  }
}

/// 响应式变量包装类，替代GetX的Rx变量
class Reactive<T> {
  T _value;
  final List<VoidCallback> _listeners = [];
  
  Reactive(this._value);
  
  /// 获取值
  T get value => _value;
  
  /// 设置值
  set value(T newValue) {
    if (_value != newValue) {
      _value = newValue;
      _notifyListeners();
    }
  }
  
  /// 添加监听器
  void listen(VoidCallback listener) {
    _listeners.add(listener);
  }
  
  /// 移除监听器
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }
  
  /// 通知所有监听器
  void _notifyListeners() {
    for (var listener in _listeners) {
      listener();
    }
  }
  
  /// 销毁
  void dispose() {
    _listeners.clear();
  }
}

/// 扩展方法，方便创建响应式变量
extension ReactiveExtension<T> on T {
  Reactive<T> get obs => Reactive<T>(this);
}
