import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'base_controller.dart';

/// 替代GetBuilder的组件
/// 用于监听特定控制器的状态变化
class ControllerBuilder<T extends BaseController> extends StatelessWidget {
  final Widget Function(BuildContext context, T controller) builder;
  final String? id;
  final bool Function(T previous, T current)? condition;

  const ControllerBuilder({
    Key? key,
    required this.builder,
    this.id,
    this.condition,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<T>(
      builder: (context, controller, child) {
        return builder(context, controller);
      },
    );
  }
}

/// 替代Obx的组件
/// 用于监听响应式变量的变化
class ReactiveBuilder<T> extends StatefulWidget {
  final Reactive<T> reactive;
  final Widget Function(T value) builder;

  const ReactiveBuilder({
    Key? key,
    required this.reactive,
    required this.builder,
  }) : super(key: key);

  @override
  State<ReactiveBuilder<T>> createState() => _ReactiveBuilderState<T>();
}

class _ReactiveBuilderState<T> extends State<ReactiveBuilder<T>> {
  @override
  void initState() {
    super.initState();
    widget.reactive.listen(_onValueChanged);
  }

  @override
  void dispose() {
    widget.reactive.removeListener(_onValueChanged);
    super.dispose();
  }

  void _onValueChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(widget.reactive.value);
  }
}

/// 多Provider包装器，用于在应用根部注册多个控制器
class MultiControllerProvider extends StatelessWidget {
  final List<BaseController> controllers;
  final Widget child;

  const MultiControllerProvider({
    Key? key,
    required this.controllers,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: controllers
          .map((controller) => ChangeNotifierProvider.value(value: controller))
          .toList(),
      child: child,
    );
  }
}

/// 单个控制器Provider包装器
class ControllerProvider<T extends BaseController> extends StatelessWidget {
  final T controller;
  final Widget child;
  final bool lazy;

  const ControllerProvider({
    Key? key,
    required this.controller,
    required this.child,
    this.lazy = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (lazy) {
      return ChangeNotifierProvider.value(
        value: controller,
        child: child,
      );
    } else {
      return ChangeNotifierProvider(
        create: (_) => controller,
        child: child,
      );
    }
  }
}

/// 控制器选择器，用于只监听控制器的特定属性
class ControllerSelector<T extends BaseController, R> extends StatelessWidget {
  final R Function(T controller) selector;
  final Widget Function(BuildContext context, R value, Widget? child) builder;
  final Widget? child;

  const ControllerSelector({
    Key? key,
    required this.selector,
    required this.builder,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Selector<T, R>(
      selector: (context, controller) => selector(controller),
      builder: builder,
      child: child,
    );
  }
}

/// 扩展方法，方便获取控制器
extension BuildContextExtension on BuildContext {
  /// 获取控制器 (替代 Get.find)
  T controller<T extends BaseController>() {
    return Provider.of<T>(this, listen: false);
  }

  /// 监听控制器 (替代 GetBuilder)
  T watchController<T extends BaseController>() {
    return Provider.of<T>(this, listen: true);
  }
}
