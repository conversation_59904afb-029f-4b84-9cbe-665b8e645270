import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';

class AdDlgItemData {
  final int nAdId;
  final String strTag;

  AdDlgItemData({this.nAdId = 0, this.strTag = ""});
}

class UnLockDlgMan {
  static List<String> listDlg = [];

  static void unLockRewardDlg(String strExtra) {
    if (!listDlg.contains(strExtra)) {
      return;
    }

    // List<String> listData = ToolsUtil.getExtraFromSrc(strExtra);
  }
}
