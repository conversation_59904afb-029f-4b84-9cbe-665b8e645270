import 'package:flutter/material.dart';
import 'package:flutter_adcontent/flutter_adcontent.dart';
import 'package:flutter_common_base/common/biz_values/biz_ad_config.dart';
import 'package:flutter_common_base/data_model/ad/ad_config.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/tab_manage/tab_fu_li.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:get/get.dart';

import 'util_unlock_dialog/unlock_dlg_manage.dart';

class CsjUnionUtil {
  static bool bInit = false;

  static ProVideoController? videoController;
  static ProVideoController? novelController;

  static VideoContentModel adSettings = VideoContentModel();

  static Future<void> init() async {
    setAdEvent();
    String strAppId = CsjUnionUtil.adSettings.strAppId;
    if( strAppId.isEmpty ) {
      strAppId = GetPlatform.isIOS ? BizAdConfig.appIdIOS : BizAdConfig.appId;
    }

    ///重新赋值
    BizAdConfig.uniAppId = strAppId;
    bool bRet = await initUnion(strAppId: strAppId);
    if( !bRet ) {
      bRet = await initUnion(strAppId: strAppId);
    }
    bInit = bRet;
    debugPrint("广告初始化：$bInit");
    bRet = await initVideo();
  }

  /// 设置广告监听
  static Future<void> setAdEvent() async {
    FlutterGromoreAds.onEventListener((event) {
      debugPrint('onEventListener adId:${event.adId} ++++action:${event.action}');
      commonEventBus.fire(AdSelfActionEvent(nAdId: ToolsUtil.safeParseInt(event.adId), strAction: event.action));
      if (event is AdErrorEvent) {
        // 错误事件
        debugPrint('onEventListener errCode:${event.errCode} errMsg:${event.errMsg}');
      } else if (event is AdRewardEvent) {
        _setRewardAct(event);
        /// 激励事件
        debugPrint('onEventListener rewardType:${event.rewardType} rewardVerify:${event.rewardVerify} rewardAmount:${event.rewardAmount} rewardName:${event.rewardName} errCode:${event.errCode} errMsg:${event.errMsg} customData:${event.customData} userId:${event.userId}');
      } else if (event is AdEcpmEvent) {
        videoController?.setCustomAdOnShow(event.ecpm);
        novelController?.setCustomAdOnShow(event.ecpm);
        /// 广告价格信息事件
        debugPrint('onEventListener ecpm:${event.ecpm} channel:${event.channel} subChannel:${event.subChannel} sdkName:${event.sdkName} scenarioId:${event.scenarioId} errMsg:${event.errMsg}');
        /// 添加记录
        /// AdsConfig.addEcpmEvent(event);
      } else if (event.action == AdEventAction.onAdLoaded) {
        // 广告加载成功
        // 判断是开屏代码位 id，如果是预加载则展示, preload: true 一定要传
        // if (event.adId == AdsConfig.splashId) {
        //   // FlutterGromoreAds.showSplashAd(
        //   //   AdsConfig.splashId,
        //   //   logo: AdsConfig.logo,
        //   //   preload: true,
        //   // );
        //   // 已缓存广告
        //   // AdsConfig.splashAdPreload = true;
        // }
      } else if (event.action == AdEventAction.onAdClosed) {
        /// 这里写关闭的逻辑
        // if (event.adId == AdsConfig.splashId) {
        //   // 未缓存广告
        //   AdsConfig.splashAdPreload = false;
        // }
      } else {
        if (event.action == AdEventAction.onAdClicked) {
          debugPrint('onEventListener onAdClicked 广告点击了');
          // AdsConfig.isShowHotStartAd = false;
          // 更新广告点击时间
          // AdsConfig.updateAdClickTime(event);
        }
      }
    });
  }

  // static void _setEcpm()


  static void _setRewardAct(AdRewardEvent event) {
    String strFrom = ToolsUtil.getExtraFromSrc(event.customData ?? "");
    if (strFrom == BizAdConfig.strAdSrcDrama) {
      videoController?.setCustomAdOnReward(event.rewardVerify);
    } else if (strFrom == BizAdConfig.strAdSrcNovel) {
      novelController?.setCustomAdOnReward(event.rewardVerify);
    }

    /// 激励事件
    debugPrint('onEventListener rewardType:${event.rewardType} rewardVerify:${event.rewardVerify} rewardAmount:${event.rewardAmount} rewardName:${event.rewardName} errCode:${event.errCode} errMsg:${event.errMsg} customData:${event.customData} userId:${event.userId}');
    if( event.rewardVerify ) {
      String strFrom = ToolsUtil.getExtraFromSrc(event.customData ?? "");
      commonEventBus.fire(AdRewardReceivedEvent(
          nAdId: ToolsUtil.safeParseInt(event.adId),
          strSrcFrom: strFrom,
          strEcpm: event.ecpm ?? "",
          strTransId: event.transId ?? "",
          strExtraData: event.customData ?? ""));
    }
  }

  ///初始化广告配置
  static Future<bool> initUnion({String strAppId = BizAdConfig.appId, String strAppName = BizAdConfig.appName,bool bDebug = true}) async {
    bool adResult = await FlutterGromoreAds.initAd(strAppId, useMediation: true);
    return adResult;
  }

  static Future<bool> initVideo() async {
    bool result = await FlutterAdcontent.init(
      initSkit: true, // 初始化短剧
      initVideo: false, // 初始化小视频
      initNovel: true, // 初始化短故事(小说)
      settingFile: BizAdConfig.settingFile, // 配置文件，不需要传.json
    );
    return result;
  }

  static void preloadFuLiAd() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getFuLiPageAdLists();
    if( mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return;
    }
    ///预加载广告
    List<AdBannerData> listAds = [];
    List jsonList = mapResult['data'] ?? [];
    List<AdBannerData> list = jsonList.map((e) => AdBannerData.fromJson(e)).toList();
    listAds.addAll(list);

    if( listAds.isEmpty ) {
      return;
    }
    ///并行加载的广告位数
    int nSyncLoadCnt = listAds.length;
    ///抽离出广告位
    List<String> listAdIds = [];
    for (int nIndex = 0; nIndex < listAds.length; nIndex++) {
      AdBannerData adItemInfo = listAds[nIndex];
      listAdIds.add(adItemInfo.strSourceId);
    }

    ///进行预加载
    String strUserId = '${UserInfoUtil.getUserInfo().id}';
    bool bRet = await FlutterGromoreAds.preload(rewardPosids: listAdIds, concurrent: nSyncLoadCnt, interval: 1, userId: strUserId);
    int nDebug = 0;
  }

  ///加载激励广告
  static Future<bool> showRewardVideoAds(
    String posId, {
    int orientation = 1,
    String customData = "FlutterAds",
    String userId = "userId",
  }) async {
    bool bRet = await FlutterGromoreAds.showRewardVideoAd(posId, userId: userId, customData: customData);
    return bRet;
  }
}
