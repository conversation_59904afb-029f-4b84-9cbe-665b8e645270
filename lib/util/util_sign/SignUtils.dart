import 'dart:core' as $core;
import 'dart:convert';
import 'dart:core';
import 'package:crypto/crypto.dart';

class SignUtils {
  static const String _secretKey = "62852FD47917659956E7578F52EB1DAF";

  /// 生成签名
  ///
  /// [headers]：请求头（注意不要包含 Sign 本身）；
  /// [params]：请求参数（可为 query 参数或表单数据）；
  /// 返回：签名字符串（大写 MD5）
  static String generateSignature({
    Map<String, String> headers = const {},
    Map<String, List<String>> params = const {},
  }) {
    final Map<String, List<String>> dataMap = {};

    // 加入 headers，前缀加 Header_
    headers.forEach((key, value) {
      if (key.toLowerCase() != 'sign') {
        dataMap['Header_$key'] = [value];
      }
    });

    // 加入参数，前缀加 Parameter_
    params.forEach((key, values) {
      if (values.isNotEmpty) {
        dataMap['Parameter_$key'] = values;
      }
    });

    // 排序所有 key
    final sortedKeys = dataMap.keys.toList()..sort();

    // 拼接 key=value&key2=value2...
    final parts = <String>[];
    for (var key in sortedKeys) {
      final values = dataMap[key]!;
      final joined = values.join(','); // 多值用逗号连接
      parts.add('$key=$joined');
    }

    parts.add(_secretKey);
    final rawString = parts.join('&');

    final sign = md5.convert(utf8.encode(rawString)).toString().toUpperCase();

    return sign;
  }
}
