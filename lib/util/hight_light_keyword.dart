import 'package:flutter/material.dart';

class HighlightText extends StatelessWidget {
  final String text;
  final String keyword; // 比如 "千金离婚" 或 "千金 离婚"
  final TextStyle normalStyle;
  final TextStyle highlightStyle;
  final TextOverflow? overflow;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextDirection? textDirection;
  final TextWidthBasis? textWidthBasis;
  final StrutStyle? strutStyle;
  final int groupSize; // 没有分隔符时的默认拆分长度
  final bool matchWholeAndParts; // 是否同时匹配整段+子词

  const HighlightText({
    super.key,
    required this.text,
    required this.keyword,
    this.normalStyle = const TextStyle(color: Colors.black),
    this.highlightStyle = const TextStyle(color: Colors.red),
    this.overflow,
    this.maxLines,
    this.textAlign,
    this.textDirection,
    this.textWidthBasis,
    this.strutStyle,
    this.groupSize = 2,
    this.matchWholeAndParts = true, // 默认整段+子词一起匹配
  });

  /// 智能拆分关键词
  List<String> _splitKeywords(String keyword) {
    // 1. 按空格/逗号/中文逗号拆分
    final parts =
    keyword.split(RegExp(r'[\s,，]+')).where((k) => k.isNotEmpty).toList();
    if (parts.length > 1) return parts;

    // 2. 没有分隔符 → 按 groupSize 切割
    final single = parts.isNotEmpty ? parts.first : keyword;
    if (single.isEmpty) return [];

    List<String> result = [];
    for (int i = 0; i < single.length; i += groupSize) {
      int end = (i + groupSize > single.length) ? single.length : i + groupSize;
      result.add(single.substring(i, end));
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    if (keyword.trim().isEmpty) {
      return Text(
        text,
        style: normalStyle,
        overflow: overflow,
        maxLines: maxLines,
        textAlign: textAlign,
        textDirection: textDirection,
        textWidthBasis: textWidthBasis,
      );
    }

    // 1. 获取拆分后的关键词
    final splitKeywords = _splitKeywords(keyword);

    // 2. 处理整段+子词模式
    final allKeywords = matchWholeAndParts
        ? {keyword, ...splitKeywords}.toList()
        : splitKeywords;

    if (allKeywords.isEmpty) {
      return Text(text, style: normalStyle);
    }

    // 3. 构建正则
    final regex = RegExp(
      allKeywords.map(RegExp.escape).join('|'),
      caseSensitive: false,
    );

    // 4. 查找匹配
    final matches = regex.allMatches(text);
    if (matches.isEmpty) {
      return Text(text, style: normalStyle);
    }

    // 5. 拼接 TextSpan
    List<TextSpan> spans = [];
    int start = 0;
    for (final match in matches) {
      if (match.start > start) {
        spans.add(TextSpan(
            text: text.substring(start, match.start), style: normalStyle));
      }
      spans.add(TextSpan(
          text: text.substring(match.start, match.end),
          style: highlightStyle));
      start = match.end;
    }
    if (start < text.length) {
      spans.add(TextSpan(text: text.substring(start), style: normalStyle));
    }

    return Text.rich(
      TextSpan(children: spans),
      overflow: overflow,
      maxLines: maxLines,
      textAlign: textAlign,
      textDirection: textDirection,
      textWidthBasis: textWidthBasis,
      strutStyle: strutStyle,
    );
  }
}
