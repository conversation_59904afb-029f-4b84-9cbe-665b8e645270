import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_base/common/biz_values/biz_strings.dart';
import 'package:flutter_common_base/util/util_common_tools/tools_util.dart';
import 'package:flutter_common_base/util/util_common_widget/data_loader.dart';
import 'package:flutter_common_base/util/util_common_widget/widget_pub.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:flutter_common_base/util/util_webview/jsbridge_util.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

const String TITLE = 'title';
const String EXTEND_BODY_BEHIND_APP_BAR = 'extendBodyBehindAppBar';
const String APP_BAR_COLOR = 'appBarColor';
const String SHOW_GO_FORWARD = 'showGoForward';
const String SHOW_BACK_FORWARD = 'showBackForward';
const String SHOW_REFRESH = 'showRefresh';
const String SHOW_CLOSE = 'showClose';
const String SHOW_APP_BAR_RET = 'showAppBarRet';
const String BG_COLOR = 'bgColor';
const String DELAY_OPEN_TIME = "delayTime"; //时间毫秒

// ignore: must_be_immutable
class WebViewPageBase extends StatefulWidget {
  ///页面要显示的标题
  String title;
  final String strUrl;

  ///是否显示appbar
  final bool bShowAppbar;

  ///appbar是否浮在body上面
  ///true:appbar悬浮到body上方，默认透明，滑动后显示appbar的颜色；
  ///false:单独展示，appbar不覆盖body，默认主题色(黑色)
  bool extendBodyBehindAppBar = false;
  ///extendBodyBehindAppBar=true时，是否支持滑动变appbar颜色
  bool isAlphaAppBar = true;

  ///状态栏的颜色
  Color appBarColor;

  ///web背景颜色
  Color backgroundColor;

  ///是否启用系统的返回功能
  bool bEnableSysBack = true;

  ///是否显示刷新按钮
  bool bShowRefreshBtn = false;

  ///是否显示用户自定义的title,
  bool bUserDefineTitle = false;

  ///是否显示关闭按钮
  bool bShowCloseBtn = false;

  ///是否显示返回按钮
  bool bShowAppbarRetBtn = true;

  WebViewPageBase({
    required this.strUrl,
    this.title = '',
    this.bShowAppbar = true,
    this.extendBodyBehindAppBar = false,
    this.appBarColor = Colors.white,
    this.bEnableSysBack = true,
    this.bShowRefreshBtn = false,
    this.bUserDefineTitle = false,
    this.bShowCloseBtn = false,
    this.bShowAppbarRetBtn = true,
    this.backgroundColor = Colors.white,
  }) {

  }

  @override
  WebViewPageBaseState createState() => WebViewPageBaseState();
}

class WebViewPageBaseState<T extends WebViewPageBase> extends State<T> with WidgetsBindingObserver ,CommonWidgetPub {
  WebViewController? webViewCont;
  Completer<WebViewController> controller = Completer<WebViewController>();

  Completer<WebViewController>? webControllerFuture;
  late WebViewController viewController = WebViewController();

  int nDefIndex = 0;
  bool bLoadSucceed = true;
  String strErrInfo = "加载失败";

  int appBarAlphaBg = 0;

  var jkNode = FocusNode();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    webControllerFuture = Completer<WebViewController>();
    // viewController.setUserAgent('${ServiceInit.strDefUA};${BizStrings.h5UserAgent}');
    viewController.setJavaScriptMode(JavaScriptMode.unrestricted);

    ///用户和h5相互调用
    viewController.addJavaScriptChannel(BizStrings.jsBridgeCallName,
        onMessageReceived: (message) {
          JsBridge jsBridge = JsBridgeUtil.parseJson(message.message);
          _executeMethod(jsBridge);
        });

    viewController.setNavigationDelegate(NavigationDelegate(
      onPageFinished: onPageFinished,
      onPageStarted: onPageStart,
      onWebResourceError: resourceLoadError,
    ));

    onWebViewCreated(viewController);

  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    //安卓10以后必现获取焦点后 才能获取剪贴板内容
    if (Platform.isAndroid && StorageUtil.get<int>('sdkInt') >= 29) {
      if (state == AppLifecycleState.resumed) {
        FocusScope.of(context).requestFocus(jkNode);
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.bShowAppbar) {
      return buildMainWidget();
    } else {
      return buildNoDefAppBar();
    }
  }

  ///创建没有标准的appbar只有一个返回按钮的页面
  Widget buildNoDefAppBar() {
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          buildMainWidget(),
          widget.bShowAppbarRetBtn ? leadingWidget(onPress:  onReturnBack) : Container(),
        ],
      ),
    );
  }


  ///创建页面主体，是否要创建appbar由参数决定
  Widget buildMainWidget() {
    return Scaffold(
      //true是body的高度包含appbar，相当于appbar悬浮在body里
      extendBodyBehindAppBar: widget.extendBodyBehindAppBar,
      resizeToAvoidBottomInset: false,
      backgroundColor: widget.backgroundColor,
      appBar: widget.bShowAppbar? customAppbar(strTitleText: widget.title,onLeadingPress: () {Navigator.of(context).pop();}) : null,
      body: WillPopScope(
        onWillPop: onDefBack,
        child: Stack(
          alignment: Alignment.center,
          fit: StackFit.expand,
          children: [
            IndexedStack(
              index: nDefIndex,
              children: [
                LoadingView(color: widget.backgroundColor),
                Container(
                  alignment: Alignment.center,
                  child: webView(),
                ),
                ClassicalErrorView(
                  onReload: onErrRequest,
                  reloadText: "重新加载",
                  errorTip: strErrInfo,
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }

  Widget webView() {
    return WebViewWidget(controller: viewController,);
  }

  ///创建appbar
  Widget buildAppBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(44),
      child: StatefulBuilder(
        builder: (_, stateSetter) {
          Color color = widget.extendBodyBehindAppBar && widget.isAlphaAppBar
              ? widget.appBarColor.withAlpha(appBarAlphaBg)
              : widget.appBarColor;
          return _buildWebPageAppbar(color);
        },
      ),
    );
  }

  Widget _buildWebPageAppbar(Color color) {
    return customAppbar(strTitleText: widget.title, actions: [
      NavigationControls(controller.future,
          bShowRefresh: widget.bShowRefreshBtn,
          bShowCloseBtn: widget.bShowCloseBtn)
    ]);
  }

  ///默认返回行为的返回,比如点击安卓的系统返回键
  Future<bool> onDefBack() {
    if (!widget.bEnableSysBack) {
      return Future.value(false);
    }

    webViewCont?.canGoBack().then((value) {
      if (value) {
        webViewCont?.goBack();
      } else {
        Navigator.of(context).pop();
      }
    });
    return Future.value(false);
  }


  ///点击appbar的返回按钮的行为
  @protected
  void onReturnBack() async {
    if (await webViewCont?.canGoBack() ?? true) {
      webViewCont?.goBack();
    } else {
      Navigator.of(context).pop();
    }
  }

  ///页面开始加载
  void onPageStart(String strURL) {
    debugPrint('Page started loading: $strURL');
  }


  ///页面加载完
  void onPageFinished(String strURl) {
    debugPrint('Page finished loading: $strURl');
    setState(() {
      if (bLoadSucceed) {
        nDefIndex = 1;
      } else {
        nDefIndex = 2;
      }
    });

    if (bLoadSucceed) {
      _getPageTitleAndSet();
    }
  }

  ///获取h5页面的标题作为标题
  ///如果用户指定标题了这个就不执行了
  Future<void> _getPageTitleAndSet() async {
    if (widget.bUserDefineTitle) {
      return;
    }

    Future.delayed(const Duration(milliseconds: 300), () async {
      if (mounted) {
        String strTitle = await viewController.getTitle() ?? "";
        if (strTitle != widget.title) {
          widget.title = strTitle;
          safeMountedUpdate();
        }
      }
    });
  }

  void safeMountedUpdate() {
    if (mounted) {
      setState(() {});
    }
  }

  ///页面创建完成
  void onWebViewCreated(WebViewController webViewController) {
    webControllerFuture?.complete(webViewController);
    viewController.loadRequest(Uri.parse(widget.strUrl));
  }

  ///代理
  FutureOr<NavigationDecision> onNavDelegate(NavigationRequest request) {
    if (Platform.isAndroid) {
      if (request.url.startsWith("https://wx.tenpay.com")) {
        viewController.loadRequest(Uri.parse(request.url), headers: {"Referer": 'H5 Referer'});
        return NavigationDecision.prevent;
      }
      if (request.url.startsWith("weixin://wap/pay")) {
        launch(request.url);
        return NavigationDecision.prevent;
      }
      if (request.url.startsWith("alipays") ||
          request.url.startsWith("alipay://")) {
        launch(request.url);
      }
    } else if (Platform.isIOS) {
      ///ios支付，屏蔽支付结果页面
      Uri uriParse = Uri.parse(request.url);
      String strPath = uriParse.path;
      String strOpen = uriParse.queryParameters["isOpenUrl"] ?? "";
      if (strPath.contains('/success.html') && strOpen == "0") {
        return NavigationDecision.prevent;
      }
      if (request.url.startsWith("weixin://wap/pay") ||
          request.url.startsWith("alipays") ||
          request.url.startsWith("alipay://")) {
        launch(request.url);
      }
    }
    return NavigationDecision.navigate;
  }

  ///对于哪些错误可以路过
  bool proErrType(WebResourceError error) {
    bool bIgnore = false;
    if (Platform.isIOS) {
      bIgnore = true;
      _getPageTitleAndSet();
    } else {
      switch (error.errorType) {
        case WebResourceErrorType.unknown:
        case WebResourceErrorType.unsupportedScheme:
          bIgnore = true;
          break;
        default:
          break;
      }
    }

    return bIgnore;
  }

  ///资源加载错误
  ///此配合和错误类型一起用
  void resourceLoadError(WebResourceError error) {
    // debugPrint("failingUrl:" + error.failingUrl + "  " + error.errorType.toString() + "   " + error.description + " code " + error.errorCode.toString());

    bool bCanIgnore = proErrType(error);
    if (bCanIgnore) {
      return;
    }

    bLoadSucceed = false;
    setState(() {
      nDefIndex = 2;
    });
  }

  ///加载错误后，重试
  void onErrRequest() {
    bLoadSucceed = true;
    setState(() {
      nDefIndex = 0;
    });

    controller.future.then((webControl) {
      webControl.reload();
    });
  }


  ///主执行js的入口函数
  void _executeMethod(JsBridge jsBridge) {
    String strMethod = jsBridge.method;
    if (strMethod.isEmpty) {
      return;
    }

    if (!mounted) {
      return;
    }

    ///若不支持某功能，则会回调给h5
    if (JsBridgeUtil.isBasicSupportFunc(strMethod)) {
      JsBridgeUtil.executeCommonMethod(context, jsBridge, controller);
    } else if (JsBridgeUtil.isBasePageSupportFunc(strMethod)) {
      _exeMethod(jsBridge, context);
    } else {
      otherMethod(strMethod, jsBridge);
    }
  }

  ///子控件复写这个来实现具体方法
  @protected
  void otherMethod(String strMethod, JsBridge jsBridge) {
    String strParam;
    bool bSup = false;
    strParam = '{"data":{"method":"$strMethod","isSupport":"$bSup"}}';
    JsBridgeUtil.exeErrorCallBack(jsBridge, controller, strParam);
  }

  ///具体执行功能
  void _exeMethod(JsBridge jsBridge, BuildContext context) async {
    if (jsBridge.method == CHANGE_APP_BAR) {
      _changeAppBar(jsBridge);
    } else if (jsBridge.method == IS_SUPPORT_FUNC) {
      isSupportFunc(jsBridge);
    } else if (jsBridge.method == HIDE_KEYBOARD) {
      _hideKeyboard(jsBridge, context);
    }
  }

  ///子控件复写这个来方法 来实现支持的方法
  @protected
  void isSupportFunc(JsBridge jsBridge) async {
    String strMethod = jsBridge.data["method"] ?? "";
    bool bSup = JsBridgeUtil.isBasicSupportFunc(strMethod) || JsBridgeUtil.isBasePageSupportFunc(strMethod);

    String strParam = '{"data":{"method":"$strMethod","isSupport":"$bSup"}}';
    JsBridgeUtil.exeJsAsync(jsBridge, controller, strParam);
  }

  ///改变appBar的元素
  void _changeAppBar(JsBridge jsBridge) {
    //页面要显示的标题
    var title = jsBridge.data[TITLE];
    //appbar是否浮在body上面
    //true:appbar悬浮到body上方，默认透明，滑动后显示appbar的颜色；
    //false:单独展示，appbar不覆盖body，默认主题色(黑色)
    var extendBodyBehindAppBar = jsBridge.data[EXTEND_BODY_BEHIND_APP_BAR];
    //状态栏的颜色
    var appBarColor = jsBridge.data[APP_BAR_COLOR];
    //是否启用系统的返回按钮
    var bEnableSysBack = jsBridge.data[ENABLE_SYS_BACK];
    //是否显示刷新按钮
    var bShowRefreshBtn = jsBridge.data[SHOW_REFRESH];
    //是否显示关闭按钮
    var bShowCloseBtn = jsBridge.data[SHOW_CLOSE];
    //是否显示返回按钮
    var bShowAppbarRetBtn = jsBridge.data[SHOW_APP_BAR_RET];
    //web背景颜色
    var backgroundColor = jsBridge.data[BG_COLOR];
    if (title != null) {
      widget.title = title;
    }
    if (appBarColor != null) {
      widget.appBarColor = getColor(appBarColor);
    }
    if (bEnableSysBack != null) {
      widget.bEnableSysBack = bEnableSysBack;
    }
    if (bShowRefreshBtn != null) {
      widget.bShowRefreshBtn = bShowRefreshBtn;
    }
    if (bShowCloseBtn != null) {
      widget.bShowCloseBtn = bShowCloseBtn;
    }
    if (bShowAppbarRetBtn != null) {
      widget.bShowAppbarRetBtn = bShowAppbarRetBtn;
    }
    if (backgroundColor != null) {
      widget.backgroundColor = getColor(backgroundColor);
    }
    if (extendBodyBehindAppBar != null && widget.extendBodyBehindAppBar != extendBodyBehindAppBar) {
      widget.extendBodyBehindAppBar = extendBodyBehindAppBar;
      setState(() {});
    } else {
      safeMountedUpdate();
    }
  }


  Color getColor(appbarColor) {
    int color;
    if (appbarColor is String && appbarColor.startsWith("#")) {
      color = ToolsUtil.getColorHexFromStr(appbarColor);
    } else {
      color = int.parse(appbarColor.toString());
    }
    Color colorBar = Color(color);
    return colorBar;
  }




  ///收起客户端的键盘
  void _hideKeyboard(JsBridge jsBridge, BuildContext context) async {
    SystemChannels.textInput.invokeMethod('TextInput.hide');
  }


}

///appbar的相关功能按钮
class NavigationControls extends StatelessWidget {
  final bool bShowRefresh;
  final bool bShowCloseBtn;

  final Future<WebViewController> _webViewControllerFuture;

  NavigationControls(this._webViewControllerFuture, {this.bShowRefresh = false,this.bShowCloseBtn = false});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<WebViewController>(
      future: _webViewControllerFuture,
      builder:
          (BuildContext context, AsyncSnapshot<WebViewController> snapshot) {
        final bool webViewReady = snapshot.connectionState == ConnectionState.done;
        final WebViewController? controller = snapshot.data;
        return Row(
          children: <Widget>[
            bShowRefresh
                ? IconButton(
                    icon: Image.asset(
                      "assets/images/web_page_refresh.png",
                      height: 30,
                      width: 30,
                    ),
                    onPressed: !webViewReady
                        ? null
                        : () {
                            controller?.reload();
                          },
                  )
                : Container(),
            bShowCloseBtn
                ? IconButton(
                    icon: Image.asset(
                      "assets/images/web_page_close.png",
                      height: 30,
                      width: 30,
                    ),
                    onPressed:(){Navigator.of(context).pop();},
                  )
                : Container(),
          ],
        );
      },
    );
  }
}