import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_common_base/util/util_storage/storage_util.dart';
import 'package:webview_flutter/webview_flutter.dart';
part 'web_method_constant.dart';
class JsBridgeUtil {

  static String _eveJSAudioVideoHead = "var medias = document.getElementsByTagName(";
  static String _eveJsFuncBody = "); for( var i = 0; i < medias.length; i++ ){medias.item(i)";

  static String _tagAudio = "'audio'";
  static String _tagVideo = "'video'";

  static String _actPlay = ".play()}";
  static String _actPause = ".pause()}";


  static String _strEveJsAudioState = _eveJSAudioVideoHead + _tagAudio + _eveJsFuncBody;
  static String _strEveJsVideoState = _eveJSAudioVideoHead + _tagVideo + _eveJsFuncBody;


  ///基础方法，放入jsbridge_util.dart里的方法
  static bool isBasicSupportFunc(String strFunc) {
    return basicMethod.contains(strFunc);
  }
  ///基本页面方法，所以浏览器页面包括的方法，放入web_page_base.dart里的
  static bool isBasePageSupportFunc(String strFunc) {
    return basePageMethods.contains(strFunc);
  }
  ///扩展页面的方法，新添加的方法都在这里
  static bool isCommonSupportFunc(String strFunc, [String? strSrc]) {
    if ((strSrc?.isEmpty ?? true)) {
      return commonMethod.contains(strFunc);
    }
    return commonMethod.contains(strFunc)&&commonSrcs.contains(strSrc);
  }

  /// 将json字符串转化成对象
  static JsBridge parseJson(String jsonStr) {
    JsBridge jsBridgeModel = JsBridge.fromMap(jsonDecode(jsonStr));
    return jsBridgeModel;
  }

  static void extendAllowLists(List<String> allowLists) {
    if (allowLists.isEmpty) {
      return;
    }
    schemeAllowList.addAll(allowLists);
  }

  ///只有在白名单中的才让正确的拉起
  static bool allowSchemeLaunch(String strUrlScheme) {
    if (strUrlScheme.isEmpty) {
      return false;
    }
    int nIndexFind = schemeAllowList.indexWhere((element) => strUrlScheme.startsWith(element));
    return nIndexFind >= 0;
  }

  static void _getDevId(JsBridge jsBridge, Completer<WebViewController> controller) async {
    //获取设备信息
    String strDevId = StorageUtil.get<String>('dev_id', defaultValue: '');
    String strParam = '{"data":{"dev_id":"$strDevId",}}';

    exeJsAsync(jsBridge,controller,strParam);
  }

  static void exeJsAsync (JsBridge jsBridge, Completer<WebViewController> controller,String strParam) {
    String strReq = "${jsBridge.success}($strParam)";
    controller.future.then((webControl) {
      webControl.runJavaScript(strReq);
    });
  }

  static void exeErrorCallBack(JsBridge jsBridge, Completer<WebViewController> controller,String strParam) async {
    String strReq = jsBridge.error + "(" + strParam + ")";
    controller.future.then((webControl) {
      webControl.runJavaScript(strReq);
    });
  }


  static String clipboardText = '';

  static executeCommonMethod(context, JsBridge jsBridge, Completer<WebViewController> controller) async {
    String strMethod = jsBridge.method;
    if (strMethod == GET_DEV_INFO) {
      //获取设备信息
      _getDevId(jsBridge, controller);
    }
  }

  ///控制H5页面的播放状态
  static void pauseOrPlayAllAudioAndVideo(
      bool bPlay, Completer<WebViewController> controller) async {

    String strAudio = "";
    String strVideo = "";
    if (bPlay) {
      strAudio = _strEveJsAudioState + _actPlay;
      strVideo = _strEveJsVideoState + _actPlay;
    } else {
      strAudio = _strEveJsAudioState + _actPause;
      strVideo = _strEveJsVideoState + _actPause;
    }
    controller.future.then((webControl) {
      webControl.runJavaScript(strAudio);
    });

    controller.future.then((webControl) {
      webControl.runJavaScript(strVideo);
    });
  }

  ///APP状态变更的时候通知H5
  static void notifyH5PageAppState(AppLifecycleState state, Completer<WebViewController> controller) async {
    if (controller == null || state == null) {
      return;
    }
    Map<String, dynamic> mapParam = {"state": state.index};
    String strParam = json.encode(mapParam);
    controller.future.then((webControl) {
      webControl.runJavaScript("appH5Interactive($strParam)").catchError((onError) {});
    });
  }
}

class JsBridge {
  JsBridge(this.method, this.data, this.success, this.error);

  String method; // 方法名
  Map data; // 传递数据
  String success; // 执行成功回调
  String error; // 执行失败回调


  /// jsonEncode方法中会调用实体类的这个方法。如果实体类中没有这个方法，会报错。
  Map toJson() {
    Map map = {};
    map["method"] = method;
    map["data"] = data;
    map["callbackFunc"] = success;
    map["error"] = error;
    return map;
  }

  static JsBridge fromMap(Map<String, dynamic> map) {
    JsBridge jsonModel =  JsBridge(map['method'], map['data'], map['callbackFunc'], map['error']);
    return jsonModel;
  }

  @override
  String toString() {
    return "JsBridge: {method: $method, data: $data, callbackFunc: $success, error: $error}";
  }
}


