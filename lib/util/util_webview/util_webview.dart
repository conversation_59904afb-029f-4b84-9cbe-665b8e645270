import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/util/util_webview/common_web_page.dart';

class WebViewUtil {
  static Future<dynamic> jumpToCommonWebPage(BuildContext context, String strUrl,
      {pageName = 'CommonWebPage',
        strTitle = '',
        extendBodyBehindAppBar = false,
        appBarColor = Colors.white,
        bEnableSysBack = true,
        bShowRefreshBtn = false,
        bUserDefineTitle = false,
        bShowCloseBtn = false,
        bShowAppbarRetBtn = true,
        backgroundColor = BizColors.defWebViewAppBarColor}) async {
    return await Navigator.of(context).push(MaterialPageRoute(
      settings: RouteSettings(name: pageName),
      builder: (context) => CommonWebPage(
        strUrl,
        strTitle: strTitle,
        appBarColor: appBarColor,
        bEnableSysBack: bEnableSysBack,
        bUserDefineTitle: bUserDefineTitle,
        backgroundColor: backgroundColor,
        bShowCloseBtn: bShowCloseBtn,
        extendBodyBehindAppBar: extendBodyBehindAppBar,
      ), //参数如果有需要自己补充
    ));
  }
}