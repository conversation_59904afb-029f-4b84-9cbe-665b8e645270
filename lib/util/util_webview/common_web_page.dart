import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_base/common/biz_values/biz_colors.dart';
import 'package:flutter_common_base/util/util_webview/jsbridge_util.dart';
import 'package:flutter_common_base/util/util_webview/web_page_base.dart';

import 'package:webview_flutter/webview_flutter.dart';
const String qiNiuImgSuffix = "https://bodianimgcdn.kuwo.cn/";
typedef OnWebMethodCallback = bool Function(JsBridge jsBridge, BuildContext context, WebViewController webControl, Completer<WebViewController> completeController);

// ignore: must_be_immutablevkjs, must_be_immutable
class CommonWebPage extends WebViewPageBase {

  CommonWebPage(strUrl,
      {strTitle = '',
      bShowAppbar = true,
      extendBodyBehindAppBar = false,
      appBarColor = BizColors.defWebViewAppBarColor,
      bEnableSysBack = true,
      bShowRefreshBtn = false,
      bUserDefineTitle = false,
      bShowCloseBtn = false,
      bShowAppbarRetBtn = true,
      backgroundColor = BizColors.defWebViewAppBarColor})
      : super(
            strUrl: strUrl,
            title: strTitle,
            bShowAppbar: bShowAppbar,
            extendBodyBehindAppBar: extendBodyBehindAppBar,
            appBarColor: appBarColor,
            bEnableSysBack: bEnableSysBack,
            bShowRefreshBtn: bShowRefreshBtn,
            bUserDefineTitle: bUserDefineTitle,
            bShowCloseBtn: bShowCloseBtn,
            bShowAppbarRetBtn: bShowAppbarRetBtn,
            backgroundColor: backgroundColor);

  @override
  WorksDescState createState() {
    return WorksDescState();
  }
}

class WorksDescState extends WebViewPageBaseState<CommonWebPage> {
  int activitiesId = 0;
  bool init = false;

  List<OnWebMethodCallback> registerMethodFunction = [];

  @override
  void dispose() {

    //反注册方法
    registerMethodFunction.clear();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    //注册回调方法
    registerMethodFunction.add(_secretExecuteMethod);

  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
  }

  ///with里实现的方法在这里添加
  @override
  void otherMethod(String strMethod, JsBridge jsBridge) {
    for (var onFunction in registerMethodFunction) {
      bool b = onFunction(jsBridge, context, viewController, controller);
      if (b) return;
    }

    //全部走完-还没有执行，则返回网页错误
    String strParam;
    bool bSup = false;
    strParam = '{"data":{"method":"$strMethod","isSupport":"$bSup"}}';
    JsBridgeUtil.exeErrorCallBack(jsBridge, controller, strParam);
  }

  ///执行本类支持的功能
  bool _secretExecuteMethod(JsBridge jsBridge, BuildContext context, WebViewController webControl, Completer<WebViewController> completeController) {
    String strMethod = jsBridge.method;
    switch (strMethod) {
      case POP_CURRENT_PAGE:
        {
          try {
            Navigator.of(context).pop();
          } catch (e) {}
        }
        break;
      default:
        return false;
    }
    return true;
  }

  ///是否支持某功能（复写父控件）
  void isSupportFunc(JsBridge jsBridge) async {
    String strMethod = jsBridge.data["method"] ?? "";
    //扩展函数
    String strSrc = jsBridge.data["src"] ?? "";

    bool bSup = JsBridgeUtil.isBasicSupportFunc(strMethod) ||
        JsBridgeUtil.isBasePageSupportFunc(strMethod) ||
        JsBridgeUtil.isCommonSupportFunc(strMethod,strSrc);

    String strParam = '{"data":{"method":"$strMethod","src":"$strSrc","isSupport":"$bSup"}}';
    JsBridgeUtil.exeJsAsync(jsBridge, controller, strParam);
  }

}
