part of 'jsbridge_util.dart';

///获取设备信息
const String GET_DEV_INFO = "getDevInfo";
///Toast提示框
const String FLUTTER_TOAST = "FlutterToast";
///让客户端发送日志
const String SEND_LOG = "sendLog";
///获取用户信息
const String GET_USER_INFO = "getUserInfo";
///获取版本号 / 渠道
const String GET_VERSION = "getVersion";
///登陆
const String JUMP_LOGIN_PAGE = "jumpLoginPage";
///判断客户端是否登录
const String IS_LOGIN = "isLogin";
///获取app高度信息
const String GET_APPBAR_INFO = "getAppBarInfo";
///调整客户端播放的状态
const String CHANGE_PLAY_STATE = "changePlayState";
///获取客户端的基础数据
const String GET_APP_CONFIG = "getAppConfig";
///使用本机浏览器打开相应的地址
const String OPEN_BROWSER = "openBrowser";
///注销账号
const String WITHDRAW_ACCOUNT = "withdrawAccount";
///添加到剪切板
const String COPY_TO_CLIPBOARD = "copyToClipboard";
///获取剪切板的内容
const String GET_CLIPBOARD_TEXT = "getClipboardText";
///获取微信是否有效
const String IS_WECHAT_VALID = "isWechatValid";


///改变appBar状态 按键等
const String CHANGE_APP_BAR = "changeAppBar";
///判断是否有指定方法
const String IS_SUPPORT_FUNC = "isSupportFunc";
///收起客户端的键盘
const String HIDE_KEYBOARD = "hideKeyboard";
///跳转到个人主页
const String JUMP_USER_PAGE = "jumpUserPage";
///跳转到排行榜页面
const String JUMP_RANKING_LIST = "jumpRankingList";
///跳转到新的web页面
const String JUMP_TO_INNER_PAGE = "jumpToInnerPage";
///跳转到反馈页面
const String JUMP_TO_FEED_HELP_PAGE = "jumpToFeedHelpPage";
///跳转到一般问题帮助页面
const String JUMP_TO_FAQ_LIST_PAGE = "jumpFaqList";
///跳转到歌单详情页
const String JUMP_TO_SONG_LIST = "jumpToSongList";
///跳转到歌手详情页
const String JUMP_TO_SINGER_PAGE = "jumpSingerDetail";
///跳转到评论详情页
const String JUMP_TO_COMMENT_PAGE = "jumpSongComment";
///跳转到unlink
const String JUMP_TO_UNLINK = "jumpUnlink";
///上传活动资源
const String UPLOAD_ACTIVITIES_SOURCE = "activityMediaUpload";


///分享活动
const String SHARE = "share";
///播放作品
const String PLAY_PROD = "playProd";
///制作作品
const String MAKE_PROD = "makeProd";
///保存图片到本地存储,其中图片信息为Base64处理过的
const String SAVE_IMG = "saveImg";
///跳转表白墙
const String JUMP_TO_WALL_PUBLISH = "jumpToWallPublish";
const String SHARE_IMG = "shareImg";
///跳转到搜索页面
const String SELECT_SONG = "selectSong";
///关闭当前页面
const String POP_CURRENT_PAGE = "popCurrentPage";
///获取举报信息
const String GET_REPORT_DATA = "getReportData";

const String ENABLE_SYS_BACK = 'enableSysBack';

///设置标题栏最右边的自定义按钮
const String SET_TITLE_RIGHT_BUTTON = "setTitleRightButton";

///支付
const String METHOD_PAY = "payStartClient";
///支付完成
const String METHOD_PAY_FINISHED = "payFinished";
///跳转会员单曲页
const String JUMP_TO_LIB_RANK = "jumpToLibRank";

///语音房点歌成功 回调歌曲信息
const String VR_SELECT_SONG_PUBLISH = "voiceroomPublish";

/// 游戏分发双端接口
const List<String> arH5GameMethod = [
  'subscribe_download_application',
  'download_application',
  'download_application_progress',
  'add_calendar_reminder',
  'delete_calendar_reminder'
];

const List<String> iosH5GameMethod = [
  'post_game_log',
  'jump_game_appstore',
];

///设置苹果支付的参数
const String SET_IAP_PARAM = "setIAPParam";


///波点数字专辑--需要支持的方法
///跳转客户端数专已购列表页
const String JUMP_TO_PURCHASED_ALBUM = "jumpToPurchasedAlbum";
const String PAY_FINISHED_ALBUM = "payFinishedAlbum";

///由A-->B,B结束后通知到A
const String CALLBACK_FUNC_NOTICE_PRE = "preEndedNotice";

///一起听上传图片
const String SYNC_LISTEN_SCENE = "syncTogetherScene";

///跳转到生成舞蹈页面
const String JUMP_ANIMATION_PREVIEW_PAGE = "jumpAnimationPreviewPage";

///监听歌曲的播放状态回调
const String SONG_STATUS_CHANGE_NOTIFY_H5 = "getCurrentPlayMusicInfo";

///波点安卓
const String SCHEME_BODIAN_ANDROID = "bodian.kuwo.cn://";

///酷狗
const String SCHEME_KUGOU = "kugou://";
///酷狗ios
const String SCHEME_KUGOU_IOS = "kugouurl://";

///qq音乐
const String SCHEME_QQ_MUSIC = "qqmusic://";

///酷我
const String SCHEME_KUWO = "kwapp://";

///appStore
const String SCHEME_APP_STORE = "itms-appss://";

///k歌
const String SCHEME_KGE = "qmkege://";

///会员权益-通知 ; param: type-1.头像挂件 2.会员评论 3.身份卡片
const String UPDATE_MEMBER_INTEREST = "updateMemberInterest";

///基础方法
const List<String> basicMethod = [
  GET_DEV_INFO,
  FLUTTER_TOAST,
  SEND_LOG,
  GET_USER_INFO,
  GET_VERSION,
  JUMP_LOGIN_PAGE,
  IS_LOGIN,
  GET_APPBAR_INFO,
  CHANGE_PLAY_STATE,
  GET_APP_CONFIG,
  OPEN_BROWSER,
  WITHDRAW_ACCOUNT,
  COPY_TO_CLIPBOARD,
  GET_CLIPBOARD_TEXT,
  IS_WECHAT_VALID,
  ENABLE_SYS_BACK,
];

///通用页面方法
const List<String> basePageMethods = [
  CHANGE_APP_BAR,
  IS_SUPPORT_FUNC,
  HIDE_KEYBOARD,
  JUMP_USER_PAGE,
  JUMP_RANKING_LIST,
  JUMP_TO_INNER_PAGE,
  JUMP_TO_FEED_HELP_PAGE,
  JUMP_TO_SONG_LIST,
  JUMP_TO_FAQ_LIST_PAGE,
  JUMP_TO_SINGER_PAGE,
  JUMP_TO_COMMENT_PAGE,
  JUMP_TO_UNLINK,
  SONG_STATUS_CHANGE_NOTIFY_H5,
  SET_TITLE_RIGHT_BUTTON,
  UPLOAD_ACTIVITIES_SOURCE
];

///扩展方法
const List<String> commonMethod = [
  SHARE,
  PLAY_PROD,
  MAKE_PROD,
  IS_SUPPORT_FUNC,
  SAVE_IMG,
  JUMP_TO_WALL_PUBLISH,
  SHARE_IMG,
  SELECT_SONG,
  POP_CURRENT_PAGE,
  GET_REPORT_DATA,
  METHOD_PAY,
  METHOD_PAY_FINISHED,
  JUMP_TO_LIB_RANK,
  SYNC_LISTEN_SCENE,
  JUMP_ANIMATION_PREVIEW_PAGE,
  ...arH5GameMethod,
  ...iosH5GameMethod,
  SET_IAP_PARAM,
];

///扩展方法-业务
const List<String> commonSrcs = [
];

///白名单
List<String> schemeAllowList = [
  SCHEME_BODIAN_ANDROID,
  SCHEME_KUGOU,
  SCHEME_KUGOU_IOS,
  SCHEME_QQ_MUSIC,
  SCHEME_KUWO,
  SCHEME_APP_STORE,
  SCHEME_KGE,
];
