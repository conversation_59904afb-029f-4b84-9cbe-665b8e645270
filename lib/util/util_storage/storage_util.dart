import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class StorageUtil {
  static late SharedPreferences _instance;

  /// 单例初始化
  static Future<void> init() async {
    try {
      _instance = await SharedPreferences.getInstance();
    } catch (e) {
      throw Exception('SharedPreferences初始化失败: $e');
    }
  }

  /// 泛型存储方法
  static Future<bool> set<T>(String key, T value) {
    if (value is int) {
      return _instance.setInt(key, value);
    } else if (value is double) {
      return _instance.setDouble(key, value);
    } else if (value is String) {
      return _instance.setString(key, value);
    } else if (value is bool) {
      return _instance.setBool(key, value);
    } else if (value is Map || value is List) {
      return _instance.setString(key, jsonEncode(value));
    } else {
      throw Exception('不支持的数据类型: ${T.runtimeType}');
    }
  }

  // 泛型读取方法
  static T get<T>(String key, {T? defaultValue}) {
    dynamic value;
    if (T == int) {
      value = _instance.getInt(key) ?? 0;
    } else if (T == double) {
      value = _instance.getDouble(key) ?? 0.0;
    } else if (T == String) {
      value = _instance.getString(key) ?? "";
    } else if (T == bool) {
      value = _instance.getBool(key) ?? false;
    } else if (T == Map || T == List) {
      String? jsonStr = _instance.getString(key);
      value = jsonStr != null ? jsonDecode(jsonStr) : (T == Map ? {} : []);
    }
    return value ?? defaultValue;
  }

  /// 删除数据
  static Future<bool> remove(String key) => _instance.remove(key);
}
