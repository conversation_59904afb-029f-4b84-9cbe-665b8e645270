import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConfigUtil {
  static ThemeData themeData = ThemeData(
    ///点击的水波纹颜色
    splashColor: Colors.transparent,

    ///选择之后的颜色
    highlightColor: Colors.transparent,

    ///应用程序主要部分（工具栏、标签栏等）的背景颜色
    primaryColor: Colors.white,
    useMaterial3: false,

    ///页面的背景颜色。
    scaffoldBackgroundColor: Colors.white,
    iconButtonTheme: const IconButtonThemeData(),
    appBarTheme: AppBarTheme(
        titleTextStyle: TextStyle(
            color: const Color(0xff292B30),
            fontSize: 16.sp,
            fontWeight: FontWeight.w600),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(),
        centerTitle: true),
    // tabBarTheme: const TabBarTheme(dividerColor: Colors.transparent, splashFactory: NoSplash.splashFactory)
    tabBarTheme: const TabBarThemeData(
        dividerColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory),
  );
}
