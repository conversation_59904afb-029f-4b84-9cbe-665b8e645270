import 'package:flutter_common_base/data_model/tab_manage/with_draw/with_draw_type.dart';
import 'package:flutter_common_base/req_resource/http_interface/api_req_interface.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';

class WithdrawInfoData {

  WithdrawInfoData._internal();
  static final WithdrawInfoData _instance = WithdrawInfoData._internal();
  factory WithdrawInfoData() => _instance;

  /// 提示信息
  List<String> listWithdrawTips = [];
  ///提现方式
  List<WithDrawTypeInfo> listType = [];

  ///获取文案提示
  Future<void> getTipText() async {
    Map<String, dynamic> mapResult = await ApiReqInterface.getWithdrawTipText();
    if (mapResult["code"] != ResponseCodeParse.codeSuccess ||
        mapResult["data"] == null) {
      return;
    }

    ///取提示数据
    List<dynamic> listRet = mapResult["data"]["tips"] ?? [];
    listWithdrawTips= (listRet).cast<String>();
    ///获取提现方式
    List items = mapResult["data"]["methods"] ?? [];
    for (var item in items) {
      WithDrawTypeInfo typeInfo = WithDrawTypeInfo.fromJson(item);
      listType.add(typeInfo);
    }
  }
}
