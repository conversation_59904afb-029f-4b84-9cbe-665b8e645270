import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_common_base/common/biz_values/biz_values.dart';
import 'package:flutter_common_base/data_model/event_bus/event_bus_declare.dart';
import 'package:flutter_common_base/data_model/event_bus/eventbus_common.dart';
import 'package:flutter_common_base/data_model/user_info/login_type_info.dart';
import 'package:flutter_common_base/req_resource/api/api_req_url.dart';
import 'package:flutter_common_base/util/util_dio/dio_util.dart';
import 'package:flutter_common_base/util/util_resp_code/response_code_desc.dart';
import 'package:flutter_common_base/util/util_user_info/user_info_util.dart';

///进度回调
typedef ProgressCallBack = void Function(int count, int total);

typedef CancelTokenProvider = void Function(CancelToken cancelToken);

class ApiReqInterface {
  ///app 启动时调用
  static Future<Map<String, dynamic>> sysStartUp() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.sysStartUp);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///发送验证码
  static Future<Map<String, dynamic>> sendSms(
      {required String strPhone}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(headers: {
        "AppSecret": "A2DD0DC95D6142D484553AF0F1C40427",
        "BusinessSecret": "BEA114E527C80C475A512980B5663947"
      }).request(ApiReqUrl.sendSmsCode, params: {
        "phone": strPhone,
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  static Future<Map<String, dynamic>> sendChangePhoneCode(
      {required String strPhone}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(headers: {
        "AppSecret": "A2DD0DC95D6142D484553AF0F1C40427",
        "BusinessSecret": "9DA2657263F96C557EF4075A6D172F3C"
      }).request(ApiReqUrl.sendChangePhoneSmsCode, params: {
        "phone": strPhone,
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///手机号和验证码登录
  static Future<Map<String, dynamic>> phoneVerifyCodeLogin(
      {required String strPhone, required String strCode}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(headers: {
        "AppSecret": "A2DD0DC95D6142D484553AF0F1C40427",
        "BusinessSecret": "BEA114E527C80C475A512980B5663947"
      }).request(ApiReqUrl.phoneLogin, params: {
        "shortMessage.phone": strPhone,
        "shortMessage.code": strCode,
        "type": 1,
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///三方微信登录
  static Future<Map<String, dynamic>> thirdLoginWx(
      {required String strCode,
      int nLoginType = LoginType.loginTypeThird,
      String strThirdSrc = ThirdLoginTypeSrc.loginSrcWx}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(headers: {
        "AppSecret": "A2DD0DC95D6142D484553AF0F1C40427",
        "BusinessSecret": "BEA114E527C80C475A512980B5663947"
      }).request(ApiReqUrl.phoneLogin, params: {
        "type": nLoginType,
        "authorization.source": strThirdSrc,
        "authorization.code": strCode
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///三方微信登录
  static Future<Map<String, dynamic>> bindThirdLogin(
      {int nSrcType = WithdrawType.typeWx, String strOpenCode = ""}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.bindThird,
          params: {"source": nSrcType, "code": strOpenCode});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///退出登录
  static Future<Map<String, dynamic>> logOut() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.logOut);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///注销账号
  static Future<Map<String, dynamic>> withdrawAccount() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.withdraw);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取用户详情
  static Future<Map<String, dynamic>> getUserInfoDetail() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.userInfo);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///修改昵称
  static Future<Map<String, dynamic>> modifyNickName(String strNick) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil()
          .request(ApiReqUrl.modifyNickName, params: {"nickname": strNick});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///修改简介
  static Future<Map<String, dynamic>> modifyIntro(String strIntro) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil()
          .request(ApiReqUrl.modifyIntro, params: {"description": strIntro});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///修改生日
  static Future<Map<String, dynamic>> modifyBirthday(String strBirthday) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil()
          .request(ApiReqUrl.modifyBirthday, params: {"birthday": strBirthday});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///修改手机号
  static Future<Map<String, dynamic>> modifyPhoneNumber(
      String strPhoneNumber, String strCode) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(headers: {
        "AppSecret": "A2DD0DC95D6142D484553AF0F1C40427",
        "BusinessSecret": "9DA2657263F96C557EF4075A6D172F3C"
      }).request(ApiReqUrl.modifyPhoneNumber,
          params: {"phone": strPhoneNumber, "code": strCode});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///修改性别
  ///1: 男 2: 女
  static Future<Map<String, dynamic>> modifyGender(int nGender) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil()
          .request(ApiReqUrl.modifyGender, params: {"gender": nGender});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///修改地区
  static Future<Map<String, dynamic>> modifyArea(int nAreaId) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil()
          .request(ApiReqUrl.modifyArea, params: {"areaId": nAreaId});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取地区列表
  ///parentId
  // integer
  // 可选
  // 0 查询一级地区列表 ，非0 查询子地区列
  static Future<Map<String, dynamic>> getAreaInfo({int parentId = 0}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlCommon)
          .request(ApiReqUrl.getAreaInfo, params: {"parentId": parentId});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取地区列表
  ///parentId
  // integer
  // 可选
  // 0 查询一级地区列表 ，非0 查询子地区列
  static Future<Map<String, dynamic>> getChildren({int nId = 0}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlCommon)
          .request(ApiReqUrl.getAreaChildren, params: {"id": nId});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取我的的页面的关注或收藏内容
  ///我的页-关注演员列表页 传入参数 model.type=4 , model.businessType=2 返回关注演员列表
  ///我的页-收藏短剧列表页 传入参数 model.type=2 , model.businessType=1 返回收藏短剧列表
  static Future<Map<String, dynamic>> myPageLikeOrActors(
      int nType, int nBusiness,
      {int nPageIndex = 1, int nTotalPerPage = 15}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.getMyFollowActorsOrLike, params: {
        "model.type": nType,
        "model.businessType": nBusiness,
        "pageNum": nPageIndex,
        "pageSize": nTotalPerPage,
        "selectCount": false,
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取历史记录
  static Future<Map<String, dynamic>> getHistories() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
        ApiReqUrl
            .getHistoryInfo, /* params: {
        "model.type": nType,
        "model.businessType": nBusiness,
        "pageNum": nPageIndex,
        "pageSize": nTotalPerPage,
      }*/
      );
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///添加历史记录
  static Future<Map<String, dynamic>> syncHistoryItem(
      int nSourceId, int nVideoIndex, int nPro) async {
    Map<String, dynamic> getVideoIdBySourceResult =
        await getVideoIdBySource(nSourceId);
    int videoId = getVideoIdBySourceResult['data'];

    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.addHistoryItem, params: {
        "id": videoId,
        "episodeIdx": nVideoIndex,
        "progress": nPro,
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    if (mapResult["code"] == 0) {
      commonEventBus.fire(MyHisOrColTabChgEvent(nIndex: 0));
    }
    return mapResult;
  }

  ///获取短剧tab页面下的轮播图
  static Future<Map<String, dynamic>> getVideoCats() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
        ApiReqUrl
            .getVideoCats, /* params: {
        "model.type": nType,
        "model.businessType": nBusiness,
        "pageNum": nPageIndex,
        "pageSize": nTotalPerPage,
      }*/
      );
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取短剧tab页面下的轮播图
  static Future<Map<String, dynamic>> getBannerList() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
        ApiReqUrl
            .getBannerList, /* params: {
        "model.type": nType,
        "model.businessType": nBusiness,
        "pageNum": nPageIndex,
        "pageSize": nTotalPerPage,
      }*/
      );
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取短剧分类下面的剧集
  static Future<Map<String, dynamic>> getVideosByCat(
      int nCatId, int nCurPageIndex,
      {int nTotalPerPage = BizValues.maxSizePerPage,
      int nChannel = 0,
      int nFeedIndex = 1}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;

    Map<String, dynamic> mapParam = {
      "selectCount": false,
      "model.categoryId": nCatId,
      "pageNum": nCurPageIndex,
      "pageSize": nTotalPerPage,
      "feed": nFeedIndex,
    };
    if (nChannel > 0) {
      mapParam["model.channel"] = nChannel;
    }

    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.getVideosByCat, params: mapParam);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///反馈
  static Future<Map<String, dynamic>> submitFeedback(String strContent) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.submitFeedback, params: {
        "content": strContent,
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///提交申诉
  static Future<Map<String, dynamic>> submitAppeal(
      String strContent, String mobile) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
          ApiReqUrl.youthModeAppeal,
          params: {"reason": strContent, "mobile": mobile});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///查询青少年模式开启状态
  static Future<Map<String, dynamic>> youthModeOpenState() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.youthModeIsOpen);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///开启青少年模式
  static Future<Map<String, dynamic>> enableYouthMode(String strPasswd) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.youthModeOpen, params: {"password": strPasswd});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///禁用青少年模式
  static Future<Map<String, dynamic>> disableYouthMode(String strPasswd) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.youthModeClose, params: {"password": strPasswd});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取榜单的内容
  ///1:热榜
  ///2：猜你想搜榜
  static Future<Map<String, dynamic>> getRankingList(int nType,
      {int nPageIndex = 1, int nCntPerPage = 3}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.rankList, params: {
        "type": nType,
        "pageNum": nPageIndex,
        "pageSize": nCntPerPage
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  /// 随机榜单
  static Future<Map<String, dynamic>> getRankingRandom() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.rankRandom);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///搜索内容
  static Future<Map<String, dynamic>> searchContent(String strKeyword,
      {int nCurIndex = 1, int nCntPerPage = 15}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.searchKeywordContent, params: {
        "keywords": strKeyword,
        "pageSize": nCntPerPage,
        "pageNum": nCurIndex
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取底部导航栏数据
  static Future<Map<String, dynamic>> getNavTabInfo() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.getBotTabList);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取底部导航栏数据
  static Future<Map<String, dynamic>> getUpdateVersion() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.getUpdateInfo);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取余额
  static Future<Map<String, dynamic>> getWallet() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.getRestMoney);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///根据源ID获取短剧主键ID
  static Future<Map<String, dynamic>> getVideoIdBySource(int nSourceId) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
          ApiReqUrl.getVideoIdBySource,
          params: {"source": 1, "sourceId": nSourceId});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取交易流水
  static Future<Map<String, dynamic>> getTransition(int nCurIndex,
      {int nCntPerPage = BizValues.maxSizePerPage}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.moneyGoWith, params: {
        "pageNum": nCurIndex,
        "pageSize": nCntPerPage,
        "selectCount": false
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///申请提现
  static Future<Map<String, dynamic>> withdrawApplication(
      int nType, String moneyGet) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.withDrawMoney,
          params: {"method": nType, "amount": moneyGet});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///行为触发(点赞,收藏,分享,关注)
  ///nType 类型(1点赞/2收藏/3分享/4关注)
  ///businessType 业务类型(1视频/2演员)
  ///businessId 业务ID
  static Future<Map<String, dynamic>> actionExeCute(
      int nType, int nBusiness, int businessId) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Map<String, dynamic> getVideoIdBySourceResult =
          await ApiReqInterface.getVideoIdBySource(businessId);
      int videoId = getVideoIdBySourceResult['data'];

      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.actionExecute, params: {
        "type": nType,
        "businessType": nBusiness,
        "businessId": videoId
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///行为触发取消(点赞,收藏,分享,关注)
  ///nType 类型(1点赞/2收藏/3分享/4关注)
  ///businessType 业务类型(1视频/2演员)
  ///businessId 业务ID
  static Future<Map<String, dynamic>> actionECancel(int nType, int businessId,
      {int nBusinessType = 1}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Map<String, dynamic> getVideoIdBySourceResult =
          await ApiReqInterface.getVideoIdBySource(businessId);
      int videoId = getVideoIdBySourceResult['data'];

      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.actionCancel, params: {
        "type": nType,
        "businessType": nBusinessType,
        "businessId": videoId
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///初始化的时候查询一下，把结果赋值过去
  static Future<bool> queryDramaFavStateByParam(int dramaId) async {
    bool bRet = false;
    if (!UserInfoUtil.isLogin()) {
      return bRet;
    }
    Map<String, dynamic> mapResult =
        await ApiReqInterface.queryDataState(2, dramaId);
    if (mapResult["code"] != ResponseCodeParse.codeSuccess) {
      return bRet;
    }

    List fav = mapResult["data"] ?? [];
    if (fav.isEmpty) {
      bRet = false;
    } else {
      bRet = true;
    }
    return bRet;
  }

  ///查询对应的状态
  ///行为触发取消(点赞,收藏,分享,关注)
  ///nType 类型(1点赞/2收藏/3分享/4关注)
  ///businessType 业务类型(1视频/2演员)
  ///businessId 业务ID
  static Future<Map<String, dynamic>> queryDataState(int nType, int businessId,
      {int nBusinessType = 1}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      /// 未登录用户如果查询自己对该短剧产生的行为，会被后端返回强制登录，所以这里需要判断未登录不再发起请求 , created by zhaoliang
      if (!UserInfoUtil.isLogin()) {
        mapResult['code'] = 0;
        return mapResult;
      }

      Map<String, dynamic> getVideoIdBySourceResult =
          await ApiReqInterface.getVideoIdBySource(businessId);
      int videoId = getVideoIdBySourceResult['data'];

      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.queryBehaviorState, params: {
        "model.type": nType,
        "model.businessType": nBusinessType,
        "model.businessId": videoId
      });
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取是低金额限制
  static Future<Map<String, dynamic>> minLimit() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil().request(ApiReqUrl.minLimit);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取福利页面的广告列表
  static Future<Map<String, dynamic>> getFuLiPageAdLists(
      {int nPosition = 1}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
          ApiReqUrl.getFuLiAdLists,
          params: {"model.positionId": nPosition});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取广告全局配置参数
  static Future<Map<String, dynamic>> getAdConfig() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo)
          .request(ApiReqUrl.getAdConfig);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取广告全局配置参数
  static Future<Map<String, dynamic>> getAdClickCnt(
      {int nPositionId = 1}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    mapResult['code'] = -1;
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
          ApiReqUrl.getAdClickCnt,
          params: {"positionId": nPositionId});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  static Future<Map<String, dynamic>> getAdRewardCnt(
      {String strTransId = ""}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
          ApiReqUrl.getAdRewardCnt,
          params: {"flowNumber": strTransId});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///广告收到奖励后向自己的服务端提交
  static Future<Map<String, dynamic>> commitAdReward(
      {int nAdId = 0, String strTransId = "", String strEcpm = ""}) async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlVideo).request(
          ApiReqUrl.adRewardReq,
          params: {"adId": nAdId, "flowNumber": strTransId, "ecpm": strEcpm});
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取提现页面配置的提示文案
  static Future<Map<String, dynamic>> getWithdrawTipText() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlPassport)
          .request(ApiReqUrl.getWithdrawSettings);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }

  ///获取提现页面配置的提示文案
  static Future<Map<String, dynamic>> getThirdLoginType() async {
    Map<String, dynamic> mapResult = <String, dynamic>{'code': -1};
    try {
      Response resp = await DioUtil(baseUrl: ApiReqUrl.baseUrlPassport)
          .request(ApiReqUrl.getThirdLoginType);
      if (resp.statusCode! >= 200 && resp.statusCode! < 300) {
        Map<String, dynamic> mapRespData = json.decode(resp.data);
        BaseResult result = BaseResult.fromMap(mapRespData);
        mapResult = result.toJson();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return mapResult;
  }
}
