/// @description :请求接口管理
class ApiReqUrl {
  ///用户中心服务
  static const String baseUrlPassport = 'https://test-api-passport.meteorsnow.com/';

  ///短剧服务
  static const String baseUrlVideo = 'https://test-api-video.meteorsnow.com';

  ///基础服务
  static const String baseUrlCommon = 'https://test-api-commons.meteorsnow.com';

  static const String baseUrlH5 = "https://h5-dev.daysparty.cn/";

  ///用户协议
  static const String userPolicy = "https://video.meteorsnow.com/views/agreement/user.html";

  ///隐私政策
  static const String userPrivacy = "https://video.meteorsnow.com/views/agreement/privacy.html";

  ///联系我们
  static const String contactUs = "https://video.meteorsnow.com/views/contact-us.html";

  ///个人信息收集清单
  static const String userInfoCollect = "https://video.meteorsnow.com/views/agreement/personal-collection.html";

  ///第三方信息共享清单
  static const String thirdInfoList = "https://video.meteorsnow.com/views/agreement/third-party-share.html";

  ///儿童个人信息保护规则
  static const String childInfoPro = "https://video.meteorsnow.com/views/agreement/children.html";

  ///青少年文明公约
  static const String youngRule = "https://video.meteorsnow.com/views/agreement/youth-civility-pledge.html";


  ///启动时调用的接口
  static const String sysStartUp = "/system/startup";

  ///发送登录手机验证码
  static const String sendSmsCode = "/shortMessageCode/sendLogin";
  static const String sendChangePhoneSmsCode = "/shortMessageCode/sendChangePhone";

  ///手机号登录
  static const String phoneLogin = "login";

  ///登出
  static const String logOut = "logout";

  ///注销账号
  static const String withdraw = "/cancel";

  ///获取当前用户信息
  static const String userInfo = "/user/myself";

  ///修改用户昵称
  static const String modifyNickName = "/user/changeNickname";

  ///修改个人描述
  static const String modifyIntro = "/user/changeDescription";

  ///修改生日
  static const String modifyBirthday = "/user/changeBirthday";

  ///修改手机号
  static const String modifyPhoneNumber = "/user/changePhone";

  ///修改性别
  static const String modifyGender = "/user/changeGender";

  ///修改区地区
  static const String modifyArea = "/user/changeArea";

  ///获取地区列表
  static const String getAreaInfo = "/area/children";

  ///当前地区的链表
  static const String getAreaChildren = "/area/ancestors";

  ///反馈提交
  static const String submitFeedback = "/feedback/submit";
  static const String youthModeAppeal = "/teenager/appeal";

  ///

  ///青少年模式是否开启
  static const String youthModeIsOpen = "/teenager/isEnabled";

  ///启用青少年模式
  static const String youthModeOpen = "/teenager/enable";

  ///关闭青少年模式
  static const String youthModeClose = "/teenager/disable";

  ///青少年模式

  ///获取我的页面的关注演员列表页或收藏短剧列表页
  static const String getMyFollowActorsOrLike = "/behavior/list";

  ///获取我的页面的浏览历史
  static const String getHistoryInfo = "/video/viewHistoryList";

  ///进行观看历史添加
  static const String addHistoryItem = "/video/viewSync";

  ///获取分类 如 都市 古装 情感等
  static const String getVideoCats = "/category/list";

  ///剧场页面的轮播图
  static const String getBannerList = "/banner/list";

  ///剧场分类下面的内容（短剧内容信息缩略图）
  static const String getVideosByCat = "/video/list";

  ///根据源ID获取短剧主键ID
  static const String getVideoIdBySource = "/video/source";

  ///榜单内容（热搜榜 猜你想搜榜0）
  static const String rankList = "/ranking/list";

  ///随机榜单
  static const String rankRandom = "/ranking/random";

  ///根据关键字搜索短剧
  static const String searchKeywordContent = "/search/videos";

  ///动态获取底部导航信息
  static const String getBotTabList = "/tab/list";

  ///获取版本升级信息
  static const String getUpdateInfo = "/version/checkUpdate";

  ///获取余额
  static const String getRestMoney = "/wallet";

  ///交易流水
  static const String moneyGoWith = "/transaction/list";

  ///申请提现
  static const String withDrawMoney = "/withdrawal/initiate";

  ///行为触发触发(点赞,收藏,分享,关注)
  static const String actionExecute = "/behavior/execute";

  ///行为取消(点赞,收藏,分享,关注)
  static const String actionCancel = "/behavior/cancel";

  ///提现最底金额限制
  static const String minLimit = "/withdrawal/limit";

  ///查询状态
  static const String queryBehaviorState = "/behavior/business";

  ///请求福利页面的广告列表
  static const String getFuLiAdLists = "/ad/list";

  ///获取广告配置参数
  static const String getAdConfig = "/csj/settings";

  ///获取广告的点击次数
  static const String getAdClickCnt = "/ad/statistics";
  ///获取点击后的广告价格
  static const String getAdRewardCnt = "/ad/clickedAmount";

  ///给服务端提交
  static const String adRewardReq = "/ad/clicked";
  /// 获取提现配置信息
  static const String getWithdrawSettings = "/withdrawal/settings";

  ///获取三方登录方方式
  static const String getThirdLoginType = "/partner/sources";
  ///绑定三方账号
  static const String bindThird = "/partner/bind";
}
