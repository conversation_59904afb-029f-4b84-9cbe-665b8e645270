PODS:
  - Ads-CN (*******):
    - Ads-CN/BUAdSDK (= *******)
  - Ads-CN/BUAdSDK (*******)
  - Ads-CN/CSJMediation (*******):
    - Ads-CN/BUAdSDK
  - BUAdTestMeasurement (*******):
    - Ads-CN (= *******)
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_adcontent (1.7.5):
    - Ads-CN/BUAdSDK (= *******)
    - Flutter
    - Pangrowth/media (= *******)
    - PangrowthX/ministory (= *******)
    - PangrowthX/shortplay (= *******)
    - TTSDKFramework/Player-SR (= ********-premium)
  - flutter_adspark (1.3.1):
    - Flutter
    - RangersAppLog/ASA
    - RangersAppLog/Core
    - RangersAppLog/DevTools
    - RangersAppLog/Host/CN (= 6.16.9)
    - RangersAppLog/Log
    - RangersAppLog/Picker
    - RangersAppLog/UITracker
    - RangersAppLog/Unique
  - flutter_gromore_ads (3.7.4):
    - Ads-CN (= *******)
    - BUAdTestMeasurement (= *******)
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.5)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - GDTMobSDK (4.15.10)
  - GMGdtAdapter (*********):
    - Ads-CN/CSJMediation (>= *******)
  - GMKsAdapter (********):
    - Ads-CN/CSJMediation (>= *******)
  - KSAdSDK (3.3.69)
  - MMKV (2.0.2):
    - MMKVCore (~> 2.0.2)
  - MMKVCore (2.0.2)
  - OneKit/BaseKit (1.4.2):
    - OneKit/Service
  - OneKit/ByteDanceKit/Foundation (1.4.2):
    - OneKit/Service
  - OneKit/ByteDanceKit/UIKit (1.4.2):
    - OneKit/ByteDanceKit/Foundation
    - OneKit/Service
  - OneKit/Service (1.4.2)
  - package_info_plus (0.4.5):
    - Flutter
  - Pangrowth/media (*******):
    - PangrowthMedia-premium (= *******)
  - PangrowthDJX (*******):
    - PGXToolbox (= *******)
  - PangrowthMedia-premium (*******):
    - RangersAPM/CN (>= 3.10.6)
    - RangersAPM/Core (>= 3.10.6)
    - RangersAPM/Crash (>= 3.10.6)
    - RangersAPM/EventMonitor (>= 3.10.6)
    - RangersAPM/HMD (>= 3.10.6)
    - RangersAPM/Public (>= 3.10.6)
    - RangersAppLog/Core (>= 6.15.1)
    - RangersAppLog/Host/CN (>= 6.15.1)
    - RangersAppLog/Log (>= 6.15.1)
  - PangrowthMiniStory (*******):
    - FMDB/standard
    - MMKV
    - OneKit/BaseKit (>= 1.3.8)
    - OneKit/ByteDanceKit/UIKit (>= 1.3.8)
    - PGXToolbox (= *******)
    - YYCache
    - YYText
  - PangrowthX/ministory (*******):
    - Ads-CN/BUAdSDK (>= *******)
    - PangrowthMiniStory (= *******)
    - PGXToolbox (= *******)
  - PangrowthX/shortplay (*******):
    - Ads-CN/BUAdSDK (>= *******)
    - PangrowthDJX (= *******)
    - PGXToolbox (= *******)
    - TTSDKFramework/Player-SR (>= ********-premium)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PGXToolbox (*******):
    - RangersAPM/CN (>= 3.10.6)
    - RangersAPM/Crash (>= 3.10.6)
    - RangersAPM/EventMonitor (>= 3.10.6)
    - RangersAppLog/Host/CN (>= 6.16.3)
    - RangersAppLog/Log (>= 6.16.3)
  - platform_device_id_plus (0.0.1):
    - Flutter
  - RangersAPM/Above (5.2.7)
  - RangersAPM/CN (5.2.7):
    - RangersAPM/Core
    - RangersAPM/Public
  - RangersAPM/Core (5.2.7):
    - RangersAPM/HMDLite
  - RangersAPM/Crash (5.2.7):
    - RangersAPM/Core
    - RangersAPM/HMD
    - RangersAPM/Public
    - RangersAPM/SessionTracker
    - RangersAPM/Zip
  - RangersAPM/EventMonitor (5.2.7):
    - RangersAPM/Core
    - RangersAPM/HMD
    - RangersAPM/Public
  - RangersAPM/HMD (5.2.7):
    - RangersAPM/Core
    - RangersAPM/Public
  - RangersAPM/HMDLite (5.2.7)
  - RangersAPM/Public (5.2.7):
    - RangersAPM/Above
    - RangersAPM/Core
    - RangersAPM/HMDLite
    - RangersAPM/Zyone
  - RangersAPM/SessionTracker (5.2.7):
    - RangersAPM/Core
    - RangersAPM/HMD
    - RangersAPM/Public
  - RangersAPM/Zip (5.2.7)
  - RangersAPM/Zyone (5.2.7)
  - RangersAppLog/ASA (6.16.9):
    - RangersAppLog/Core
  - RangersAppLog/Core (6.16.9):
    - RangersAppLog/Encryptor/VOLC
  - RangersAppLog/DevTools (6.16.9):
    - RangersAppLog/Core
  - RangersAppLog/Encryptor/VOLC (6.16.9)
  - RangersAppLog/Host/CN (6.16.9):
    - RangersAppLog/Core
  - RangersAppLog/Log (6.16.9):
    - RangersAppLog/Core
  - RangersAppLog/Picker (6.16.9):
    - RangersAppLog/Log
    - RangersAppLog/UITracker
  - RangersAppLog/UITracker (6.16.9):
    - RangersAppLog/Core
  - RangersAppLog/Unique (6.16.9):
    - RangersAppLog/Core
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TTSDKFramework/BMF (********-premium):
    - TTSDKFramework/Core
  - TTSDKFramework/Core (********-premium):
    - RangersAppLog/Core (~> 6.16.2)
    - RangersAppLog/Host/CN (~> 6.16.2)
  - TTSDKFramework/HttpDNS (********-premium)
  - TTSDKFramework/Player (********-premium):
    - TTSDKFramework/Core
    - TTSDKFramework/HttpDNS
    - TTSDKFramework/PlayerCore
    - TTSDKFramework/Reachability
    - TTSDKFramework/TopSignature
    - TTSDKFramework/VCN
    - TTSDKFramework/VolcLog
  - TTSDKFramework/Player-SR (********-premium):
    - TTSDKFramework/BMF
    - TTSDKFramework/Player
  - TTSDKFramework/PlayerCore (********-premium):
    - TTSDKFramework/Core
    - TTSDKFramework/Tools
    - TTSDKFramework/TTFFmpeg
    - TTSDKFramework/VideoProcessor
  - TTSDKFramework/Reachability (********-premium)
  - TTSDKFramework/SSL (********-premium)
  - TTSDKFramework/Tools (********-premium):
    - TTSDKFramework/SSL
  - TTSDKFramework/TopSignature (********-premium)
  - TTSDKFramework/TTFFmpeg (********-premium):
    - TTSDKFramework/Tools
  - TTSDKFramework/VCN (********-premium)
  - TTSDKFramework/VideoProcessor (********-premium)
  - TTSDKFramework/VolcLog (********-premium):
    - TTSDKFramework/SSL
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - WechatOpenSDK-XCFramework (2.0.5)
  - YYCache (1.0.4)
  - YYText (1.0.7)

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_adcontent (from `.symlinks/plugins/flutter_adcontent/ios`)
  - flutter_adspark (from `.symlinks/plugins/flutter_adspark/ios`)
  - flutter_gromore_ads (from `.symlinks/plugins/flutter_gromore_ads/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - GDTMobSDK (= 4.15.10)
  - GMGdtAdapter (= *********)
  - GMKsAdapter (= ********)
  - KSAdSDK (= 3.3.69)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - platform_device_id_plus (from `.symlinks/plugins/platform_device_id_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  https://github.com/volcengine/volcengine-specs.git:
    - OneKit
    - Pangrowth
    - PangrowthDJX
    - PangrowthMedia-premium
    - PangrowthMiniStory
    - PangrowthX
    - PGXToolbox
    - RangersAPM
    - RangersAppLog
    - TTSDKFramework
  https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git:
    - Ads-CN
    - BUAdTestMeasurement
    - FMDB
    - GDTMobSDK
    - GMGdtAdapter
    - GMKsAdapter
    - KSAdSDK
    - MMKV
    - MMKVCore
    - WechatOpenSDK-XCFramework
    - YYCache
    - YYText

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_adcontent:
    :path: ".symlinks/plugins/flutter_adcontent/ios"
  flutter_adspark:
    :path: ".symlinks/plugins/flutter_adspark/ios"
  flutter_gromore_ads:
    :path: ".symlinks/plugins/flutter_gromore_ads/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  platform_device_id_plus:
    :path: ".symlinks/plugins/platform_device_id_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  Ads-CN: 1d65518db43f7da28b47275105d56d74a88a9f1f
  BUAdTestMeasurement: cb7403e5cabda14d99d132f205f718f2beecce10
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_adcontent: ff4accd52c5010a5866a5041b7cc265c65806dae
  flutter_adspark: a3acdbe313413c82e2833f150df7be873eb0885f
  flutter_gromore_ads: 050ae72695fe4502504841bc479b85a13c1d739f
  fluwx: 2ef787502fccb3f3596b380509001a8ea71cbbff
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  GDTMobSDK: 17a7af711cf6b7c8a3a0a4edc3916dc2467877e9
  GMGdtAdapter: a02a0d549df2fcfeb7883c257cd49f0faf202cfd
  GMKsAdapter: c5fdefd50c91e47317578b11f218b65906dc6992
  KSAdSDK: 6567620e4ebe6064b9a1a3c8de111c4842a31160
  MMKV: 3eacda84cd1c4fc95cf848d3ecb69d85ed56006c
  MMKVCore: 508b4d3a8ce031f1b5c8bd235f0517fb3f4c73a9
  OneKit: 05d59eb122e247640c35ceb28b300ae27e89cb88
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  Pangrowth: 0e975cc60cc98f7c140333b6f54a84f10a35c987
  PangrowthDJX: d03144f946d1d152b4f658b7394e11812ad357b1
  PangrowthMedia-premium: a22fd065f2891fcc832b3e4163f6e426c95f28b1
  PangrowthMiniStory: 5e4e42e9efb0f430acc9d1cc00ef2359c72fb06d
  PangrowthX: 98c68b35b16b8fa557ea652e62b38b47f78c966c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PGXToolbox: a734652267f8839a54c1b4c8ab36c7cc98dbbe8a
  platform_device_id_plus: ead043ea948d38cec3e8d2b9aa17cfd0825e2d23
  RangersAPM: 9ea2a2dec79eb4fcb496943983b1d5c2040a1ab5
  RangersAppLog: d4fa640a806e5fef583b07bea2f610684e81161e
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  TTSDKFramework: 3cba00b346c4a9adaa014e26fd081bb8ad9c5314
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  WechatOpenSDK-XCFramework: b072030c9eeee91dfff1856a7846f70f7b9a88ed
  YYCache: 8105b6638f5e849296c71f331ff83891a4942952
  YYText: 5c461d709e24d55a182d1441c41dc639a18a4849

PODFILE CHECKSUM: 479d583002ea43df3411110668124d54550aae6a

COCOAPODS: 1.16.2
