Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter run

## exception

SocketException: SocketException: Send failed (OS Error: No route to host, errno = 65), address = 0.0.0.0, port = 5353

```
#0      _NativeSocket.send (dart:io-patch/socket_patch.dart:1275:34)
#1      _RawDatagramSocket.send (dart:io-patch/socket_patch.dart:2590:15)
#2      MDnsClient.lookup (package:multicast_dns/multicast_dns.dart:219:22)
#3      MDnsVmServiceDiscovery._pollingVmService (package:flutter_tools/src/mdns_discovery.dart:232:66)
<asynchronous suspension>
#4      MDnsVmServiceDiscovery.firstMatchingVmService (package:flutter_tools/src/mdns_discovery.dart:188:56)
<asynchronous suspension>
#5      MDnsVmServiceDiscovery.getVMServiceUriForLaunch (package:flutter_tools/src/mdns_discovery.dart:433:50)
<asynchronous suspension>
#6      Future.any.onValue (dart:async/future.dart:628:5)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.24.5, on macOS 15.6 24G84 darwin-x64, locale zh-Hans-CN)
    • Flutter version 3.24.5 on channel stable at /Users/<USER>/fvm/versions/3.24.5
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision dec2ee5c1f (9 months ago), 2024-11-13 11:13:06 -0800
    • Engine revision a18df97ca5
    • Dart version 3.5.4
    • DevTools version 2.37.3
    • Pub download mirror https://pub.flutter-io.cn
    • Flutter download mirror https://storage.flutter-io.cn

[✗] Android toolchain - develop for Android devices
    ✗ ANDROID_HOME = /Users/<USER>/Documents/android_sdk
      but Android SDK not found at this location.

[✓] Xcode - develop for iOS and macOS (Xcode 16.4)
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Build 16F6
    • CocoaPods version 1.15.2

[✓] Chrome - develop for the web
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[✓] Android Studio (version 2022.3)
    • Android Studio at /Applications/Android Studio.app/Contents
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 17.0.6+0-17.0.6b829.9-10027231)

[✓] VS Code (version 1.102.3)
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.116.0

[✓] Connected device (3 available)
    • SPhone (mobile) • 00008120-00166D863EA2201E • ios            • iOS 18.5 22F76
    • macOS (desktop) • macos                     • darwin-x64     • macOS 15.6 24G84 darwin-x64
    • Chrome (web)    • chrome                    • web-javascript • Google Chrome 137.0.7151.120

[✓] Network resources
    • All expected network resources are available.

! Doctor found issues in 1 category.
```
